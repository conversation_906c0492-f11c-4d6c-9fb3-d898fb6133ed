/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/node/darwin-arm64/node
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/node/LICENSE
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/api.json
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/install_media_pack.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_chrome_beta_linux.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_chrome_beta_mac.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_chrome_beta_win.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_chrome_stable_linux.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_chrome_stable_mac.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_chrome_stable_win.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_beta_linux.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_beta_mac.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_beta_win.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_dev_linux.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_dev_mac.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_dev_win.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_stable_linux.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_stable_mac.sh
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/bin/reinstall_msedge_stable_win.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/browsers.json
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/cli.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/index.d.ts
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/index.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/index.mjs
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/androidServerImpl.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/browserServerImpl.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/cli/driver.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/cli/program.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/cli/programWithTestStub.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/accessibility.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/android.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/api.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/artifact.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/browser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/browserContext.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/browserType.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/cdpSession.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/channelOwner.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/clientHelper.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/clientInstrumentation.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/clientStackTrace.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/clock.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/connection.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/consoleMessage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/coverage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/dialog.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/download.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/electron.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/elementHandle.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/errors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/eventEmitter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/events.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/fetch.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/fileChooser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/fileUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/frame.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/harRouter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/input.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/jsHandle.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/jsonPipe.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/localUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/locator.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/network.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/page.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/platform.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/playwright.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/selectors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/stream.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/timeoutSettings.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/tracing.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/types.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/video.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/waiter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/webError.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/webSocket.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/worker.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/client/writableStream.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/bindingsControllerSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/clockSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/injectedScriptSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/pollingRecorderSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/storageScriptSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/utilityScriptSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/generated/webSocketMockSource.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/inprocess.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/inProcessFactory.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/outofprocess.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/protocol/serializers.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/protocol/validator.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/protocol/validatorPrimitives.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/remote/playwrightConnection.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/remote/playwrightServer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/accessibility.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/android/android.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/android/backendAdb.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/artifact.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiBrowser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiChromium.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiConnection.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiExecutionContext.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiFirefox.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiInput.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiNetworkManager.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiOverCdp.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiPage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/bidiPdf.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiCommands.d.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiDeserializer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiKeyboard.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiProtocol.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiProtocolCore.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiProtocolPermissions.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/bidiSerializer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/bidi/third_party/firefoxPrefs.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/browser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/browserContext.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/browserType.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/callLog.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/appIcon.png
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/chromium.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/chromiumSwitches.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crAccessibility.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crBrowser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crConnection.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crCoverage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crDevTools.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crDragDrop.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crExecutionContext.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crInput.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crNetworkManager.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crPage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crPdf.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crProtocolHelper.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/crServiceWorker.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/defaultFontFamilies.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/protocol.d.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/chromium/videoRecorder.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/clock.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/csharp.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/java.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/javascript.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/jsonl.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/language.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/languages.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/python.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/codegen/types.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/console.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/cookieStore.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/debugController.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/debugger.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/deviceDescriptors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/deviceDescriptorsSource.json
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dialog.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/androidDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/artifactDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/browserContextDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/browserDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/browserTypeDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/cdpSessionDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/debugControllerDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/dialogDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/dispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/electronDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/elementHandlerDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/frameDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/jsHandleDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/jsonPipeDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/localUtilsDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/networkDispatchers.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/pageDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/playwrightDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/streamDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/tracingDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/webSocketRouteDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dispatchers/writableStreamDispatcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/dom.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/download.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/electron/electron.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/electron/loader.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/errors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/fetch.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/fileChooser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/fileUploadUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffAccessibility.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffBrowser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffConnection.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffExecutionContext.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffInput.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffNetworkManager.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/ffPage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/firefox.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/firefox/protocol.d.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/formData.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/frames.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/frameSelectors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/har/harRecorder.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/har/harTracer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/harBackend.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/helper.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/index.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/input.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/instrumentation.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/javascript.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/launchApp.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/localUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/macEditingCommands.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/network.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/page.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/pipeTransport.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/playwright.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/progress.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/protocolError.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder/chat.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder/recorderApp.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder/recorderRunner.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder/recorderSignalProcessor.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder/recorderUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/recorder/throttledFile.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/registry/browserFetcher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/registry/dependencies.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/registry/index.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/registry/nativeDeps.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/registry/oopDownloadBrowserMain.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/screenshotter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/selectors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/socksClientCertificatesInterceptor.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/socksInterceptor.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/trace/recorder/snapshotter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/trace/recorder/snapshotterInjected.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/trace/recorder/tracing.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/trace/test/inMemorySnapshotter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/trace/viewer/traceViewer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/transport.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/types.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/usKeyboardLayout.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/ascii.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/comparators.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/crypto.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/debug.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/debugLogger.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/env.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/eventsHelper.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/expectUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/fileUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/happyEyeballs.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/hostPlatform.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/httpServer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/image_tools/colorUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/image_tools/compare.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/image_tools/imageChannel.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/image_tools/stats.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/linuxUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/network.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/nodePlatform.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/pipeTransport.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/processLauncher.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/profiler.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/socksProxy.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/spawnAsync.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/task.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/userAgent.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/wsServer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/zipFile.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/utils/zones.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/protocol.d.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/webkit.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkAccessibility.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkBrowser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkConnection.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkExecutionContext.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkInput.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkInterceptableRequest.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkPage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkProvisionalPage.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/server/webkit/wkWorkers.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/third_party/pixelmatch.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/ariaSnapshot.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/assert.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/colors.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/cssParser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/cssTokenizer.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/headers.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/locatorGenerators.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/locatorParser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/locatorUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/manualPromise.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/mimeType.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/multimap.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/protocolFormatter.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/protocolMetainfo.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/rtti.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/selectorParser.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/semaphore.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/stackTrace.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/stringUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/time.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/timeoutRunner.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/traceUtils.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/types.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/urlMatch.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utils/isomorphic/utilityScriptSerializers.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utilsBundle.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utilsBundleImpl/index.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/utilsBundleImpl/xdg-open
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/htmlReport/index.html
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/assets/codeMirrorModule-C3UTv-Ge.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/assets/codeMirrorModule-ISMgoUzq.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/assets/codicon-DCmgc-ay.ttf
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/assets/index-Ds0aE8ml.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/assets/index-eHBmevrY.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/index.html
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/recorder/playwright-logo.svg
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/assets/codeMirrorModule-3kZ8GVgK.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/assets/defaultSettingsView-Ds-bson8.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/assets/xtermModule-BoAIEibi.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/codeMirrorModule.C3UTv-Ge.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/codicon.DCmgc-ay.ttf
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/defaultSettingsView.NYBT19Ch.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/index.CFOW-Ezb.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/index.CG3BNeDq.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/index.html
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/playwright-logo.svg
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/snapshot.html
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/sw.bundle.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/uiMode.BatfzHMG.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/uiMode.BijnIl3L.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/uiMode.html
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/vite/traceViewer/xtermModule.Beg8tuEN.css
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/zipBundle.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/lib/zipBundleImpl.js
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/package.json
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/protocol.yml
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/README.md
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/ThirdPartyNotices.txt
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/types/protocol.d.ts
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/types/structs.d.ts
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/.playwright/package/types/types.d.ts
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/playwright.ps1
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/google-list-create
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/google-list-create.deps.json
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/google-list-create.runtimeconfig.json
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/google-list-create.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/google-list-create.pdb
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Bcl.AsyncInterfaces.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.Configuration.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.Configuration.Json.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.FileProviders.Physical.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/RiderProjects/google-list-create/bin/Release/net9.0/Microsoft.Playwright.dll
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.csproj.AssemblyReference.cache
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.AssemblyInfoInputs.cache
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.AssemblyInfo.cs
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.csproj.CoreCompileInputs.cache
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-l.9A70A2B2.Up2Date
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.dll
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/refint/google-list-create.dll
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.pdb
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/google-list-create.genruntimeconfig.cache
/Users/<USER>/RiderProjects/google-list-create/obj/Release/net9.0/ref/google-list-create.dll
