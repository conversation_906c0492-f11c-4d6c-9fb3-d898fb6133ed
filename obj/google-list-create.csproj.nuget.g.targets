﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.json/6.0.10/buildTransitive/netcoreapp3.1/System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json/6.0.10/buildTransitive/netcoreapp3.1/System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.playwright/1.54.0/buildTransitive/Microsoft.Playwright.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.playwright/1.54.0/buildTransitive/Microsoft.Playwright.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder/9.0.8/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder/9.0.8/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets')" />
  </ImportGroup>
</Project>