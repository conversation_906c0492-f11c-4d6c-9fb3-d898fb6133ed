{"format": 1, "restore": {"/Users/<USER>/RiderProjects/google-list-create/google-list-create.csproj": {}}, "projects": {"/Users/<USER>/RiderProjects/google-list-create/google-list-create.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/RiderProjects/google-list-create/google-list-create.csproj", "projectName": "google-list-create", "projectPath": "/Users/<USER>/RiderProjects/google-list-create/google-list-create.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/RiderProjects/google-list-create/obj/", "projectStyle": "PackageReference", "fallbackFolders": ["/usr/local/share/dotnet/sdk/NuGetFallbackFolder"], "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Playwright": {"target": "Package", "version": "[1.54.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}