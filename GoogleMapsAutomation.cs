using Microsoft.Playwright;
using GoogleListCreate.Models;

namespace GoogleListCreate;

public class GoogleMapsAutomation : IDisposable
{
    private IPlaywright? _playwright;
    private IBrowser? _browser;
    private IPage? _page;
    private readonly GoogleMapsSettings _settings;
    private bool _disposed = false;

    public GoogleMapsAutomation(GoogleMapsSettings settings)
    {
        _settings = settings;
    }

    public async Task InitializeAsync()
    {
        _playwright = await Playwright.CreateAsync();

        var launchOptions = new BrowserTypeLaunchOptions
        {
            Headless = _settings.Headless,
            SlowMo = 1000
        };

        // 根據設定選擇瀏覽器
        _browser = _settings.Browser switch
        {
            BrowserChoice.SystemChrome => await LaunchSystemChromeAsync(launchOptions),
            BrowserChoice.PlaywrightChromium => await _playwright.Chromium.LaunchAsync(launchOptions),
            BrowserChoice.PlaywrightFirefox => await _playwright.Firefox.LaunchAsync(launchOptions),
            BrowserChoice.PlaywrightWebkit => await _playwright.Webkit.LaunchAsync(launchOptions),
            _ => await LaunchSystemChromeAsync(launchOptions)
        };

        _page = await _browser.NewPageAsync();
        await _page.SetViewportSizeAsync(1920, 1080);
    }

    private async Task<IBrowser> LaunchSystemChromeAsync(BrowserTypeLaunchOptions options)
    {
        // 嘗試使用系統 Chrome
        var chromePaths = new[]
        {
            _settings.CustomChromePath,
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", // macOS
            "/usr/bin/google-chrome", // Linux
            "/usr/bin/chromium", // Linux Chromium
            @"C:\Program Files\Google\Chrome\Application\chrome.exe", // Windows
            @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" // Windows x86
        };

        foreach (var path in chromePaths)
        {
            if (!string.IsNullOrEmpty(path) && File.Exists(path))
            {
                Console.WriteLine($"使用系統 Chrome: {path}");
                options.ExecutablePath = path;
                return await _playwright.Chromium.LaunchAsync(options);
            }
        }

        // 如果找不到系統 Chrome，回退到 Playwright Chromium
        Console.WriteLine("找不到系統 Chrome，使用 Playwright Chromium");
        return await _playwright.Chromium.LaunchAsync(options);
    }

    public async Task<bool> LoginToGoogleAsync()
    {
        if (_page == null) throw new InvalidOperationException("頁面未初始化");

        try
        {
            await _page.GotoAsync("https://accounts.google.com/signin", new PageGotoOptions
            {
                WaitUntil = WaitUntilState.NetworkIdle
            });

            await _page.FillAsync("input[type=\"email\"]", _settings.Email);
            await _page.ClickAsync("#identifierNext");
            await _page.WaitForTimeoutAsync(2000);

            await _page.FillAsync("input[type=\"password\"]", _settings.Password);
            await _page.ClickAsync("#passwordNext");
            await _page.WaitForTimeoutAsync(3000);

            var isLoggedIn = await _page.WaitForSelectorAsync("[data-ogsr-up]", new PageWaitForSelectorOptions
            {
                Timeout = 10000,
                State = WaitForSelectorState.Visible
            }) != null;

            return isLoggedIn;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"登入失敗: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 在 Google Maps 中建立新的已儲存清單
    /// </summary>
    /// <param name="listInfo">要建立的清單資訊（包含名稱和描述）</param>
    /// <returns>建立成功返回 true，失敗返回 false</returns>
    public async Task<bool> CreateNewSavedListAsync(ListInfo listInfo)
    {
        if (_page == null) throw new InvalidOperationException("頁面未初始化");
        if (listInfo == null) throw new ArgumentNullException(nameof(listInfo));
        if (string.IsNullOrWhiteSpace(listInfo.Name)) throw new ArgumentException("清單名稱不能為空", nameof(listInfo));

        return await CreateNewSavedListAsync(listInfo.Name, listInfo.Description);
    }

    /// <summary>
    /// 在 Google Maps 中建立新的已儲存清單
    /// </summary>
    /// <param name="listName">要建立的清單名稱</param>
    /// <param name="listDescription">要建立的清單描述（選填）</param>
    /// <returns>建立成功返回 true，失敗返回 false</returns>
    public async Task<bool> CreateNewSavedListAsync(string listName, string? listDescription = null)
    {
        if (_page == null) throw new InvalidOperationException("頁面未初始化");
        if (string.IsNullOrWhiteSpace(listName)) throw new ArgumentException("清單名稱不能為空", nameof(listName));

        try
        {
            var displayText = string.IsNullOrWhiteSpace(listDescription)
                ? $"正在建立新的已儲存清單: {listName}"
                : $"正在建立新的已儲存清單: {listName} ({listDescription})";
            Console.WriteLine(displayText);

            // 確保在 Google Maps 頁面
            await _page.GotoAsync("https://maps.google.com", new PageGotoOptions
            {
                WaitUntil = WaitUntilState.DOMContentLoaded
            });

            Console.WriteLine("等待頁面載入完成...");
            await _page.WaitForTimeoutAsync(3000);

            // 第一階段：定位並點擊左側「已儲存」按鈕
            Console.WriteLine("第一階段：尋找並點擊左側「已儲存」按鈕...");

            var savedButtonSelectors = new[]
            {
                // 基於 aria-label 的選擇器（最穩定）
                "button[aria-label*=\"已儲存\"]",
                "button[aria-label*=\"Saved\"]",
                "button[aria-label*=\"儲存\"]",

                // 基於 data-value 的選擇器
                "button[data-value=\"已儲存\"]",
                "button[data-value=\"Saved\"]",

                // 基於文字內容的選擇器
                "button:has-text(\"已儲存\")",
                "button:has-text(\"Saved\")",

                // 基於 jsaction 的選擇器
                "[jsaction*=\"saved\"]",
                "[jsaction*=\"bookmark\"]",

                // 基於圖示的選擇器
                "button[data-testid*=\"saved\"]",
                "button[data-testid*=\"bookmark\"]",

                // 左側邊欄的通用選擇器
                ".left-panel button[role=\"tab\"]",
                ".sidebar button[role=\"tab\"]",
                "[role=\"tablist\"] button",

                // 更廣泛的選擇器
                "button[title*=\"已儲存\"]",
                "button[title*=\"Saved\"]"
            };

            IElementHandle? savedButton = null;
            foreach (var selector in savedButtonSelectors)
            {
                try
                {
                    var buttons = await _page.QuerySelectorAllAsync(selector);
                    Console.WriteLine($"選擇器 '{selector}' 找到 {buttons.Count} 個按鈕");

                    foreach (var button in buttons)
                    {
                        var buttonText = await button.InnerTextAsync();
                        var ariaLabel = await button.GetAttributeAsync("aria-label") ?? "";
                        var title = await button.GetAttributeAsync("title") ?? "";

                        Console.WriteLine($"  - 按鈕文字: '{buttonText}', aria-label: '{ariaLabel}', title: '{title}'");

                        // 檢查是否為「已儲存」相關按鈕
                        if (buttonText.Contains("已儲存") || buttonText.Contains("Saved") ||
                            ariaLabel.Contains("已儲存") || ariaLabel.Contains("Saved") ||
                            title.Contains("已儲存") || title.Contains("Saved"))
                        {
                            savedButton = button;
                            Console.WriteLine($"✓ 找到「已儲存」按鈕！選擇器: {selector}");
                            break;
                        }
                    }

                    if (savedButton != null) break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"選擇器 {selector} 失敗: {ex.Message}");
                }
            }

            if (savedButton == null)
            {
                Console.WriteLine("找不到「已儲存」按鈕，嘗試截圖除錯...");
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-saved-button-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                return false;
            }

            // 點擊「已儲存」按鈕
            await savedButton.ClickAsync();
            Console.WriteLine("已點擊「已儲存」按鈕，等待右側區塊載入...");
            await _page.WaitForTimeoutAsync(_settings.ListCreation.SavedButtonClickDelayMs);

            // 截圖檢查點擊後狀態（如果啟用除錯截圖）
            if (_settings.ListCreation.EnableDebugScreenshots)
            {
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"after-saved-click-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
            }

            // 第二階段：在右側區塊中尋找並點擊「新增清單」按鈕
            Console.WriteLine("第二階段：在右側區塊中尋找「新增清單」按鈕...");

            var newListButtonSelectors = new[]
            {
                // 基於文字內容的選擇器
                "button:has-text(\"新增清單\")",
                "button:has-text(\"Add list\")",
                "button:has-text(\"建立清單\")",
                "button:has-text(\"Create list\")",
                "button:has-text(\"+ 新增清單\")",
                "button:has-text(\"+ Add list\")",

                // 基於 aria-label 的選擇器
                "button[aria-label*=\"新增清單\"]",
                "button[aria-label*=\"Add list\"]",
                "button[aria-label*=\"建立清單\"]",
                "button[aria-label*=\"Create list\"]",
                "button[aria-label*=\"新增\"]",
                "button[aria-label*=\"Add\"]",

                // 基於 data 屬性的選擇器
                "button[data-value*=\"add\"]",
                "button[data-value*=\"create\"]",
                "button[data-value*=\"new\"]",
                "button[data-testid*=\"add\"]",
                "button[data-testid*=\"create\"]",
                "button[data-testid*=\"new\"]",

                // 基於 jsaction 的選擇器
                "[jsaction*=\"create\"]",
                "[jsaction*=\"add\"]",
                "[jsaction*=\"new\"]",

                // 基於圖示的選擇器（+ 符號）
                "button[title*=\"新增\"]",
                "button[title*=\"Add\"]",
                "button:has([data-icon=\"add\"])",
                "button:has([data-icon=\"plus\"])",

                // 右側面板的通用選擇器
                ".right-panel button",
                ".content-panel button",
                ".saved-lists-panel button",
                "[role=\"main\"] button",

                // 更廣泛的選擇器
                "button[class*=\"add\"]",
                "button[class*=\"create\"]",
                "button[class*=\"new\"]"
            };

            IElementHandle? newListButton = null;
            foreach (var selector in newListButtonSelectors)
            {
                try
                {
                    var buttons = await _page.QuerySelectorAllAsync(selector);
                    Console.WriteLine($"選擇器 '{selector}' 找到 {buttons.Count} 個按鈕");

                    foreach (var button in buttons)
                    {
                        var buttonText = await button.InnerTextAsync();
                        var ariaLabel = await button.GetAttributeAsync("aria-label") ?? "";
                        var title = await button.GetAttributeAsync("title") ?? "";
                        var isVisible = await button.IsVisibleAsync();
                        var isEnabled = await button.IsEnabledAsync();

                        Console.WriteLine($"  - 按鈕文字: '{buttonText}', aria-label: '{ariaLabel}', title: '{title}', 可見: {isVisible}, 可用: {isEnabled}");

                        // 檢查是否為「新增清單」相關按鈕
                        if (isVisible && isEnabled && (
                            buttonText.Contains("新增清單") || buttonText.Contains("Add list") ||
                            buttonText.Contains("建立清單") || buttonText.Contains("Create list") ||
                            buttonText.Contains("+ 新增") || buttonText.Contains("+ Add") ||
                            ariaLabel.Contains("新增清單") || ariaLabel.Contains("Add list") ||
                            ariaLabel.Contains("建立清單") || ariaLabel.Contains("Create list") ||
                            title.Contains("新增清單") || title.Contains("Add list") ||
                            title.Contains("建立清單") || title.Contains("Create list")))
                        {
                            newListButton = button;
                            Console.WriteLine($"✓ 找到「新增清單」按鈕！選擇器: {selector}");
                            break;
                        }
                    }

                    if (newListButton != null) break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"選擇器 {selector} 失敗: {ex.Message}");
                }
            }

            if (newListButton == null)
            {
                Console.WriteLine("找不到「新增清單」按鈕，嘗試截圖除錯...");
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-new-list-button-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                return false;
            }

            // 點擊「新增清單」按鈕
            await newListButton.ClickAsync();
            Console.WriteLine("已點擊「新增清單」按鈕，等待輸入對話框出現...");
            await _page.WaitForTimeoutAsync(_settings.ListCreation.NewListButtonClickDelayMs);

            // 截圖檢查點擊後狀態（如果啟用除錯截圖）
            if (_settings.ListCreation.EnableDebugScreenshots)
            {
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"after-new-list-click-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
            }

            // 第三階段：輸入清單名稱
            Console.WriteLine("第三階段：輸入清單名稱...");

            var inputSelectors = new[]
            {
                // 基於 placeholder 的選擇器
                "input[placeholder*=\"清單名稱\"]",
                "input[placeholder*=\"List name\"]",
                "input[placeholder*=\"名稱\"]",
                "input[placeholder*=\"Name\"]",
                "textarea[placeholder*=\"清單名稱\"]",
                "textarea[placeholder*=\"List name\"]",

                // 基於 aria-label 的選擇器
                "input[aria-label*=\"清單名稱\"]",
                "input[aria-label*=\"List name\"]",
                "input[aria-label*=\"名稱\"]",
                "input[aria-label*=\"Name\"]",

                // 基於 data 屬性的選擇器
                "input[data-testid*=\"name\"]",
                "input[data-testid*=\"title\"]",
                "input[data-testid*=\"list\"]",

                // 對話框中的輸入框
                "[role=\"dialog\"] input[type=\"text\"]",
                "[role=\"dialog\"] textarea",
                ".modal input[type=\"text\"]",
                ".modal textarea",
                ".dialog input[type=\"text\"]",
                ".dialog textarea",

                // 通用輸入框選擇器
                "input[type=\"text\"]:visible",
                "textarea:visible",
                "input:not([type]):visible"
            };

            IElementHandle? listNameInput = null;
            foreach (var selector in inputSelectors)
            {
                try
                {
                    var inputs = await _page.QuerySelectorAllAsync(selector);
                    Console.WriteLine($"選擇器 '{selector}' 找到 {inputs.Count} 個輸入框");

                    foreach (var input in inputs)
                    {
                        var isVisible = await input.IsVisibleAsync();
                        var isEnabled = await input.IsEnabledAsync();
                        var placeholder = await input.GetAttributeAsync("placeholder") ?? "";
                        var ariaLabel = await input.GetAttributeAsync("aria-label") ?? "";

                        Console.WriteLine($"  - placeholder: '{placeholder}', aria-label: '{ariaLabel}', 可見: {isVisible}, 可用: {isEnabled}");

                        if (isVisible && isEnabled)
                        {
                            listNameInput = input;
                            Console.WriteLine($"✓ 找到可用的輸入框！選擇器: {selector}");
                            break;
                        }
                    }

                    if (listNameInput != null) break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"選擇器 {selector} 失敗: {ex.Message}");
                }
            }

            if (listNameInput == null)
            {
                Console.WriteLine("找不到清單名稱輸入框，嘗試截圖除錯...");
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-input-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                return false;
            }

            // 輸入清單名稱（使用多種方式確保成功）
            Console.WriteLine($"開始輸入清單名稱: {listName}");
            bool inputSuccess = await TryInputTextAsync(listNameInput, listName);

            if (!inputSuccess)
            {
                Console.WriteLine("輸入清單名稱失敗");
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"input-failed-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                return false;
            }

            // 如果有描述，嘗試尋找並填入描述欄位
            if (!string.IsNullOrWhiteSpace(listDescription))
            {
                Console.WriteLine($"嘗試輸入清單描述: {listDescription}");
                await TryInputDescriptionAsync(listDescription);
            }

            // 第四階段：點擊確認按鈕建立清單
            Console.WriteLine("第四階段：尋找並點擊確認按鈕...");

            var confirmButtonSelectors = new[]
            {
                // 基於文字內容的選擇器
                "button:has-text(\"建立\")",
                "button:has-text(\"Create\")",
                "button:has-text(\"確認\")",
                "button:has-text(\"Confirm\")",
                "button:has-text(\"儲存\")",
                "button:has-text(\"Save\")",
                "button:has-text(\"完成\")",
                "button:has-text(\"Done\")",

                // 基於 aria-label 的選擇器
                "button[aria-label*=\"建立\"]",
                "button[aria-label*=\"Create\"]",
                "button[aria-label*=\"確認\"]",
                "button[aria-label*=\"Confirm\"]",
                "button[aria-label*=\"儲存\"]",
                "button[aria-label*=\"Save\"]",

                // 基於 data 屬性的選擇器
                "button[data-value=\"create\"]",
                "button[data-value=\"confirm\"]",
                "button[data-value=\"save\"]",
                "button[data-testid*=\"create\"]",
                "button[data-testid*=\"confirm\"]",
                "button[data-testid*=\"save\"]",

                // 基於 jsaction 的選擇器
                "button[jsaction*=\"confirm\"]",
                "button[jsaction*=\"save\"]",
                "button[jsaction*=\"create\"]",

                // 對話框中的按鈕
                "[role=\"dialog\"] button[type=\"submit\"]",
                "[role=\"dialog\"] button:last-child",
                ".modal button[type=\"submit\"]",
                ".modal button:last-child",
                ".dialog button[type=\"submit\"]",
                ".dialog button:last-child",

                // 通用確認按鈕
                "button[type=\"submit\"]",
                "button.primary",
                "button.confirm"
            };

            IElementHandle? confirmButton = null;
            foreach (var selector in confirmButtonSelectors)
            {
                try
                {
                    var buttons = await _page.QuerySelectorAllAsync(selector);
                    Console.WriteLine($"選擇器 '{selector}' 找到 {buttons.Count} 個按鈕");

                    foreach (var button in buttons)
                    {
                        var buttonText = await button.InnerTextAsync();
                        var ariaLabel = await button.GetAttributeAsync("aria-label") ?? "";
                        var isVisible = await button.IsVisibleAsync();
                        var isEnabled = await button.IsEnabledAsync();

                        Console.WriteLine($"  - 按鈕文字: '{buttonText}', aria-label: '{ariaLabel}', 可見: {isVisible}, 可用: {isEnabled}");

                        if (isVisible && (
                            buttonText.Contains("建立") || buttonText.Contains("Create") ||
                            buttonText.Contains("確認") || buttonText.Contains("Confirm") ||
                            buttonText.Contains("儲存") || buttonText.Contains("Save") ||
                            buttonText.Contains("完成") || buttonText.Contains("Done") ||
                            ariaLabel.Contains("建立") || ariaLabel.Contains("Create") ||
                            ariaLabel.Contains("確認") || ariaLabel.Contains("Confirm") ||
                            ariaLabel.Contains("儲存") || ariaLabel.Contains("Save")))
                        {
                            confirmButton = button;
                            Console.WriteLine($"✓ 找到確認按鈕！選擇器: {selector}");
                            break;
                        }
                    }

                    if (confirmButton != null) break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"選擇器 {selector} 失敗: {ex.Message}");
                }
            }

            if (confirmButton == null)
            {
                Console.WriteLine("找不到確認按鈕，嘗試截圖除錯...");
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-confirm-button-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                return false;
            }

            // 等待按鈕變為可用
            try
            {
                await confirmButton.WaitForElementStateAsync(ElementState.Enabled, new() { Timeout = 5000 });
                Console.WriteLine("確認按鈕已啟用");
            }
            catch
            {
                Console.WriteLine("確認按鈕仍未啟用，嘗試強制點擊...");
            }

            // 點擊確認按鈕
            await confirmButton.ClickAsync();
            Console.WriteLine($"✓ 已成功建立新清單: {listName}");

            // 等待操作完成
            await _page.WaitForTimeoutAsync(2000);

            // 最終截圖確認
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"list-created-{DateTime.Now:yyyyMMdd-HHmmss}.png" });

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"建立新清單失敗: {ex.Message}");
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"create-list-error-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
            return false;
        }
    }

    /// <summary>
    /// 嘗試使用多種方式輸入文字到指定元素
    /// </summary>
    /// <param name="element">目標輸入元素</param>
    /// <param name="text">要輸入的文字</param>
    /// <returns>輸入成功返回 true，失敗返回 false</returns>
    private async Task<bool> TryInputTextAsync(IElementHandle element, string text)
    {
        // 方式一：使用 JavaScript 直接設定值
        try
        {
            Console.WriteLine("嘗試使用 JavaScript 直接設定值...");
            await _page.EvaluateAsync(@"(args) => {
                const input = args.element;
                const value = args.value;

                // 清空並設定新值
                input.value = '';
                input.value = value;

                // 觸發各種事件以確保 Google Maps 識別到輸入
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
                input.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));

                // 確保輸入框獲得焦點
                input.focus();

                return input.value;
            }", new { element, value = text });

            await _page.WaitForTimeoutAsync(500);
            var jsValue = await element.InputValueAsync();
            Console.WriteLine($"JavaScript 設定後的值: '{jsValue}'");

            if (jsValue == text)
            {
                Console.WriteLine("✓ JavaScript 輸入成功");
                return true;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"JavaScript 輸入失敗: {ex.Message}");
        }

        // 方式二：強制點擊並使用鍵盤輸入
        try
        {
            Console.WriteLine("嘗試強制點擊並使用鍵盤輸入...");

            // 強制點擊輸入框
            await element.ClickAsync(new ElementHandleClickOptions { Force = true });
            await _page.WaitForTimeoutAsync(200);

            // 清空內容
            await _page.Keyboard.PressAsync("Control+a");
            await _page.WaitForTimeoutAsync(100);
            await _page.Keyboard.PressAsync("Delete");
            await _page.WaitForTimeoutAsync(100);

            // 逐字符輸入（模擬真人打字）
            foreach (char c in text)
            {
                await _page.Keyboard.TypeAsync(c.ToString());
                await _page.WaitForTimeoutAsync(_settings.ListCreation.TypingDelayMs); // 使用配置的字符間隔時間
            }

            await _page.WaitForTimeoutAsync(500);
            var keyboardValue = await element.InputValueAsync();
            Console.WriteLine($"鍵盤輸入後的值: '{keyboardValue}'");

            if (keyboardValue == text)
            {
                Console.WriteLine("✓ 鍵盤輸入成功");
                return true;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"鍵盤輸入失敗: {ex.Message}");
        }

        // 方式三：使用 Fill 方法（最後嘗試）
        try
        {
            Console.WriteLine("嘗試使用 Fill 方法...");
            await element.FocusAsync();
            await _page.WaitForTimeoutAsync(200);
            await element.FillAsync(text);
            await _page.WaitForTimeoutAsync(500);

            var fillValue = await element.InputValueAsync();
            Console.WriteLine($"Fill 方法後的值: '{fillValue}'");

            if (fillValue == text)
            {
                Console.WriteLine("✓ Fill 方法輸入成功");
                return true;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fill 方法失敗: {ex.Message}");
        }

        // 最終驗證
        var finalValue = await element.InputValueAsync();
        Console.WriteLine($"最終輸入值: '{finalValue}' (預期: '{text}')");

        return finalValue == text;
    }

    /// <summary>
    /// 嘗試尋找並輸入清單描述
    /// </summary>
    /// <param name="description">要輸入的描述</param>
    /// <returns>輸入成功返回 true，失敗返回 false</returns>
    private async Task<bool> TryInputDescriptionAsync(string description)
    {
        // 尋找描述輸入框的選擇器
        var descriptionSelectors = new[]
        {
            // 基於 placeholder 的選擇器
            "textarea[placeholder*=\"描述\"]",
            "textarea[placeholder*=\"Description\"]",
            "input[placeholder*=\"描述\"]",
            "input[placeholder*=\"Description\"]",
            "textarea[placeholder*=\"說明\"]",
            "textarea[placeholder*=\"備註\"]",

            // 基於 aria-label 的選擇器
            "textarea[aria-label*=\"描述\"]",
            "textarea[aria-label*=\"Description\"]",
            "input[aria-label*=\"描述\"]",
            "input[aria-label*=\"Description\"]",

            // 基於 data 屬性的選擇器
            "textarea[data-testid*=\"description\"]",
            "input[data-testid*=\"description\"]",
            "textarea[data-testid*=\"desc\"]",
            "input[data-testid*=\"desc\"]",

            // 對話框中的第二個輸入框（通常名稱是第一個，描述是第二個）
            "[role=\"dialog\"] textarea:nth-of-type(2)",
            "[role=\"dialog\"] input[type=\"text\"]:nth-of-type(2)",
            ".modal textarea:nth-of-type(2)",
            ".modal input[type=\"text\"]:nth-of-type(2)",

            // 通用 textarea 選擇器（排除第一個，因為第一個通常是名稱）
            "textarea:not(:first-of-type):visible",
            "textarea:nth-of-type(n+2):visible"
        };

        IElementHandle? descriptionInput = null;
        foreach (var selector in descriptionSelectors)
        {
            try
            {
                var inputs = await _page.QuerySelectorAllAsync(selector);
                Console.WriteLine($"描述選擇器 '{selector}' 找到 {inputs.Count} 個輸入框");

                foreach (var input in inputs)
                {
                    var isVisible = await input.IsVisibleAsync();
                    var isEnabled = await input.IsEnabledAsync();
                    var placeholder = await input.GetAttributeAsync("placeholder") ?? "";
                    var ariaLabel = await input.GetAttributeAsync("aria-label") ?? "";

                    Console.WriteLine($"  - placeholder: '{placeholder}', aria-label: '{ariaLabel}', 可見: {isVisible}, 可用: {isEnabled}");

                    if (isVisible && isEnabled)
                    {
                        descriptionInput = input;
                        Console.WriteLine($"✓ 找到可用的描述輸入框！選擇器: {selector}");
                        break;
                    }
                }

                if (descriptionInput != null) break;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"描述選擇器 {selector} 失敗: {ex.Message}");
            }
        }

        if (descriptionInput == null)
        {
            Console.WriteLine("⚠️ 找不到描述輸入框，可能此版本的 Google Maps 不支援清單描述，或描述欄位是選填的");
            return false;
        }

        // 輸入描述
        Console.WriteLine($"開始輸入清單描述: {description}");
        bool inputSuccess = await TryInputTextAsync(descriptionInput, description);

        if (inputSuccess)
        {
            Console.WriteLine("✓ 清單描述輸入成功");
        }
        else
        {
            Console.WriteLine("⚠️ 清單描述輸入失敗，但不影響清單建立");
        }

        return inputSuccess;
    }

    public async Task<bool> AddPlaceToListAsync(PlaceInfo place)
    {
        if (_page == null) throw new InvalidOperationException("頁面未初始化");

        try
        {
            Console.WriteLine($"正在導航到 Google Maps...");
            await _page.GotoAsync("https://maps.google.com", new PageGotoOptions
            {
                WaitUntil = WaitUntilState.DOMContentLoaded
            });

            Console.WriteLine($"等待搜尋框出現...");
            await _page.WaitForSelectorAsync("#searchboxinput", new PageWaitForSelectorOptions
            {
                Timeout = _settings.TimeoutSeconds * 1000
            });

            Console.WriteLine($"搜尋框已找到，開始搜尋: {place.Name}");

            await _page.FillAsync("#searchboxinput", place.Name);
            await _page.PressAsync("#searchboxinput", "Enter");

            await _page.WaitForTimeoutAsync(3000);

            var saveButton = await _page.WaitForSelectorAsync("[data-value=\"儲存\"], [data-value=\"Save\"], button[aria-label*=\"儲存\"], button[aria-label*=\"Save\"]",
                new PageWaitForSelectorOptions
                {
                    Timeout = _settings.TimeoutSeconds * 1000
                });

            if (saveButton == null)
            {
                Console.WriteLine($"找不到儲存按鈕，正在嘗試其他選擇器...");

                var possibleSaveButtons = new[]
                {
                    "[jsaction*=\"save\"]",
                    "button[data-value=\"Save\"]",
                    "button[data-value=\"儲存\"]",
                    ".widget-pane-link[data-value=\"Save\"]",
                    "[aria-label*=\"Save\"]",
                    "[aria-label*=\"儲存\"]"
                };

                foreach (var selector in possibleSaveButtons)
                {
                    try
                    {
                        saveButton = await _page.QuerySelectorAsync(selector);
                        if (saveButton != null)
                        {
                            Console.WriteLine($"找到儲存按鈕: {selector}");
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"嘗試選擇器 {selector} 失敗: {ex.Message}");
                    }
                }
            }

            if (saveButton == null)
            {
                Console.WriteLine("無法找到儲存按鈕");
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"error-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                return false;
            }

            await saveButton.ClickAsync();
            Console.WriteLine("已點擊儲存按鈕，等待清單選單出現...");
            await _page.WaitForTimeoutAsync(3000);

            // 截圖看看點擊後的狀態
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"after-save-click-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
            Console.WriteLine("已截圖，檢查點擊後狀態");

            // 多種可能的清單選擇器
            var listSelectors = new[]
            {
                "[data-index][role=\"menuitemradio\"]",
                "[role=\"menuitem\"]",
                "div[data-value]",
                ".lists-menu-item",
                "[jsaction*=\"select\"]",
                "[role=\"menu\"] [role=\"menuitem\"]",
                ".save-menu [role=\"menuitem\"]"
            };

            var existingLists = new List<IElementHandle>();
            foreach (var selector in listSelectors)
            {
                var lists = await _page.QuerySelectorAllAsync(selector);
                Console.WriteLine($"嘗試選擇器 '{selector}': 找到 {lists.Count} 個元素");
                if (lists.Count > 0)
                {
                    Console.WriteLine($"✓ 使用選擇器 '{selector}' 找到 {lists.Count} 個清單項目");
                    existingLists.AddRange(lists);
                    break;
                }
            }

            if (existingLists.Count == 0)
            {
                Console.WriteLine("⚠️ 沒有找到任何清單項目，檢查頁面結構...");

                // 嘗試找到任何可能的選單或彈出視窗
                var menuElements = await _page.QuerySelectorAllAsync("[role=\"menu\"], .menu, [data-role=\"menu\"], .dropdown-menu, .popup");
                Console.WriteLine($"找到 {menuElements.Count} 個選單元素");

                // 檢查是否有其他可能的清單容器
                var containers = await _page.QuerySelectorAllAsync("div[jsaction], div[data-action], .save-dialog, .lists-container");
                Console.WriteLine($"找到 {containers.Count} 個可能的容器元素");
            }

            IElementHandle? targetList = null;
            IElementHandle? newListButtonFromMenu = null;

            if (existingLists.Count > 0)
            {
                Console.WriteLine($"檢查現有清單是否包含: {place.ListName}");

                foreach (var list in existingLists)
                {
                    try
                    {
                        var listText = await list.InnerTextAsync();
                        var trimmedText = listText.Trim();
                        Console.WriteLine($"  - 找到清單: {trimmedText}");

                        if (trimmedText.Contains(place.ListName))
                        {
                            targetList = list;
                            Console.WriteLine($"  ✓ 匹配！將使用現有清單: {place.ListName}");
                            break;
                        }

                        // 檢查是否是「新增清單」按鈕
                        if (trimmedText.Contains("新增清單") || trimmedText.Contains("Add list") ||
                            trimmedText.Contains("Create new list") || trimmedText.Contains("建立清單"))
                        {
                            newListButtonFromMenu = list;
                            Console.WriteLine($"  ✓ 找到「新增清單」按鈕");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  - 無法讀取清單文字: {ex.Message}");
                    }
                }
            }

            if (targetList != null)
            {
                await targetList.ClickAsync();
                Console.WriteLine($"✓ 已新增 {place.Name} 到現有清單: {place.ListName}");
            }
            else
            {
                Console.WriteLine($"找不到現有清單 '{place.ListName}'，嘗試建立新清單...");

                IElementHandle? newListButton = newListButtonFromMenu;

                // 如果從選單中沒找到，嘗試其他選擇器
                if (newListButton == null)
                {
                    var newListSelectors = new[]
                    {
                        "button[jsaction*=\"create\"]",
                        "[aria-label*=\"建立新清單\"]",
                        "[aria-label*=\"Create new list\"]",
                        "[data-value=\"new_list\"]",
                        "div[role=\"menuitem\"]:last-child",
                        "button:has-text('建立新清單')",
                        "button:has-text('Create new list')"
                    };

                    foreach (var selector in newListSelectors)
                    {
                        try
                        {
                            newListButton = await _page.QuerySelectorAsync(selector);
                            if (newListButton != null)
                            {
                                Console.WriteLine($"找到新建清單按鈕（選擇器: {selector}）");
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"嘗試選擇器失敗 {selector}: {ex.Message}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("將使用選單中的「新增清單」按鈕");
                }

                if (newListButton != null)
                {
                    await newListButton.ClickAsync();
                    Console.WriteLine("已點擊建立新清單按鈕，等待輸入框出現...");
                    await _page.WaitForTimeoutAsync(2000);

                    // 多種可能的輸入框選擇器
                    var inputSelectors = new[]
                    {
                        "input[placeholder=\"清單名稱\"]",
                        "input[placeholder*=\"清單名稱\"]",
                        "input[placeholder=\"List name\"]",
                        "input[placeholder*=\"List name\"]",
                        "input[placeholder*=\"名稱\"]",
                        "input[placeholder*=\"Name\"]",
                        "textarea[placeholder=\"清單名稱\"]",
                        "textarea[placeholder*=\"清單名稱\"]",
                        "input[type=\"text\"]",
                        "textarea",
                        ".new-list-dialog input",
                        ".new-list-dialog textarea"
                    };

                    IElementHandle? listNameInput = null;
                    foreach (var selector in inputSelectors)
                    {
                        try
                        {
                            listNameInput = await _page.QuerySelectorAsync(selector);
                            if (listNameInput != null)
                            {
                                // 檢查元素是否可見和可用
                                var isVisible = await listNameInput.IsVisibleAsync();
                                var isEnabled = await listNameInput.IsEnabledAsync();
                                Console.WriteLine($"找到輸入框（選擇器: {selector}）- 可見: {isVisible}, 可用: {isEnabled}");

                                if (isVisible && isEnabled)
                                {
                                    break;
                                }
                                listNameInput = null;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"測試選擇器 {selector} 失敗: {ex.Message}");
                        }
                    }

                    if (listNameInput != null)
                    {
                        Console.WriteLine("開始輸入清單名稱...");

                        // 驗證輸入框初始狀態
                        var initialValue = await listNameInput.InputValueAsync();
                        Console.WriteLine($"輸入框初始值: '{initialValue}'");

                        bool inputSuccess = false;

                        // 方式一：使用 JavaScript 直接設定值
                        try
                        {
                            Console.WriteLine("嘗試使用 JavaScript 直接設定值...");
                            await _page.EvaluateAsync(@"(args) => {
                                const input = args.element;
                                const value = args.value;
                                
                                // 清空並設定新值
                                input.value = '';
                                input.value = value;
                                
                                // 觸發各種事件以確保 Google Maps 識別到輸入
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                                input.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
                                
                                // 確保輸入框獲得焦點
                                input.focus();
                                
                                return input.value;
                            }", new { element = listNameInput, value = place.ListName });

                            await _page.WaitForTimeoutAsync(500);
                            var jsValue = await listNameInput.InputValueAsync();
                            Console.WriteLine($"JavaScript 設定後的值: '{jsValue}'");

                            if (jsValue == place.ListName)
                            {
                                inputSuccess = true;
                                Console.WriteLine("✓ JavaScript 輸入成功");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"JavaScript 輸入失敗: {ex.Message}");
                        }

                        // 方式二：強制點擊並使用鍵盤輸入
                        if (!inputSuccess)
                        {
                            try
                            {
                                Console.WriteLine("嘗試強制點擊並使用鍵盤輸入...");

                                // 強制點擊輸入框
                                await listNameInput.ClickAsync(new ElementHandleClickOptions { Force = true });
                                await _page.WaitForTimeoutAsync(200);

                                // 清空內容
                                await _page.Keyboard.PressAsync("Control+a");
                                await _page.WaitForTimeoutAsync(100);
                                await _page.Keyboard.PressAsync("Delete");
                                await _page.WaitForTimeoutAsync(100);

                                // 逐字符輸入（模擬真人打字）
                                foreach (char c in place.ListName)
                                {
                                    await _page.Keyboard.TypeAsync(c.ToString());
                                    await _page.WaitForTimeoutAsync(50); // 每個字符間隔 50ms
                                }

                                await _page.WaitForTimeoutAsync(500);
                                var keyboardValue = await listNameInput.InputValueAsync();
                                Console.WriteLine($"鍵盤輸入後的值: '{keyboardValue}'");

                                if (keyboardValue == place.ListName)
                                {
                                    inputSuccess = true;
                                    Console.WriteLine("✓ 鍵盤輸入成功");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"鍵盤輸入失敗: {ex.Message}");
                            }
                        }

                        // 方式三：使用 Fill 方法（最後嘗試）
                        if (!inputSuccess)
                        {
                            try
                            {
                                Console.WriteLine("嘗試使用 Fill 方法...");
                                await listNameInput.FocusAsync();
                                await _page.WaitForTimeoutAsync(200);
                                await listNameInput.FillAsync(place.ListName);
                                await _page.WaitForTimeoutAsync(500);

                                var fillValue = await listNameInput.InputValueAsync();
                                Console.WriteLine($"Fill 方法後的值: '{fillValue}'");

                                if (fillValue == place.ListName)
                                {
                                    inputSuccess = true;
                                    Console.WriteLine("✓ Fill 方法輸入成功");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Fill 方法失敗: {ex.Message}");
                            }
                        }

                        // 最終驗證
                        await _page.WaitForTimeoutAsync(500);
                        var finalValue = await listNameInput.InputValueAsync();
                        Console.WriteLine($"最終輸入值: '{finalValue}' (預期: '{place.ListName}')");

                        if (finalValue != place.ListName)
                        {
                            Console.WriteLine("⚠️ 警告：輸入值與預期不符，可能需要手動輸入");
                        }

                        // 等待按鈕啟用
                        await _page.WaitForTimeoutAsync(1000);

                        // 多種可能的確認按鈕選擇器
                        var confirmSelectors = new[]
                        {
                            "button:text('建立')",
                            "button:text('Create')",
                            "button[aria-label='建立']",
                            "button[aria-label='Create']",
                            "button[jsaction*='confirm']",
                            "button[jsaction*='save']",
                            "button[data-value='create']",
                            "button[type='submit']",
                            ".modal-dialog button:last-child",
                            "[role='dialog'] button:last-child",
                            "#modal-dialog button:last-child"
                        };

                        IElementHandle? createButton = null;
                        Console.WriteLine("尋找建立按鈕...");

                        foreach (var selector in confirmSelectors)
                        {
                            try
                            {
                                var buttons = await _page.QuerySelectorAllAsync(selector);
                                Console.WriteLine($"選擇器 '{selector}' 找到 {buttons.Count} 個按鈕");

                                foreach (var button in buttons)
                                {
                                    var buttonText = await button.InnerTextAsync();
                                    var isEnabled = await button.IsEnabledAsync();
                                    Console.WriteLine($"  - 按鈕文字: '{buttonText}', 啟用: {isEnabled}");

                                    if (buttonText.Contains("建立") || buttonText.Contains("Create"))
                                    {
                                        createButton = button;
                                        Console.WriteLine($"✓ 找到建立按鈕！");
                                        break;
                                    }
                                }

                                if (createButton != null) break;
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"選擇器 {selector} 失敗: {ex.Message}");
                            }
                        }

                        if (createButton != null)
                        {
                            // 等待按鈕變為可用
                            try
                            {
                                await createButton.WaitForElementStateAsync(ElementState.Enabled, new() { Timeout = 5000 });
                                Console.WriteLine("建立按鈕已啟用");
                            }
                            catch
                            {
                                Console.WriteLine("建立按鈕仍未啟用，嘗試強制點擊...");
                            }

                            await createButton.ClickAsync();
                            Console.WriteLine($"✓ 已建立新清單 '{place.ListName}' 並新增景點 {place.Name}");
                        }
                        else
                        {
                            Console.WriteLine("找不到建立按鈕");
                            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-create-button-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                            return false;
                        }
                    }
                    else
                    {
                        Console.WriteLine("找不到清單名稱輸入框");
                        await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-input-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("找不到建立新清單按鈕");
                    await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"no-new-list-button-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
                    return false;
                }
            }

            await _page.WaitForTimeoutAsync(2000);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"新增景點失敗: {ex.Message}");
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = $"error-{DateTime.Now:yyyyMMdd-HHmmss}.png" });
            return false;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _browser?.CloseAsync().Wait();
            _playwright?.Dispose();
            _disposed = true;
        }
    }
}