"use strict";var $b=Object.create;var ms=Object.defineProperty;var Vb=Object.getOwnPropertyDescriptor;var Hb=Object.getOwnPropertyNames;var Gb=Object.getPrototypeOf,Wb=Object.prototype.hasOwnProperty;var _=(i,e)=>()=>(e||i((e={exports:{}}).exports,e),e.exports),Tf=(i,e)=>{for(var t in e)ms(i,t,{get:e[t],enumerable:!0})},Af=(i,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Hb(e))!Wb.call(i,n)&&n!==t&&ms(i,n,{get:()=>e[n],enumerable:!(r=Vb(e,n))||r.enumerable});return i};var $e=(i,e,t)=>(t=i!=null?$b(Gb(i)):{},Af(e||!i||!i.__esModule?ms(t,"default",{value:i,enumerable:!0}):t,i)),Yb=i=>Af(ms({},"__esModule",{value:!0}),i);var Bf=_((bI,Lf)=>{var Nf={};Lf.exports=Nf;var If={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(If).forEach(function(i){var e=If[i],t=Nf[i]=[];t.open="\x1B["+e[0]+"m",t.close="\x1B["+e[1]+"m"})});var Pf=_((_I,Rf)=>{"use strict";Rf.exports=function(i,e){e=e||process.argv;var t=e.indexOf("--"),r=/^-{1,2}/.test(i)?"":"--",n=e.indexOf(r+i);return n!==-1&&(t===-1?!0:n<t)}});var Df=_((wI,Mf)=>{"use strict";var Kb=require("os"),Ft=Pf(),at=process.env,mr=void 0;Ft("no-color")||Ft("no-colors")||Ft("color=false")?mr=!1:(Ft("color")||Ft("colors")||Ft("color=true")||Ft("color=always"))&&(mr=!0);"FORCE_COLOR"in at&&(mr=at.FORCE_COLOR.length===0||parseInt(at.FORCE_COLOR,10)!==0);function zb(i){return i===0?!1:{level:i,hasBasic:!0,has256:i>=2,has16m:i>=3}}function Jb(i){if(mr===!1)return 0;if(Ft("color=16m")||Ft("color=full")||Ft("color=truecolor"))return 3;if(Ft("color=256"))return 2;if(i&&!i.isTTY&&mr!==!0)return 0;var e=mr?1:0;if(process.platform==="win32"){var t=Kb.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(t[0])>=10&&Number(t[2])>=10586?Number(t[2])>=14931?3:2:1}if("CI"in at)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(n){return n in at})||at.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in at)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(at.TEAMCITY_VERSION)?1:0;if("TERM_PROGRAM"in at){var r=parseInt((at.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(at.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(at.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(at.TERM)||"COLORTERM"in at?1:(at.TERM==="dumb",e)}function Sa(i){var e=Jb(i);return zb(e)}Mf.exports={supportsColor:Sa,stdout:Sa(process.stdout),stderr:Sa(process.stderr)}});var qf=_((xI,Ff)=>{Ff.exports=function(e,t){var r="";e=e||"Run the trap, drop the bass",e=e.split("");var n={a:["@","\u0104","\u023A","\u0245","\u0394","\u039B","\u0414"],b:["\xDF","\u0181","\u0243","\u026E","\u03B2","\u0E3F"],c:["\xA9","\u023B","\u03FE"],d:["\xD0","\u018A","\u0500","\u0501","\u0502","\u0503"],e:["\xCB","\u0115","\u018E","\u0258","\u03A3","\u03BE","\u04BC","\u0A6C"],f:["\u04FA"],g:["\u0262"],h:["\u0126","\u0195","\u04A2","\u04BA","\u04C7","\u050A"],i:["\u0F0F"],j:["\u0134"],k:["\u0138","\u04A0","\u04C3","\u051E"],l:["\u0139"],m:["\u028D","\u04CD","\u04CE","\u0520","\u0521","\u0D69"],n:["\xD1","\u014B","\u019D","\u0376","\u03A0","\u048A"],o:["\xD8","\xF5","\xF8","\u01FE","\u0298","\u047A","\u05DD","\u06DD","\u0E4F"],p:["\u01F7","\u048E"],q:["\u09CD"],r:["\xAE","\u01A6","\u0210","\u024C","\u0280","\u042F"],s:["\xA7","\u03DE","\u03DF","\u03E8"],t:["\u0141","\u0166","\u0373"],u:["\u01B1","\u054D"],v:["\u05D8"],w:["\u0428","\u0460","\u047C","\u0D70"],x:["\u04B2","\u04FE","\u04FC","\u04FD"],y:["\xA5","\u04B0","\u04CB"],z:["\u01B5","\u0240"]};return e.forEach(function(s){s=s.toLowerCase();var o=n[s]||[" "],a=Math.floor(Math.random()*o.length);typeof n[s]!="undefined"?r+=n[s][a]:r+=s}),r}});var Uf=_((SI,jf)=>{jf.exports=function(e,t){e=e||"   he is here   ";var r={up:["\u030D","\u030E","\u0304","\u0305","\u033F","\u0311","\u0306","\u0310","\u0352","\u0357","\u0351","\u0307","\u0308","\u030A","\u0342","\u0313","\u0308","\u034A","\u034B","\u034C","\u0303","\u0302","\u030C","\u0350","\u0300","\u0301","\u030B","\u030F","\u0312","\u0313","\u0314","\u033D","\u0309","\u0363","\u0364","\u0365","\u0366","\u0367","\u0368","\u0369","\u036A","\u036B","\u036C","\u036D","\u036E","\u036F","\u033E","\u035B","\u0346","\u031A"],down:["\u0316","\u0317","\u0318","\u0319","\u031C","\u031D","\u031E","\u031F","\u0320","\u0324","\u0325","\u0326","\u0329","\u032A","\u032B","\u032C","\u032D","\u032E","\u032F","\u0330","\u0331","\u0332","\u0333","\u0339","\u033A","\u033B","\u033C","\u0345","\u0347","\u0348","\u0349","\u034D","\u034E","\u0353","\u0354","\u0355","\u0356","\u0359","\u035A","\u0323"],mid:["\u0315","\u031B","\u0300","\u0301","\u0358","\u0321","\u0322","\u0327","\u0328","\u0334","\u0335","\u0336","\u035C","\u035D","\u035E","\u035F","\u0360","\u0362","\u0338","\u0337","\u0361"," \u0489"]},n=[].concat(r.up,r.down,r.mid);function s(l){var c=Math.floor(Math.random()*l);return c}function o(l){var c=!1;return n.filter(function(u){c=u===l}),c}function a(l,c){var u="",f,d;c=c||{},c.up=typeof c.up!="undefined"?c.up:!0,c.mid=typeof c.mid!="undefined"?c.mid:!0,c.down=typeof c.down!="undefined"?c.down:!0,c.size=typeof c.size!="undefined"?c.size:"maxi",l=l.split("");for(d in l)if(!o(d)){switch(u=u+l[d],f={up:0,down:0,mid:0},c.size){case"mini":f.up=s(8),f.mid=s(2),f.down=s(8);break;case"maxi":f.up=s(16)+3,f.mid=s(4)+1,f.down=s(64)+3;break;default:f.up=s(8)+1,f.mid=s(6)/2,f.down=s(8)+1;break}var m=["up","mid","down"];for(var g in m)for(var y=m[g],b=0;b<=f[y];b++)c[y]&&(u=u+r[y][s(r[y].length)])}return u}return a(e,t)}});var Vf=_((EI,$f)=>{$f.exports=function(i){return function(e,t,r){if(e===" ")return e;switch(t%3){case 0:return i.red(e);case 1:return i.white(e);case 2:return i.blue(e)}}}});var Gf=_((OI,Hf)=>{Hf.exports=function(i){return function(e,t,r){return t%2===0?e:i.inverse(e)}}});var Yf=_((kI,Wf)=>{Wf.exports=function(i){var e=["red","yellow","green","blue","magenta"];return function(t,r,n){return t===" "?t:i[e[r++%e.length]](t)}}});var zf=_((CI,Kf)=>{Kf.exports=function(i){var e=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(t,r,n){return t===" "?t:i[e[Math.round(Math.random()*(e.length-2))]](t)}}});var th=_((AI,eh)=>{var ye={};eh.exports=ye;ye.themes={};var Zb=require("util"),Ui=ye.styles=Bf(),Zf=Object.defineProperties,Qb=new RegExp(/[\r\n]+/g);ye.supportsColor=Df().supportsColor;typeof ye.enabled=="undefined"&&(ye.enabled=ye.supportsColor()!==!1);ye.enable=function(){ye.enabled=!0};ye.disable=function(){ye.enabled=!1};ye.stripColors=ye.strip=function(i){return(""+i).replace(/\x1B\[\d+m/g,"")};var TI=ye.stylize=function(e,t){if(!ye.enabled)return e+"";var r=Ui[t];return!r&&t in ye?ye[t](e):r.open+e+r.close},Xb=/[|\\{}()[\]^$+*?.]/g,e_=function(i){if(typeof i!="string")throw new TypeError("Expected a string");return i.replace(Xb,"\\$&")};function Qf(i){var e=function t(){return i_.apply(t,arguments)};return e._styles=i,e.__proto__=t_,e}var Xf=function(){var i={};return Ui.grey=Ui.gray,Object.keys(Ui).forEach(function(e){Ui[e].closeRe=new RegExp(e_(Ui[e].close),"g"),i[e]={get:function(){return Qf(this._styles.concat(e))}}}),i}(),t_=Zf(function(){},Xf);function i_(){var i=Array.prototype.slice.call(arguments),e=i.map(function(o){return o!=null&&o.constructor===String?o:Zb.inspect(o)}).join(" ");if(!ye.enabled||!e)return e;for(var t=e.indexOf(`
`)!=-1,r=this._styles,n=r.length;n--;){var s=Ui[r[n]];e=s.open+e.replace(s.closeRe,s.open)+s.close,t&&(e=e.replace(Qb,function(o){return s.close+o+s.open}))}return e}ye.setTheme=function(i){if(typeof i=="string"){console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");return}for(var e in i)(function(t){ye[t]=function(r){if(typeof i[t]=="object"){var n=r;for(var s in i[t])n=ye[i[t][s]](n);return n}return ye[i[t]](r)}})(e)};function r_(){var i={};return Object.keys(Xf).forEach(function(e){i[e]={get:function(){return Qf([e])}}}),i}var n_=function(e,t){var r=t.split("");return r=r.map(e),r.join("")};ye.trap=qf();ye.zalgo=Uf();ye.maps={};ye.maps.america=Vf()(ye);ye.maps.zebra=Gf()(ye);ye.maps.rainbow=Yf()(ye);ye.maps.random=zf()(ye);for(Jf in ye.maps)(function(i){ye[i]=function(e){return n_(ye.maps[i],e)}})(Jf);var Jf;Zf(ye,r_())});var rh=_((II,ih)=>{var s_=th();ih.exports=s_});var sh=_((NI,nh)=>{var gr=1e3,vr=gr*60,yr=vr*60,$i=yr*24,o_=$i*7,a_=$i*365.25;nh.exports=function(i,e){e=e||{};var t=typeof i;if(t==="string"&&i.length>0)return l_(i);if(t==="number"&&isFinite(i))return e.long?u_(i):c_(i);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(i))};function l_(i){if(i=String(i),!(i.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(i);if(e){var t=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return t*a_;case"weeks":case"week":case"w":return t*o_;case"days":case"day":case"d":return t*$i;case"hours":case"hour":case"hrs":case"hr":case"h":return t*yr;case"minutes":case"minute":case"mins":case"min":case"m":return t*vr;case"seconds":case"second":case"secs":case"sec":case"s":return t*gr;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function c_(i){var e=Math.abs(i);return e>=$i?Math.round(i/$i)+"d":e>=yr?Math.round(i/yr)+"h":e>=vr?Math.round(i/vr)+"m":e>=gr?Math.round(i/gr)+"s":i+"ms"}function u_(i){var e=Math.abs(i);return e>=$i?gs(i,e,$i,"day"):e>=yr?gs(i,e,yr,"hour"):e>=vr?gs(i,e,vr,"minute"):e>=gr?gs(i,e,gr,"second"):i+" ms"}function gs(i,e,t,r){var n=e>=t*1.5;return Math.round(i/t)+" "+r+(n?"s":"")}});var Ea=_((LI,oh)=>{function f_(i){t.debug=t,t.default=t,t.coerce=l,t.disable=s,t.enable=n,t.enabled=o,t.humanize=sh(),t.destroy=c,Object.keys(i).forEach(u=>{t[u]=i[u]}),t.names=[],t.skips=[],t.formatters={};function e(u){let f=0;for(let d=0;d<u.length;d++)f=(f<<5)-f+u.charCodeAt(d),f|=0;return t.colors[Math.abs(f)%t.colors.length]}t.selectColor=e;function t(u){let f,d=null,m,g;function y(...b){if(!y.enabled)return;let x=y,E=Number(new Date),k=E-(f||E);x.diff=k,x.prev=f,x.curr=E,f=E,b[0]=t.coerce(b[0]),typeof b[0]!="string"&&b.unshift("%O");let O=0;b[0]=b[0].replace(/%([a-zA-Z%])/g,(R,T)=>{if(R==="%%")return"%";O++;let A=t.formatters[T];if(typeof A=="function"){let C=b[O];R=A.call(x,C),b.splice(O,1),O--}return R}),t.formatArgs.call(x,b),(x.log||t.log).apply(x,b)}return y.namespace=u,y.useColors=t.useColors(),y.color=t.selectColor(u),y.extend=r,y.destroy=t.destroy,Object.defineProperty(y,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==t.namespaces&&(m=t.namespaces,g=t.enabled(u)),g),set:b=>{d=b}}),typeof t.init=="function"&&t.init(y),y}function r(u,f){let d=t(this.namespace+(typeof f=="undefined"?":":f)+u);return d.log=this.log,d}function n(u){t.save(u),t.namespaces=u,t.names=[],t.skips=[];let f,d=(typeof u=="string"?u:"").split(/[\s,]+/),m=d.length;for(f=0;f<m;f++)d[f]&&(u=d[f].replace(/\*/g,".*?"),u[0]==="-"?t.skips.push(new RegExp("^"+u.slice(1)+"$")):t.names.push(new RegExp("^"+u+"$")))}function s(){let u=[...t.names.map(a),...t.skips.map(a).map(f=>"-"+f)].join(",");return t.enable(""),u}function o(u){if(u[u.length-1]==="*")return!0;let f,d;for(f=0,d=t.skips.length;f<d;f++)if(t.skips[f].test(u))return!1;for(f=0,d=t.names.length;f<d;f++)if(t.names[f].test(u))return!0;return!1}function a(u){return u.toString().substring(2,u.toString().length-2).replace(/\.\*\?$/,"*")}function l(u){return u instanceof Error?u.stack||u.message:u}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return t.enable(t.load()),t}oh.exports=f_});var ah=_((wt,vs)=>{wt.formatArgs=p_;wt.save=d_;wt.load=m_;wt.useColors=h_;wt.storage=g_();wt.destroy=(()=>{let i=!1;return()=>{i||(i=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();wt.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function h_(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function p_(i){if(i[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+i[0]+(this.useColors?"%c ":" ")+"+"+vs.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;i.splice(1,0,e,"color: inherit");let t=0,r=0;i[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(t++,n==="%c"&&(r=t))}),i.splice(r,0,e)}wt.log=console.debug||console.log||(()=>{});function d_(i){try{i?wt.storage.setItem("debug",i):wt.storage.removeItem("debug")}catch{}}function m_(){let i;try{i=wt.storage.getItem("debug")}catch{}return!i&&typeof process!="undefined"&&"env"in process&&(i=process.env.DEBUG),i}function g_(){try{return localStorage}catch{}}vs.exports=Ea()(wt);var{formatters:v_}=vs.exports;v_.j=function(i){try{return JSON.stringify(i)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var ch=_((BI,lh)=>{"use strict";lh.exports=(i,e=process.argv)=>{let t=i.startsWith("-")?"":i.length===1?"-":"--",r=e.indexOf(t+i),n=e.indexOf("--");return r!==-1&&(n===-1||r<n)}});var hh=_((RI,fh)=>{"use strict";var y_=require("os"),uh=require("tty"),Ct=ch(),{env:Ke}=process,ys;Ct("no-color")||Ct("no-colors")||Ct("color=false")||Ct("color=never")?ys=0:(Ct("color")||Ct("colors")||Ct("color=true")||Ct("color=always"))&&(ys=1);function b_(){if("FORCE_COLOR"in Ke)return Ke.FORCE_COLOR==="true"?1:Ke.FORCE_COLOR==="false"?0:Ke.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(Ke.FORCE_COLOR,10),3)}function __(i){return i===0?!1:{level:i,hasBasic:!0,has256:i>=2,has16m:i>=3}}function w_(i,{streamIsTTY:e,sniffFlags:t=!0}={}){let r=b_();r!==void 0&&(ys=r);let n=t?ys:r;if(n===0)return 0;if(t){if(Ct("color=16m")||Ct("color=full")||Ct("color=truecolor"))return 3;if(Ct("color=256"))return 2}if(i&&!e&&n===void 0)return 0;let s=n||0;if(Ke.TERM==="dumb")return s;if(process.platform==="win32"){let o=y_.release().split(".");return Number(o[0])>=10&&Number(o[2])>=10586?Number(o[2])>=14931?3:2:1}if("CI"in Ke)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(o=>o in Ke)||Ke.CI_NAME==="codeship"?1:s;if("TEAMCITY_VERSION"in Ke)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(Ke.TEAMCITY_VERSION)?1:0;if(Ke.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in Ke){let o=Number.parseInt((Ke.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(Ke.TERM_PROGRAM){case"iTerm.app":return o>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(Ke.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(Ke.TERM)||"COLORTERM"in Ke?1:s}function Oa(i,e={}){let t=w_(i,{streamIsTTY:i&&i.isTTY,...e});return __(t)}fh.exports={supportsColor:Oa,stdout:Oa({isTTY:uh.isatty(1)}),stderr:Oa({isTTY:uh.isatty(2)})}});var dh=_((Xe,_s)=>{var x_=require("tty"),bs=require("util");Xe.init=A_;Xe.log=k_;Xe.formatArgs=E_;Xe.save=C_;Xe.load=T_;Xe.useColors=S_;Xe.destroy=bs.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");Xe.colors=[6,2,3,4,5,1];try{let i=hh();i&&(i.stderr||i).level>=2&&(Xe.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}Xe.inspectOpts=Object.keys(process.env).filter(i=>/^debug_/i.test(i)).reduce((i,e)=>{let t=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),i[t]=r,i},{});function S_(){return"colors"in Xe.inspectOpts?!!Xe.inspectOpts.colors:x_.isatty(process.stderr.fd)}function E_(i){let{namespace:e,useColors:t}=this;if(t){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),s=`  ${n};1m${e} \x1B[0m`;i[0]=s+i[0].split(`
`).join(`
`+s),i.push(n+"m+"+_s.exports.humanize(this.diff)+"\x1B[0m")}else i[0]=O_()+e+" "+i[0]}function O_(){return Xe.inspectOpts.hideDate?"":new Date().toISOString()+" "}function k_(...i){return process.stderr.write(bs.format(...i)+`
`)}function C_(i){i?process.env.DEBUG=i:delete process.env.DEBUG}function T_(){return process.env.DEBUG}function A_(i){i.inspectOpts={};let e=Object.keys(Xe.inspectOpts);for(let t=0;t<e.length;t++)i.inspectOpts[e[t]]=Xe.inspectOpts[e[t]]}_s.exports=Ea()(Xe);var{formatters:ph}=_s.exports;ph.o=function(i){return this.inspectOpts.colors=this.useColors,bs.inspect(i,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};ph.O=function(i){return this.inspectOpts.colors=this.useColors,bs.inspect(i,this.inspectOpts)}});var br=_((PI,ka)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?ka.exports=ah():ka.exports=dh()});var qh=_((MI,fw)=>{fw.exports={name:"dotenv",version:"16.4.5",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard","lint-readme":"standard-markdown",pretest:"npm run lint && npm run dts-check",test:"tap tests/*.js --100 -Rspec","test:coverage":"tap --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@definitelytyped/dtslint":"^0.0.133","@types/node":"^18.11.3",decache:"^4.6.1",sinon:"^14.0.1",standard:"^17.0.0","standard-markdown":"^7.1.0","standard-version":"^9.5.0",tap:"^16.3.0",tar:"^6.1.11",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var Vh=_((DI,ri)=>{var Ma=require("fs"),Da=require("path"),hw=require("os"),pw=require("crypto"),dw=qh(),Fa=dw.version,mw=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function gw(i){let e={},t=i.toString();t=t.replace(/\r\n?/mg,`
`);let r;for(;(r=mw.exec(t))!=null;){let n=r[1],s=r[2]||"";s=s.trim();let o=s[0];s=s.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),o==='"'&&(s=s.replace(/\\n/g,`
`),s=s.replace(/\\r/g,"\r")),e[n]=s}return e}function vw(i){let e=$h(i),t=Ve.configDotenv({path:e});if(!t.parsed){let o=new Error(`MISSING_DATA: Cannot parse ${e} for an unknown reason`);throw o.code="MISSING_DATA",o}let r=Uh(i).split(","),n=r.length,s;for(let o=0;o<n;o++)try{let a=r[o].trim(),l=_w(t,a);s=Ve.decrypt(l.ciphertext,l.key);break}catch(a){if(o+1>=n)throw a}return Ve.parse(s)}function yw(i){console.log(`[dotenv@${Fa}][INFO] ${i}`)}function bw(i){console.log(`[dotenv@${Fa}][WARN] ${i}`)}function As(i){console.log(`[dotenv@${Fa}][DEBUG] ${i}`)}function Uh(i){return i&&i.DOTENV_KEY&&i.DOTENV_KEY.length>0?i.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function _w(i,e){let t;try{t=new URL(e)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let r=t.password;if(!r){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let n=t.searchParams.get("environment");if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let s=`DOTENV_VAULT_${n.toUpperCase()}`,o=i.parsed[s];if(!o){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:o,key:r}}function $h(i){let e=null;if(i&&i.path&&i.path.length>0)if(Array.isArray(i.path))for(let t of i.path)Ma.existsSync(t)&&(e=t.endsWith(".vault")?t:`${t}.vault`);else e=i.path.endsWith(".vault")?i.path:`${i.path}.vault`;else e=Da.resolve(process.cwd(),".env.vault");return Ma.existsSync(e)?e:null}function jh(i){return i[0]==="~"?Da.join(hw.homedir(),i.slice(1)):i}function ww(i){yw("Loading env from encrypted .env.vault");let e=Ve._parseVault(i),t=process.env;return i&&i.processEnv!=null&&(t=i.processEnv),Ve.populate(t,e,i),{parsed:e}}function xw(i){let e=Da.resolve(process.cwd(),".env"),t="utf8",r=!!(i&&i.debug);i&&i.encoding?t=i.encoding:r&&As("No encoding is specified. UTF-8 is used by default");let n=[e];if(i&&i.path)if(!Array.isArray(i.path))n=[jh(i.path)];else{n=[];for(let l of i.path)n.push(jh(l))}let s,o={};for(let l of n)try{let c=Ve.parse(Ma.readFileSync(l,{encoding:t}));Ve.populate(o,c,i)}catch(c){r&&As(`Failed to load ${l} ${c.message}`),s=c}let a=process.env;return i&&i.processEnv!=null&&(a=i.processEnv),Ve.populate(a,o,i),s?{parsed:o,error:s}:{parsed:o}}function Sw(i){if(Uh(i).length===0)return Ve.configDotenv(i);let e=$h(i);return e?Ve._configVault(i):(bw(`You set DOTENV_KEY but you are missing a .env.vault file at ${e}. Did you forget to build it?`),Ve.configDotenv(i))}function Ew(i,e){let t=Buffer.from(e.slice(-64),"hex"),r=Buffer.from(i,"base64"),n=r.subarray(0,12),s=r.subarray(-16);r=r.subarray(12,-16);try{let o=pw.createDecipheriv("aes-256-gcm",t,n);return o.setAuthTag(s),`${o.update(r)}${o.final()}`}catch(o){let a=o instanceof RangeError,l=o.message==="Invalid key length",c=o.message==="Unsupported state or unable to authenticate data";if(a||l){let u=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw u.code="INVALID_DOTENV_KEY",u}else if(c){let u=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw u.code="DECRYPTION_FAILED",u}else throw o}}function Ow(i,e,t={}){let r=!!(t&&t.debug),n=!!(t&&t.override);if(typeof e!="object"){let s=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw s.code="OBJECT_REQUIRED",s}for(let s of Object.keys(e))Object.prototype.hasOwnProperty.call(i,s)?(n===!0&&(i[s]=e[s]),r&&As(n===!0?`"${s}" is already defined and WAS overwritten`:`"${s}" is already defined and was NOT overwritten`)):i[s]=e[s]}var Ve={configDotenv:xw,_configVault:ww,_parseVault:vw,config:Sw,decrypt:Ew,parse:gw,populate:Ow};ri.exports.configDotenv=Ve.configDotenv;ri.exports._configVault=Ve._configVault;ri.exports._parseVault=Ve._parseVault;ri.exports.config=Ve.config;ri.exports.decrypt=Ve.decrypt;ri.exports.parse=Ve.parse;ri.exports.populate=Ve.populate;ri.exports=Ve});var Gh=_(Hh=>{"use strict";var kw=require("url").parse,Cw={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},Tw=String.prototype.endsWith||function(i){return i.length<=this.length&&this.indexOf(i,this.length-i.length)!==-1};function Aw(i){var e=typeof i=="string"?kw(i):i||{},t=e.protocol,r=e.host,n=e.port;if(typeof r!="string"||!r||typeof t!="string"||(t=t.split(":",1)[0],r=r.replace(/:\d*$/,""),n=parseInt(n)||Cw[t]||0,!Iw(r,n)))return"";var s=wr("npm_config_"+t+"_proxy")||wr(t+"_proxy")||wr("npm_config_proxy")||wr("all_proxy");return s&&s.indexOf("://")===-1&&(s=t+"://"+s),s}function Iw(i,e){var t=(wr("npm_config_no_proxy")||wr("no_proxy")).toLowerCase();return t?t==="*"?!1:t.split(/[,\s]/).every(function(r){if(!r)return!0;var n=r.match(/^(.+):(\d+)$/),s=n?n[1]:r,o=n?parseInt(n[2]):0;return o&&o!==e?!0:/^[.*]/.test(s)?(s.charAt(0)==="*"&&(s=s.slice(1)),!Tw.call(i,s)):i!==s}):!0}function wr(i){return process.env[i.toLowerCase()]||process.env[i.toUpperCase()]||""}Hh.getProxyForUrl=Aw});var Wh=_(qa=>{"use strict";Object.defineProperty(qa,"__esModule",{value:!0});function Nw(i){return function(e,t){return new Promise((r,n)=>{i.call(this,e,t,(s,o)=>{s?n(s):r(o)})})}}qa.default=Nw});var $a=_((Ua,Kh)=>{"use strict";var Yh=Ua&&Ua.__importDefault||function(i){return i&&i.__esModule?i:{default:i}},Lw=require("events"),Bw=Yh(br()),Rw=Yh(Wh()),nn=Bw.default("agent-base");function Pw(i){return!!i&&typeof i.addRequest=="function"}function ja(){let{stack:i}=new Error;return typeof i!="string"?!1:i.split(`
`).some(e=>e.indexOf("(https.js:")!==-1||e.indexOf("node:https:")!==-1)}function Is(i,e){return new Is.Agent(i,e)}(function(i){class e extends Lw.EventEmitter{constructor(r,n){super();let s=n;typeof r=="function"?this.callback=r:r&&(s=r),this.timeout=null,s&&typeof s.timeout=="number"&&(this.timeout=s.timeout),this.maxFreeSockets=1,this.maxSockets=1,this.maxTotalSockets=1/0,this.sockets={},this.freeSockets={},this.requests={},this.options={}}get defaultPort(){return typeof this.explicitDefaultPort=="number"?this.explicitDefaultPort:ja()?443:80}set defaultPort(r){this.explicitDefaultPort=r}get protocol(){return typeof this.explicitProtocol=="string"?this.explicitProtocol:ja()?"https:":"http:"}set protocol(r){this.explicitProtocol=r}callback(r,n,s){throw new Error('"agent-base" has no default implementation, you must subclass and override `callback()`')}addRequest(r,n){let s=Object.assign({},n);typeof s.secureEndpoint!="boolean"&&(s.secureEndpoint=ja()),s.host==null&&(s.host="localhost"),s.port==null&&(s.port=s.secureEndpoint?443:80),s.protocol==null&&(s.protocol=s.secureEndpoint?"https:":"http:"),s.host&&s.path&&delete s.path,delete s.agent,delete s.hostname,delete s._defaultAgent,delete s.defaultPort,delete s.createConnection,r._last=!0,r.shouldKeepAlive=!1;let o=!1,a=null,l=s.timeout||this.timeout,c=m=>{r._hadError||(r.emit("error",m),r._hadError=!0)},u=()=>{a=null,o=!0;let m=new Error(`A "socket" was not created for HTTP request before ${l}ms`);m.code="ETIMEOUT",c(m)},f=m=>{o||(a!==null&&(clearTimeout(a),a=null),c(m))},d=m=>{if(o)return;if(a!=null&&(clearTimeout(a),a=null),Pw(m)){nn("Callback returned another Agent instance %o",m.constructor.name),m.addRequest(r,s);return}if(m){m.once("free",()=>{this.freeSocket(m,s)}),r.onSocket(m);return}let g=new Error(`no Duplex stream was returned to agent-base for \`${r.method} ${r.path}\``);c(g)};if(typeof this.callback!="function"){c(new Error("`callback` is not defined"));return}this.promisifiedCallback||(this.callback.length>=3?(nn("Converting legacy callback function to promise"),this.promisifiedCallback=Rw.default(this.callback)):this.promisifiedCallback=this.callback),typeof l=="number"&&l>0&&(a=setTimeout(u,l)),"port"in s&&typeof s.port!="number"&&(s.port=Number(s.port));try{nn("Resolving socket for %o request: %o",s.protocol,`${r.method} ${r.path}`),Promise.resolve(this.promisifiedCallback(r,s)).then(d,f)}catch(m){Promise.reject(m).catch(f)}}freeSocket(r,n){nn("Freeing socket %o %o",r.constructor.name,n),r.destroy()}destroy(){nn("Destroying agent %o",this.constructor.name)}}i.Agent=e,i.prototype=i.Agent.prototype})(Is||(Is={}));Kh.exports=Is});var zh=_(on=>{"use strict";var Mw=on&&on.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(on,"__esModule",{value:!0});var Dw=Mw(br()),sn=Dw.default("https-proxy-agent:parse-proxy-response");function Fw(i){return new Promise((e,t)=>{let r=0,n=[];function s(){let f=i.read();f?u(f):i.once("readable",s)}function o(){i.removeListener("end",l),i.removeListener("error",c),i.removeListener("close",a),i.removeListener("readable",s)}function a(f){sn("onclose had error %o",f)}function l(){sn("onend")}function c(f){o(),sn("onerror %o",f),t(f)}function u(f){n.push(f),r+=f.length;let d=Buffer.concat(n,r);if(d.indexOf(`\r
\r
`)===-1){sn("have not received end of HTTP headers yet..."),s();return}let g=d.toString("ascii",0,d.indexOf(`\r
`)),y=+g.split(" ")[1];sn("got proxy server response: %o",g),e({statusCode:y,buffered:d})}i.on("error",c),i.on("close",a),i.on("end",l),s()})}on.default=Fw});var Qh=_(Hi=>{"use strict";var qw=Hi&&Hi.__awaiter||function(i,e,t,r){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(u){try{c(r.next(u))}catch(f){o(f)}}function l(u){try{c(r.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})},xr=Hi&&Hi.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Hi,"__esModule",{value:!0});var Jh=xr(require("net")),Zh=xr(require("tls")),jw=xr(require("url")),Uw=xr(require("assert")),$w=xr(br()),Vw=$a(),Hw=xr(zh()),an=$w.default("https-proxy-agent:agent"),Va=class extends Vw.Agent{constructor(e){let t;if(typeof e=="string"?t=jw.default.parse(e):t=e,!t)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");an("creating new HttpsProxyAgent instance: %o",t),super(t);let r=Object.assign({},t);this.secureProxy=t.secureProxy||Yw(r.protocol),r.host=r.hostname||r.host,typeof r.port=="string"&&(r.port=parseInt(r.port,10)),!r.port&&r.host&&(r.port=this.secureProxy?443:80),this.secureProxy&&!("ALPNProtocols"in r)&&(r.ALPNProtocols=["http 1.1"]),r.host&&r.path&&(delete r.path,delete r.pathname),this.proxy=r}callback(e,t){return qw(this,void 0,void 0,function*(){let{proxy:r,secureProxy:n}=this,s;n?(an("Creating `tls.Socket`: %o",r),s=Zh.default.connect(r)):(an("Creating `net.Socket`: %o",r),s=Jh.default.connect(r));let o=Object.assign({},r.headers),l=`CONNECT ${`${t.host}:${t.port}`} HTTP/1.1\r
`;r.auth&&(o["Proxy-Authorization"]=`Basic ${Buffer.from(r.auth).toString("base64")}`);let{host:c,port:u,secureEndpoint:f}=t;Ww(u,f)||(c+=`:${u}`),o.Host=c,o.Connection="close";for(let b of Object.keys(o))l+=`${b}: ${o[b]}\r
`;let d=Hw.default(s);s.write(`${l}\r
`);let{statusCode:m,buffered:g}=yield d;if(m===200){if(e.once("socket",Gw),t.secureEndpoint){an("Upgrading socket connection to TLS");let b=t.servername||t.host;return Zh.default.connect(Object.assign(Object.assign({},Kw(t,"host","hostname","path","port")),{socket:s,servername:b}))}return s}s.destroy();let y=new Jh.default.Socket({writable:!1});return y.readable=!0,e.once("socket",b=>{an("replaying proxy buffer for failed request"),Uw.default(b.listenerCount("data")>0),b.push(g),b.push(null)}),y})}};Hi.default=Va;function Gw(i){i.resume()}function Ww(i,e){return!!(!e&&i===80||e&&i===443)}function Yw(i){return typeof i=="string"?/^https:?$/i.test(i):!1}function Kw(i,...e){let t={},r;for(r in i)e.includes(r)||(t[r]=i[r]);return t}});var ep=_((Wa,Xh)=>{"use strict";var zw=Wa&&Wa.__importDefault||function(i){return i&&i.__esModule?i:{default:i}},Ha=zw(Qh());function Ga(i){return new Ha.default(i)}(function(i){i.HttpsProxyAgent=Ha.default,i.prototype=Ha.default.prototype})(Ga||(Ga={}));Xh.exports=Ga});var rp=_(($I,Ns)=>{var ip=ip||function(i){return Buffer.from(i).toString("base64")};function Jw(i){var e=this,t=Math.round,r=Math.floor,n=new Array(64),s=new Array(64),o=new Array(64),a=new Array(64),l,c,u,f,d=new Array(65535),m=new Array(65535),g=new Array(64),y=new Array(64),b=[],x=0,E=7,k=new Array(64),O=new Array(64),S=new Array(64),R=new Array(256),T=new Array(2048),A,C=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],P=[0,1,2,3,4,5,6,7,8,9,10,11],U=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],F=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],H=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],j=[0,1,2,3,4,5,6,7,8,9,10,11],V=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],Y=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function Q(I){for(var Z=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],te=0;te<64;te++){var ee=r((Z[te]*I+50)/100);ee<1?ee=1:ee>255&&(ee=255),n[C[te]]=ee}for(var le=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],ce=0;ce<64;ce++){var _e=r((le[ce]*I+50)/100);_e<1?_e=1:_e>255&&(_e=255),s[C[ce]]=_e}for(var we=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Re=0,Ae=0;Ae<8;Ae++)for(var q=0;q<8;q++)o[Re]=1/(n[C[Re]]*we[Ae]*we[q]*8),a[Re]=1/(s[C[Re]]*we[Ae]*we[q]*8),Re++}function W(I,Z){for(var te=0,ee=0,le=new Array,ce=1;ce<=16;ce++){for(var _e=1;_e<=I[ce];_e++)le[Z[ee]]=[],le[Z[ee]][0]=te,le[Z[ee]][1]=ce,ee++,te++;te*=2}return le}function de(){l=W(L,P),c=W(H,j),u=W(U,F),f=W(V,Y)}function ae(){for(var I=1,Z=2,te=1;te<=15;te++){for(var ee=I;ee<Z;ee++)m[32767+ee]=te,d[32767+ee]=[],d[32767+ee][1]=te,d[32767+ee][0]=ee;for(var le=-(Z-1);le<=-I;le++)m[32767+le]=te,d[32767+le]=[],d[32767+le][1]=te,d[32767+le][0]=Z-1+le;I<<=1,Z<<=1}}function ne(){for(var I=0;I<256;I++)T[I]=19595*I,T[I+256>>0]=38470*I,T[I+512>>0]=7471*I+32768,T[I+768>>0]=-11059*I,T[I+1024>>0]=-21709*I,T[I+1280>>0]=32768*I+8421375,T[I+1536>>0]=-27439*I,T[I+1792>>0]=-5329*I}function ue(I){for(var Z=I[0],te=I[1]-1;te>=0;)Z&1<<te&&(x|=1<<E),te--,E--,E<0&&(x==255?(N(255),N(0)):N(x),E=7,x=0)}function N(I){b.push(I)}function X(I){N(I>>8&255),N(I&255)}function ke(I,Z){var te,ee,le,ce,_e,we,Re,Ae,q=0,J,se=8,Ne=64;for(J=0;J<se;++J){te=I[q],ee=I[q+1],le=I[q+2],ce=I[q+3],_e=I[q+4],we=I[q+5],Re=I[q+6],Ae=I[q+7];var oe=te+Ae,me=te-Ae,Ee=ee+Re,ie=ee-Re,xe=le+we,Ue=le-we,Ie=ce+_e,pt=ce-_e,Ot=oe+Ie,Xt=oe-Ie,hi=Ee+xe,pi=Ee-xe;I[q]=Ot+hi,I[q+4]=Ot-hi;var Bi=(pi+Xt)*.707106781;I[q+2]=Xt+Bi,I[q+6]=Xt-Bi,Ot=pt+Ue,hi=Ue+ie,pi=ie+me;var Ri=(Ot-pi)*.382683433,hr=.5411961*Ot+Ri,Pi=1.306562965*pi+Ri,Mi=hi*.707106781,Di=me+Mi,Fi=me-Mi;I[q+5]=Fi+hr,I[q+3]=Fi-hr,I[q+1]=Di+Pi,I[q+7]=Di-Pi,q+=8}for(q=0,J=0;J<se;++J){te=I[q],ee=I[q+8],le=I[q+16],ce=I[q+24],_e=I[q+32],we=I[q+40],Re=I[q+48],Ae=I[q+56];var es=te+Ae,Xr=te-Ae,ts=ee+Re,is=ee-Re,rs=le+we,ns=le-we,ss=ce+_e,ya=ce-_e,qi=es+ss,ei=es-ss,ji=ts+rs,pr=ts-rs;I[q]=qi+ji,I[q+32]=qi-ji;var os=(pr+ei)*.707106781;I[q+16]=ei+os,I[q+48]=ei-os,qi=ya+ns,ji=ns+is,pr=is+Xr;var as=(qi-pr)*.382683433,ls=.5411961*qi+as,cs=1.306562965*pr+as,Vt=ji*.707106781,us=Xr+Vt,fs=Xr-Vt;I[q+40]=fs+ls,I[q+24]=fs-ls,I[q+8]=us+cs,I[q+56]=us-cs,q++}var dr;for(J=0;J<Ne;++J)dr=I[J]*Z[J],g[J]=dr>0?dr+.5|0:dr-.5|0;return g}function be(){X(65504),X(16),N(74),N(70),N(73),N(70),N(0),N(1),N(1),N(0),X(1),X(1),N(0),N(0)}function ge(I){if(I){X(65505),I[0]===69&&I[1]===120&&I[2]===105&&I[3]===102?X(I.length+2):(X(I.length+5+2),N(69),N(120),N(105),N(102),N(0));for(var Z=0;Z<I.length;Z++)N(I[Z])}}function ve(I,Z){X(65472),X(17),N(8),X(Z),X(I),N(3),N(1),N(17),N(0),N(2),N(17),N(1),N(3),N(17),N(1)}function fe(){X(65499),X(132),N(0);for(var I=0;I<64;I++)N(n[I]);N(1);for(var Z=0;Z<64;Z++)N(s[Z])}function z(){X(65476),X(418),N(0);for(var I=0;I<16;I++)N(L[I+1]);for(var Z=0;Z<=11;Z++)N(P[Z]);N(16);for(var te=0;te<16;te++)N(U[te+1]);for(var ee=0;ee<=161;ee++)N(F[ee]);N(1);for(var le=0;le<16;le++)N(H[le+1]);for(var ce=0;ce<=11;ce++)N(j[ce]);N(17);for(var _e=0;_e<16;_e++)N(V[_e+1]);for(var we=0;we<=161;we++)N(Y[we])}function $(I){typeof I=="undefined"||I.constructor!==Array||I.forEach(Z=>{if(typeof Z=="string"){X(65534);var te=Z.length;X(te+2);var ee;for(ee=0;ee<te;ee++)N(Z.charCodeAt(ee))}})}function Te(){X(65498),X(12),N(3),N(1),N(0),N(2),N(17),N(3),N(17),N(0),N(63),N(0)}function re(I,Z,te,ee,le){for(var ce=le[0],_e=le[240],we,Re=16,Ae=63,q=64,J=ke(I,Z),se=0;se<q;++se)y[C[se]]=J[se];var Ne=y[0]-te;te=y[0],Ne==0?ue(ee[0]):(we=32767+Ne,ue(ee[m[we]]),ue(d[we]));for(var oe=63;oe>0&&y[oe]==0;oe--);if(oe==0)return ue(ce),te;for(var me=1,Ee;me<=oe;){for(var ie=me;y[me]==0&&me<=oe;++me);var xe=me-ie;if(xe>=Re){Ee=xe>>4;for(var Ue=1;Ue<=Ee;++Ue)ue(_e);xe=xe&15}we=32767+y[me],ue(le[(xe<<4)+m[we]]),ue(d[we]),me++}return oe!=Ae&&ue(ce),te}function he(){for(var I=String.fromCharCode,Z=0;Z<256;Z++)R[Z]=I(Z)}this.encode=function(I,Z){var te=new Date().getTime();Z&&ht(Z),b=new Array,x=0,E=7,X(65496),be(),$(I.comments),ge(I.exifBuffer),fe(),ve(I.width,I.height),z(),Te();var ee=0,le=0,ce=0;x=0,E=7,this.encode.displayName="_encode_";for(var _e=I.data,we=I.width,Re=I.height,Ae=we*4,q=we*3,J,se=0,Ne,oe,me,Ee,ie,xe,Ue,Ie;se<Re;){for(J=0;J<Ae;){for(Ee=Ae*se+J,ie=Ee,xe=-1,Ue=0,Ie=0;Ie<64;Ie++)Ue=Ie>>3,xe=(Ie&7)*4,ie=Ee+Ue*Ae+xe,se+Ue>=Re&&(ie-=Ae*(se+1+Ue-Re)),J+xe>=Ae&&(ie-=J+xe-Ae+4),Ne=_e[ie++],oe=_e[ie++],me=_e[ie++],k[Ie]=(T[Ne]+T[oe+256>>0]+T[me+512>>0]>>16)-128,O[Ie]=(T[Ne+768>>0]+T[oe+1024>>0]+T[me+1280>>0]>>16)-128,S[Ie]=(T[Ne+1280>>0]+T[oe+1536>>0]+T[me+1792>>0]>>16)-128;ee=re(k,o,ee,l,u),le=re(O,a,le,c,f),ce=re(S,a,ce,c,f),J+=32}se+=8}if(E>=0){var pt=[];pt[1]=E+1,pt[0]=(1<<E+1)-1,ue(pt)}if(X(65497),typeof Ns=="undefined")return new Uint8Array(b);return Buffer.from(b);var Ot,Xt};function ht(I){if(I<=0&&(I=1),I>100&&(I=100),A!=I){var Z=0;I<50?Z=Math.floor(5e3/I):Z=Math.floor(200-I*2),Q(Z),A=I}}function yt(){var I=new Date().getTime();i||(i=50),he(),de(),ae(),ne(),ht(i);var Z=new Date().getTime()-I}yt()}typeof Ns!="undefined"?Ns.exports=tp:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].encode=tp);function tp(i,e){typeof e=="undefined"&&(e=50);var t=new Jw(e),r=t.encode(i,e);return{data:r,width:i.width,height:i.height}}});var sp=_((VI,Ka)=>{var Ya=function(){"use strict";var e=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),t=4017,r=799,n=3406,s=2276,o=1567,a=3784,l=5793,c=2896;function u(){}function f(E,k){for(var O=0,S=[],R,T,A=16;A>0&&!E[A-1];)A--;S.push({children:[],index:0});var C=S[0],L;for(R=0;R<A;R++){for(T=0;T<E[R];T++){for(C=S.pop(),C.children[C.index]=k[O];C.index>0;){if(S.length===0)throw new Error("Could not recreate Huffman Table");C=S.pop()}for(C.index++,S.push(C);S.length<=R;)S.push(L={children:[],index:0}),C.children[C.index]=L.children,C=L;O++}R+1<A&&(S.push(L={children:[],index:0}),C.children[C.index]=L.children,C=L)}return S[0].children}function d(E,k,O,S,R,T,A,C,L,P){var U=O.precision,F=O.samplesPerLine,H=O.scanLines,j=O.mcusPerLine,V=O.progressive,Y=O.maxH,Q=O.maxV,W=k,de=0,ae=0;function ne(){if(ae>0)return ae--,de>>ae&1;if(de=E[k++],de==255){var q=E[k++];if(q)throw new Error("unexpected marker: "+(de<<8|q).toString(16))}return ae=7,de>>>7}function ue(q){for(var J=q,se;(se=ne())!==null;){if(J=J[se],typeof J=="number")return J;if(typeof J!="object")throw new Error("invalid huffman sequence")}return null}function N(q){for(var J=0;q>0;){var se=ne();if(se===null)return;J=J<<1|se,q--}return J}function X(q){var J=N(q);return J>=1<<q-1?J:J+(-1<<q)+1}function ke(q,J){var se=ue(q.huffmanTableDC),Ne=se===0?0:X(se);J[0]=q.pred+=Ne;for(var oe=1;oe<64;){var me=ue(q.huffmanTableAC),Ee=me&15,ie=me>>4;if(Ee===0){if(ie<15)break;oe+=16;continue}oe+=ie;var xe=e[oe];J[xe]=X(Ee),oe++}}function be(q,J){var se=ue(q.huffmanTableDC),Ne=se===0?0:X(se)<<L;J[0]=q.pred+=Ne}function ge(q,J){J[0]|=ne()<<L}var ve=0;function fe(q,J){if(ve>0){ve--;return}for(var se=T,Ne=A;se<=Ne;){var oe=ue(q.huffmanTableAC),me=oe&15,Ee=oe>>4;if(me===0){if(Ee<15){ve=N(Ee)+(1<<Ee)-1;break}se+=16;continue}se+=Ee;var ie=e[se];J[ie]=X(me)*(1<<L),se++}}var z=0,$;function Te(q,J){for(var se=T,Ne=A,oe=0;se<=Ne;){var me=e[se],Ee=J[me]<0?-1:1;switch(z){case 0:var ie=ue(q.huffmanTableAC),xe=ie&15,oe=ie>>4;if(xe===0)oe<15?(ve=N(oe)+(1<<oe),z=4):(oe=16,z=1);else{if(xe!==1)throw new Error("invalid ACn encoding");$=X(xe),z=oe?2:3}continue;case 1:case 2:J[me]?J[me]+=(ne()<<L)*Ee:(oe--,oe===0&&(z=z==2?3:0));break;case 3:J[me]?J[me]+=(ne()<<L)*Ee:(J[me]=$<<L,z=0);break;case 4:J[me]&&(J[me]+=(ne()<<L)*Ee);break}se++}z===4&&(ve--,ve===0&&(z=0))}function re(q,J,se,Ne,oe){var me=se/j|0,Ee=se%j,ie=me*q.v+Ne,xe=Ee*q.h+oe;q.blocks[ie]===void 0&&P.tolerantDecoding||J(q,q.blocks[ie][xe])}function he(q,J,se){var Ne=se/q.blocksPerLine|0,oe=se%q.blocksPerLine;q.blocks[Ne]===void 0&&P.tolerantDecoding||J(q,q.blocks[Ne][oe])}var ht=S.length,yt,I,Z,te,ee,le;V?T===0?le=C===0?be:ge:le=C===0?fe:Te:le=ke;var ce=0,_e,we;ht==1?we=S[0].blocksPerLine*S[0].blocksPerColumn:we=j*O.mcusPerColumn,R||(R=we);for(var Re,Ae;ce<we;){for(I=0;I<ht;I++)S[I].pred=0;if(ve=0,ht==1)for(yt=S[0],ee=0;ee<R;ee++)he(yt,le,ce),ce++;else for(ee=0;ee<R;ee++){for(I=0;I<ht;I++)for(yt=S[I],Re=yt.h,Ae=yt.v,Z=0;Z<Ae;Z++)for(te=0;te<Re;te++)re(yt,le,ce,Z,te);if(ce++,ce===we)break}if(ce===we)do{if(E[k]===255&&E[k+1]!==0)break;k+=1}while(k<E.length-2);if(ae=0,_e=E[k]<<8|E[k+1],_e<65280)throw new Error("marker was not found");if(_e>=65488&&_e<=65495)k+=2;else break}return k-W}function m(E,k){var O=[],S=k.blocksPerLine,R=k.blocksPerColumn,T=S<<3,A=new Int32Array(64),C=new Uint8Array(64);function L(W,de,ae){var ne=k.quantizationTable,ue,N,X,ke,be,ge,ve,fe,z,$=ae,Te;for(Te=0;Te<64;Te++)$[Te]=W[Te]*ne[Te];for(Te=0;Te<8;++Te){var re=8*Te;if($[1+re]==0&&$[2+re]==0&&$[3+re]==0&&$[4+re]==0&&$[5+re]==0&&$[6+re]==0&&$[7+re]==0){z=l*$[0+re]+512>>10,$[0+re]=z,$[1+re]=z,$[2+re]=z,$[3+re]=z,$[4+re]=z,$[5+re]=z,$[6+re]=z,$[7+re]=z;continue}ue=l*$[0+re]+128>>8,N=l*$[4+re]+128>>8,X=$[2+re],ke=$[6+re],be=c*($[1+re]-$[7+re])+128>>8,fe=c*($[1+re]+$[7+re])+128>>8,ge=$[3+re]<<4,ve=$[5+re]<<4,z=ue-N+1>>1,ue=ue+N+1>>1,N=z,z=X*a+ke*o+128>>8,X=X*o-ke*a+128>>8,ke=z,z=be-ve+1>>1,be=be+ve+1>>1,ve=z,z=fe+ge+1>>1,ge=fe-ge+1>>1,fe=z,z=ue-ke+1>>1,ue=ue+ke+1>>1,ke=z,z=N-X+1>>1,N=N+X+1>>1,X=z,z=be*s+fe*n+2048>>12,be=be*n-fe*s+2048>>12,fe=z,z=ge*r+ve*t+2048>>12,ge=ge*t-ve*r+2048>>12,ve=z,$[0+re]=ue+fe,$[7+re]=ue-fe,$[1+re]=N+ve,$[6+re]=N-ve,$[2+re]=X+ge,$[5+re]=X-ge,$[3+re]=ke+be,$[4+re]=ke-be}for(Te=0;Te<8;++Te){var he=Te;if($[8+he]==0&&$[16+he]==0&&$[24+he]==0&&$[32+he]==0&&$[40+he]==0&&$[48+he]==0&&$[56+he]==0){z=l*ae[Te+0]+8192>>14,$[0+he]=z,$[8+he]=z,$[16+he]=z,$[24+he]=z,$[32+he]=z,$[40+he]=z,$[48+he]=z,$[56+he]=z;continue}ue=l*$[0+he]+2048>>12,N=l*$[32+he]+2048>>12,X=$[16+he],ke=$[48+he],be=c*($[8+he]-$[56+he])+2048>>12,fe=c*($[8+he]+$[56+he])+2048>>12,ge=$[24+he],ve=$[40+he],z=ue-N+1>>1,ue=ue+N+1>>1,N=z,z=X*a+ke*o+2048>>12,X=X*o-ke*a+2048>>12,ke=z,z=be-ve+1>>1,be=be+ve+1>>1,ve=z,z=fe+ge+1>>1,ge=fe-ge+1>>1,fe=z,z=ue-ke+1>>1,ue=ue+ke+1>>1,ke=z,z=N-X+1>>1,N=N+X+1>>1,X=z,z=be*s+fe*n+2048>>12,be=be*n-fe*s+2048>>12,fe=z,z=ge*r+ve*t+2048>>12,ge=ge*t-ve*r+2048>>12,ve=z,$[0+he]=ue+fe,$[56+he]=ue-fe,$[8+he]=N+ve,$[48+he]=N-ve,$[16+he]=X+ge,$[40+he]=X-ge,$[24+he]=ke+be,$[32+he]=ke-be}for(Te=0;Te<64;++Te){var ht=128+($[Te]+8>>4);de[Te]=ht<0?0:ht>255?255:ht}}x(T*R*8);for(var P,U,F=0;F<R;F++){var H=F<<3;for(P=0;P<8;P++)O.push(new Uint8Array(T));for(var j=0;j<S;j++){L(k.blocks[F][j],C,A);var V=0,Y=j<<3;for(U=0;U<8;U++){var Q=O[H+U];for(P=0;P<8;P++)Q[Y+P]=C[V++]}}}return O}function g(E){return E<0?0:E>255?255:E}u.prototype={load:function(k){var O=new XMLHttpRequest;O.open("GET",k,!0),O.responseType="arraybuffer",O.onload=function(){var S=new Uint8Array(O.response||O.mozResponseArrayBuffer);this.parse(S),this.onload&&this.onload()}.bind(this),O.send(null)},parse:function(k){var O=this.opts.maxResolutionInMP*1e3*1e3,S=0,R=k.length;function T(){var ie=k[S]<<8|k[S+1];return S+=2,ie}function A(){var ie=T(),xe=k.subarray(S,S+ie-2);return S+=xe.length,xe}function C(ie){var xe=1,Ue=1,Ie,pt;for(pt in ie.components)ie.components.hasOwnProperty(pt)&&(Ie=ie.components[pt],xe<Ie.h&&(xe=Ie.h),Ue<Ie.v&&(Ue=Ie.v));var Ot=Math.ceil(ie.samplesPerLine/8/xe),Xt=Math.ceil(ie.scanLines/8/Ue);for(pt in ie.components)if(ie.components.hasOwnProperty(pt)){Ie=ie.components[pt];var hi=Math.ceil(Math.ceil(ie.samplesPerLine/8)*Ie.h/xe),pi=Math.ceil(Math.ceil(ie.scanLines/8)*Ie.v/Ue),Bi=Ot*Ie.h,Ri=Xt*Ie.v,hr=Ri*Bi,Pi=[];x(hr*256);for(var Mi=0;Mi<Ri;Mi++){for(var Di=[],Fi=0;Fi<Bi;Fi++)Di.push(new Int32Array(64));Pi.push(Di)}Ie.blocksPerLine=hi,Ie.blocksPerColumn=pi,Ie.blocks=Pi}ie.maxH=xe,ie.maxV=Ue,ie.mcusPerLine=Ot,ie.mcusPerColumn=Xt}var L=null,P=null,U=null,F,H,j=[],V=[],Y=[],Q=[],W=T(),de=-1;if(this.comments=[],W!=65496)throw new Error("SOI not found");for(W=T();W!=65497;){var ae,ne,ue;switch(W){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var N=A();if(W===65534){var X=String.fromCharCode.apply(null,N);this.comments.push(X)}W===65504&&N[0]===74&&N[1]===70&&N[2]===73&&N[3]===70&&N[4]===0&&(L={version:{major:N[5],minor:N[6]},densityUnits:N[7],xDensity:N[8]<<8|N[9],yDensity:N[10]<<8|N[11],thumbWidth:N[12],thumbHeight:N[13],thumbData:N.subarray(14,14+3*N[12]*N[13])}),W===65505&&N[0]===69&&N[1]===120&&N[2]===105&&N[3]===102&&N[4]===0&&(this.exifBuffer=N.subarray(5,N.length)),W===65518&&N[0]===65&&N[1]===100&&N[2]===111&&N[3]===98&&N[4]===101&&N[5]===0&&(P={version:N[6],flags0:N[7]<<8|N[8],flags1:N[9]<<8|N[10],transformCode:N[11]});break;case 65499:for(var ke=T(),be=ke+S-2;S<be;){var ge=k[S++];x(256);var ve=new Int32Array(64);if(ge>>4===0)for(ne=0;ne<64;ne++){var fe=e[ne];ve[fe]=k[S++]}else if(ge>>4===1)for(ne=0;ne<64;ne++){var fe=e[ne];ve[fe]=T()}else throw new Error("DQT: invalid table spec");j[ge&15]=ve}break;case 65472:case 65473:case 65474:T(),F={},F.extended=W===65473,F.progressive=W===65474,F.precision=k[S++],F.scanLines=T(),F.samplesPerLine=T(),F.components={},F.componentsOrder=[];var z=F.scanLines*F.samplesPerLine;if(z>O){var $=Math.ceil((z-O)/1e6);throw new Error(`maxResolutionInMP limit exceeded by ${$}MP`)}var Te=k[S++],re,he=0,ht=0;for(ae=0;ae<Te;ae++){re=k[S];var yt=k[S+1]>>4,I=k[S+1]&15,Z=k[S+2];if(yt<=0||I<=0)throw new Error("Invalid sampling factor, expected values above 0");F.componentsOrder.push(re),F.components[re]={h:yt,v:I,quantizationIdx:Z},S+=3}C(F),V.push(F);break;case 65476:var te=T();for(ae=2;ae<te;){var ee=k[S++],le=new Uint8Array(16),ce=0;for(ne=0;ne<16;ne++,S++)ce+=le[ne]=k[S];x(16+ce);var _e=new Uint8Array(ce);for(ne=0;ne<ce;ne++,S++)_e[ne]=k[S];ae+=17+ce,(ee>>4===0?Q:Y)[ee&15]=f(le,_e)}break;case 65501:T(),H=T();break;case 65500:T(),T();break;case 65498:var we=T(),Re=k[S++],Ae=[],q;for(ae=0;ae<Re;ae++){q=F.components[k[S++]];var J=k[S++];q.huffmanTableDC=Q[J>>4],q.huffmanTableAC=Y[J&15],Ae.push(q)}var se=k[S++],Ne=k[S++],oe=k[S++],me=d(k,S,F,Ae,H,se,Ne,oe>>4,oe&15,this.opts);S+=me;break;case 65535:k[S]!==255&&S--;break;default:if(k[S-3]==255&&k[S-2]>=192&&k[S-2]<=254){S-=3;break}else if(W===224||W==225){if(de!==-1)throw new Error(`first unknown JPEG marker at offset ${de.toString(16)}, second unknown JPEG marker ${W.toString(16)} at offset ${(S-1).toString(16)}`);de=S-1;let ie=T();if(k[S+ie-2]===255){S+=ie-2;break}}throw new Error("unknown JPEG marker "+W.toString(16))}W=T()}if(V.length!=1)throw new Error("only single frame JPEGs supported");for(var ae=0;ae<V.length;ae++){var Ee=V[ae].components;for(var ne in Ee)Ee[ne].quantizationTable=j[Ee[ne].quantizationIdx],delete Ee[ne].quantizationIdx}this.width=F.samplesPerLine,this.height=F.scanLines,this.jfif=L,this.adobe=P,this.components=[];for(var ae=0;ae<F.componentsOrder.length;ae++){var q=F.components[F.componentsOrder[ae]];this.components.push({lines:m(F,q),scaleX:q.h/F.maxH,scaleY:q.v/F.maxV})}},getData:function(k,O){var S=this.width/k,R=this.height/O,T,A,C,L,P,U,F,H,j,V,Y=0,Q,W,de,ae,ne,ue,N,X,ke,be,ge,ve=k*O*this.components.length;x(ve);var fe=new Uint8Array(ve);switch(this.components.length){case 1:for(T=this.components[0],V=0;V<O;V++)for(P=T.lines[0|V*T.scaleY*R],j=0;j<k;j++)Q=P[0|j*T.scaleX*S],fe[Y++]=Q;break;case 2:for(T=this.components[0],A=this.components[1],V=0;V<O;V++)for(P=T.lines[0|V*T.scaleY*R],U=A.lines[0|V*A.scaleY*R],j=0;j<k;j++)Q=P[0|j*T.scaleX*S],fe[Y++]=Q,Q=U[0|j*A.scaleX*S],fe[Y++]=Q;break;case 3:for(ge=!0,this.adobe&&this.adobe.transformCode?ge=!0:typeof this.opts.colorTransform!="undefined"&&(ge=!!this.opts.colorTransform),T=this.components[0],A=this.components[1],C=this.components[2],V=0;V<O;V++)for(P=T.lines[0|V*T.scaleY*R],U=A.lines[0|V*A.scaleY*R],F=C.lines[0|V*C.scaleY*R],j=0;j<k;j++)ge?(Q=P[0|j*T.scaleX*S],W=U[0|j*A.scaleX*S],de=F[0|j*C.scaleX*S],X=g(Q+1.402*(de-128)),ke=g(Q-.3441363*(W-128)-.71413636*(de-128)),be=g(Q+1.772*(W-128))):(X=P[0|j*T.scaleX*S],ke=U[0|j*A.scaleX*S],be=F[0|j*C.scaleX*S]),fe[Y++]=X,fe[Y++]=ke,fe[Y++]=be;break;case 4:if(!this.adobe)throw new Error("Unsupported color mode (4 components)");for(ge=!1,this.adobe&&this.adobe.transformCode?ge=!0:typeof this.opts.colorTransform!="undefined"&&(ge=!!this.opts.colorTransform),T=this.components[0],A=this.components[1],C=this.components[2],L=this.components[3],V=0;V<O;V++)for(P=T.lines[0|V*T.scaleY*R],U=A.lines[0|V*A.scaleY*R],F=C.lines[0|V*C.scaleY*R],H=L.lines[0|V*L.scaleY*R],j=0;j<k;j++)ge?(Q=P[0|j*T.scaleX*S],W=U[0|j*A.scaleX*S],de=F[0|j*C.scaleX*S],ae=H[0|j*L.scaleX*S],ne=255-g(Q+1.402*(de-128)),ue=255-g(Q-.3441363*(W-128)-.71413636*(de-128)),N=255-g(Q+1.772*(W-128))):(ne=P[0|j*T.scaleX*S],ue=U[0|j*A.scaleX*S],N=F[0|j*C.scaleX*S],ae=H[0|j*L.scaleX*S]),fe[Y++]=255-ne,fe[Y++]=255-ue,fe[Y++]=255-N,fe[Y++]=255-ae;break;default:throw new Error("Unsupported color mode")}return fe},copyToImageData:function(k,O){var S=k.width,R=k.height,T=k.data,A=this.getData(S,R),C=0,L=0,P,U,F,H,j,V,Y,Q,W;switch(this.components.length){case 1:for(U=0;U<R;U++)for(P=0;P<S;P++)F=A[C++],T[L++]=F,T[L++]=F,T[L++]=F,O&&(T[L++]=255);break;case 3:for(U=0;U<R;U++)for(P=0;P<S;P++)Y=A[C++],Q=A[C++],W=A[C++],T[L++]=Y,T[L++]=Q,T[L++]=W,O&&(T[L++]=255);break;case 4:for(U=0;U<R;U++)for(P=0;P<S;P++)j=A[C++],V=A[C++],F=A[C++],H=A[C++],Y=255-g(j*(1-H/255)+H),Q=255-g(V*(1-H/255)+H),W=255-g(F*(1-H/255)+H),T[L++]=Y,T[L++]=Q,T[L++]=W,O&&(T[L++]=255);break;default:throw new Error("Unsupported color mode")}}};var y=0,b=0;function x(E=0){var k=y+E;if(k>b){var O=Math.ceil((k-b)/1024/1024);throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${O}MB`)}y=k}return u.resetMaxMemoryUsage=function(E){y=0,b=E},u.getBytesAllocated=function(){return y},u.requestMemoryAllocation=x,u}();typeof Ka!="undefined"?Ka.exports=np:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].decode=np);function np(i,e={}){var t={colorTransform:void 0,useTArray:!1,formatAsRGBA:!0,tolerantDecoding:!0,maxResolutionInMP:100,maxMemoryUsageInMB:512},r={...t,...e},n=new Uint8Array(i),s=new Ya;s.opts=r,Ya.resetMaxMemoryUsage(r.maxMemoryUsageInMB*1024*1024),s.parse(n);var o=r.formatAsRGBA?4:3,a=s.width*s.height*o;try{Ya.requestMemoryAllocation(a);var l={width:s.width,height:s.height,exifBuffer:s.exifBuffer,data:r.useTArray?new Uint8Array(a):Buffer.alloc(a)};s.comments.length>0&&(l.comments=s.comments)}catch(c){throw c instanceof RangeError?new Error("Could not allocate enough memory for the image. Required: "+a):c instanceof ReferenceError&&c.message==="Buffer is not defined"?new Error("Buffer is not globally defined in this environment. Consider setting useTArray to true"):c}return s.copyToImageData(l,r.formatAsRGBA),l}});var ap=_((HI,op)=>{var Zw=rp(),Qw=sp();op.exports={encode:Zw,decode:Qw}});var cp=_((GI,lp)=>{"use strict";function Ls(){this._types=Object.create(null),this._extensions=Object.create(null);for(let i=0;i<arguments.length;i++)this.define(arguments[i]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}Ls.prototype.define=function(i,e){for(let t in i){let r=i[t].map(function(n){return n.toLowerCase()});t=t.toLowerCase();for(let n=0;n<r.length;n++){let s=r[n];if(s[0]!=="*"){if(!e&&s in this._types)throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+t+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+t+'".');this._types[s]=t}}if(e||!this._extensions[t]){let n=r[0];this._extensions[t]=n[0]!=="*"?n:n.substr(1)}}};Ls.prototype.getType=function(i){i=String(i);let e=i.replace(/^.*[/\\]/,"").toLowerCase(),t=e.replace(/^.*\./,"").toLowerCase(),r=e.length<i.length;return(t.length<e.length-1||!r)&&this._types[t]||null};Ls.prototype.getExtension=function(i){return i=/^\s*([^;\s]*)/.test(i)&&RegExp.$1,i&&this._extensions[i.toLowerCase()]||null};lp.exports=Ls});var fp=_((WI,up)=>{up.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}});var pp=_((YI,hp)=>{hp.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}});var mp=_((KI,dp)=>{"use strict";var Xw=cp();dp.exports=new Xw(fp(),pp())});var vp=_((zI,gp)=>{gp.exports=function(i,e){for(var t=[],r=0;r<i.length;r++){var n=e(i[r],r);e1(n)?t.push.apply(t,n):t.push(n)}return t};var e1=Array.isArray||function(i){return Object.prototype.toString.call(i)==="[object Array]"}});var xp=_((JI,wp)=>{"use strict";wp.exports=bp;function bp(i,e,t){i instanceof RegExp&&(i=yp(i,t)),e instanceof RegExp&&(e=yp(e,t));var r=_p(i,e,t);return r&&{start:r[0],end:r[1],pre:t.slice(0,r[0]),body:t.slice(r[0]+i.length,r[1]),post:t.slice(r[1]+e.length)}}function yp(i,e){var t=e.match(i);return t?t[0]:null}bp.range=_p;function _p(i,e,t){var r,n,s,o,a,l=t.indexOf(i),c=t.indexOf(e,l+1),u=l;if(l>=0&&c>0){if(i===e)return[l,c];for(r=[],s=t.length;u>=0&&!a;)u==l?(r.push(u),l=t.indexOf(i,u+1)):r.length==1?a=[r.pop(),c]:(n=r.pop(),n<s&&(s=n,o=c),c=t.indexOf(e,u+1)),u=l<c&&l>=0?l:c;r.length&&(a=[s,o])}return a}});var Ip=_((ZI,Ap)=>{var t1=vp(),Sp=xp();Ap.exports=n1;var Ep="\0SLASH"+Math.random()+"\0",Op="\0OPEN"+Math.random()+"\0",Ja="\0CLOSE"+Math.random()+"\0",kp="\0COMMA"+Math.random()+"\0",Cp="\0PERIOD"+Math.random()+"\0";function za(i){return parseInt(i,10)==i?parseInt(i,10):i.charCodeAt(0)}function i1(i){return i.split("\\\\").join(Ep).split("\\{").join(Op).split("\\}").join(Ja).split("\\,").join(kp).split("\\.").join(Cp)}function r1(i){return i.split(Ep).join("\\").split(Op).join("{").split(Ja).join("}").split(kp).join(",").split(Cp).join(".")}function Tp(i){if(!i)return[""];var e=[],t=Sp("{","}",i);if(!t)return i.split(",");var r=t.pre,n=t.body,s=t.post,o=r.split(",");o[o.length-1]+="{"+n+"}";var a=Tp(s);return s.length&&(o[o.length-1]+=a.shift(),o.push.apply(o,a)),e.push.apply(e,o),e}function n1(i){return i?(i.substr(0,2)==="{}"&&(i="\\{\\}"+i.substr(2)),Sr(i1(i),!0).map(r1)):[]}function s1(i){return"{"+i+"}"}function o1(i){return/^-?0\d/.test(i)}function a1(i,e){return i<=e}function l1(i,e){return i>=e}function Sr(i,e){var t=[],r=Sp("{","}",i);if(!r||/\$$/.test(r.pre))return[i];var n=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),s=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),o=n||s,a=r.body.indexOf(",")>=0;if(!o&&!a)return r.post.match(/,(?!,).*\}/)?(i=r.pre+"{"+r.body+Ja+r.post,Sr(i)):[i];var l;if(o)l=r.body.split(/\.\./);else if(l=Tp(r.body),l.length===1&&(l=Sr(l[0],!1).map(s1),l.length===1)){var u=r.post.length?Sr(r.post,!1):[""];return u.map(function(P){return r.pre+l[0]+P})}var c=r.pre,u=r.post.length?Sr(r.post,!1):[""],f;if(o){var d=za(l[0]),m=za(l[1]),g=Math.max(l[0].length,l[1].length),y=l.length==3?Math.abs(za(l[2])):1,b=a1,x=m<d;x&&(y*=-1,b=l1);var E=l.some(o1);f=[];for(var k=d;b(k,m);k+=y){var O;if(s)O=String.fromCharCode(k),O==="\\"&&(O="");else if(O=String(k),E){var S=g-O.length;if(S>0){var R=new Array(S+1).join("0");k<0?O="-"+R+O.slice(1):O=R+O}}f.push(O)}}else f=t1(l,function(L){return Sr(L,!1)});for(var T=0;T<f.length;T++)for(var A=0;A<u.length;A++){var C=c+f[T]+u[A];(!e||o||C)&&t.push(C)}return t}});var Mp=_((QI,Pp)=>{Pp.exports=xt;xt.Minimatch=et;var ln=function(){try{return require("path")}catch{}}()||{sep:"/"};xt.sep=ln.sep;var Xa=xt.GLOBSTAR=et.GLOBSTAR={},c1=Ip(),Np={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},Za="[^/]",Qa=Za+"*?",u1="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",f1="(?:(?!(?:\\/|^)\\.).)*?",Lp=h1("().*{}+?[]^$\\!");function h1(i){return i.split("").reduce(function(e,t){return e[t]=!0,e},{})}var Bp=/\/+/;xt.filter=p1;function p1(i,e){return e=e||{},function(t,r,n){return xt(t,i,e)}}function mi(i,e){e=e||{};var t={};return Object.keys(i).forEach(function(r){t[r]=i[r]}),Object.keys(e).forEach(function(r){t[r]=e[r]}),t}xt.defaults=function(i){if(!i||typeof i!="object"||!Object.keys(i).length)return xt;var e=xt,t=function(n,s,o){return e(n,s,mi(i,o))};return t.Minimatch=function(n,s){return new e.Minimatch(n,mi(i,s))},t.Minimatch.defaults=function(n){return e.defaults(mi(i,n)).Minimatch},t.filter=function(n,s){return e.filter(n,mi(i,s))},t.defaults=function(n){return e.defaults(mi(i,n))},t.makeRe=function(n,s){return e.makeRe(n,mi(i,s))},t.braceExpand=function(n,s){return e.braceExpand(n,mi(i,s))},t.match=function(r,n,s){return e.match(r,n,mi(i,s))},t};et.defaults=function(i){return xt.defaults(i).Minimatch};function xt(i,e,t){return Rs(e),t||(t={}),!t.nocomment&&e.charAt(0)==="#"?!1:new et(e,t).match(i)}function et(i,e){if(!(this instanceof et))return new et(i,e);Rs(i),e||(e={}),i=i.trim(),!e.allowWindowsEscape&&ln.sep!=="/"&&(i=i.split(ln.sep).join("/")),this.options=e,this.set=[],this.pattern=i,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.make()}et.prototype.debug=function(){};et.prototype.make=d1;function d1(){var i=this.pattern,e=this.options;if(!e.nocomment&&i.charAt(0)==="#"){this.comment=!0;return}if(!i){this.empty=!0;return}this.parseNegate();var t=this.globSet=this.braceExpand();e.debug&&(this.debug=function(){console.error.apply(console,arguments)}),this.debug(this.pattern,t),t=this.globParts=t.map(function(r){return r.split(Bp)}),this.debug(this.pattern,t),t=t.map(function(r,n,s){return r.map(this.parse,this)},this),this.debug(this.pattern,t),t=t.filter(function(r){return r.indexOf(!1)===-1}),this.debug(this.pattern,t),this.set=t}et.prototype.parseNegate=m1;function m1(){var i=this.pattern,e=!1,t=this.options,r=0;if(!t.nonegate){for(var n=0,s=i.length;n<s&&i.charAt(n)==="!";n++)e=!e,r++;r&&(this.pattern=i.substr(r)),this.negate=e}}xt.braceExpand=function(i,e){return Rp(i,e)};et.prototype.braceExpand=Rp;function Rp(i,e){return e||(this instanceof et?e=this.options:e={}),i=typeof i=="undefined"?this.pattern:i,Rs(i),e.nobrace||!/\{(?:(?!\{).)*\}/.test(i)?[i]:c1(i)}var g1=1024*64,Rs=function(i){if(typeof i!="string")throw new TypeError("invalid pattern");if(i.length>g1)throw new TypeError("pattern is too long")};et.prototype.parse=v1;var Bs={};function v1(i,e){Rs(i);var t=this.options;if(i==="**")if(t.noglobstar)i="*";else return Xa;if(i==="")return"";var r="",n=!!t.nocase,s=!1,o=[],a=[],l,c=!1,u=-1,f=-1,d=i.charAt(0)==="."?"":t.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",m=this;function g(){if(l){switch(l){case"*":r+=Qa,n=!0;break;case"?":r+=Za,n=!0;break;default:r+="\\"+l;break}m.debug("clearStateChar %j %j",l,r),l=!1}}for(var y=0,b=i.length,x;y<b&&(x=i.charAt(y));y++){if(this.debug("%s	%s %s %j",i,y,r,x),s&&Lp[x]){r+="\\"+x,s=!1;continue}switch(x){case"/":return!1;case"\\":g(),s=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s	%s %s %j <-- stateChar",i,y,r,x),c){this.debug("  in class"),x==="!"&&y===f+1&&(x="^"),r+=x;continue}m.debug("call clearStateChar %j",l),g(),l=x,t.noext&&g();continue;case"(":if(c){r+="(";continue}if(!l){r+="\\(";continue}o.push({type:l,start:y-1,reStart:r.length,open:Np[l].open,close:Np[l].close}),r+=l==="!"?"(?:(?!(?:":"(?:",this.debug("plType %j %j",l,r),l=!1;continue;case")":if(c||!o.length){r+="\\)";continue}g(),n=!0;var E=o.pop();r+=E.close,E.type==="!"&&a.push(E),E.reEnd=r.length;continue;case"|":if(c||!o.length||s){r+="\\|",s=!1;continue}g(),r+="|";continue;case"[":if(g(),c){r+="\\"+x;continue}c=!0,f=y,u=r.length,r+=x;continue;case"]":if(y===f+1||!c){r+="\\"+x,s=!1;continue}var k=i.substring(f+1,y);try{RegExp("["+k+"]")}catch{var O=this.parse(k,Bs);r=r.substr(0,u)+"\\["+O[0]+"\\]",n=n||O[1],c=!1;continue}n=!0,c=!1,r+=x;continue;default:g(),s?s=!1:Lp[x]&&!(x==="^"&&c)&&(r+="\\"),r+=x}}for(c&&(k=i.substr(f+1),O=this.parse(k,Bs),r=r.substr(0,u)+"\\["+O[0],n=n||O[1]),E=o.pop();E;E=o.pop()){var S=r.slice(E.reStart+E.open.length);this.debug("setting tail",r,E),S=S.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(de,ae,ne){return ne||(ne="\\"),ae+ae+ne+"|"}),this.debug(`tail=%j
   %s`,S,S,E,r);var R=E.type==="*"?Qa:E.type==="?"?Za:"\\"+E.type;n=!0,r=r.slice(0,E.reStart)+R+"\\("+S}g(),s&&(r+="\\\\");var T=!1;switch(r.charAt(0)){case"[":case".":case"(":T=!0}for(var A=a.length-1;A>-1;A--){var C=a[A],L=r.slice(0,C.reStart),P=r.slice(C.reStart,C.reEnd-8),U=r.slice(C.reEnd-8,C.reEnd),F=r.slice(C.reEnd);U+=F;var H=L.split("(").length-1,j=F;for(y=0;y<H;y++)j=j.replace(/\)[+*?]?/,"");F=j;var V="";F===""&&e!==Bs&&(V="$");var Y=L+P+F+V+U;r=Y}if(r!==""&&n&&(r="(?=.)"+r),T&&(r=d+r),e===Bs)return[r,n];if(!n)return b1(i);var Q=t.nocase?"i":"";try{var W=new RegExp("^"+r+"$",Q)}catch{return new RegExp("$.")}return W._glob=i,W._src=r,W}xt.makeRe=function(i,e){return new et(i,e||{}).makeRe()};et.prototype.makeRe=y1;function y1(){if(this.regexp||this.regexp===!1)return this.regexp;var i=this.set;if(!i.length)return this.regexp=!1,this.regexp;var e=this.options,t=e.noglobstar?Qa:e.dot?u1:f1,r=e.nocase?"i":"",n=i.map(function(s){return s.map(function(o){return o===Xa?t:typeof o=="string"?_1(o):o._src}).join("\\/")}).join("|");n="^(?:"+n+")$",this.negate&&(n="^(?!"+n+").*$");try{this.regexp=new RegExp(n,r)}catch{this.regexp=!1}return this.regexp}xt.match=function(i,e,t){t=t||{};var r=new et(e,t);return i=i.filter(function(n){return r.match(n)}),r.options.nonull&&!i.length&&i.push(e),i};et.prototype.match=function(e,t){if(typeof t=="undefined"&&(t=this.partial),this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&t)return!0;var r=this.options;ln.sep!=="/"&&(e=e.split(ln.sep).join("/")),e=e.split(Bp),this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var s,o;for(o=e.length-1;o>=0&&(s=e[o],!s);o--);for(o=0;o<n.length;o++){var a=n[o],l=e;r.matchBase&&a.length===1&&(l=[s]);var c=this.matchOne(l,a,t);if(c)return r.flipNegate?!0:!this.negate}return r.flipNegate?!1:this.negate};et.prototype.matchOne=function(i,e,t){var r=this.options;this.debug("matchOne",{this:this,file:i,pattern:e}),this.debug("matchOne",i.length,e.length);for(var n=0,s=0,o=i.length,a=e.length;n<o&&s<a;n++,s++){this.debug("matchOne loop");var l=e[s],c=i[n];if(this.debug(e,l,c),l===!1)return!1;if(l===Xa){this.debug("GLOBSTAR",[e,l,c]);var u=n,f=s+1;if(f===a){for(this.debug("** at the end");n<o;n++)if(i[n]==="."||i[n]===".."||!r.dot&&i[n].charAt(0)===".")return!1;return!0}for(;u<o;){var d=i[u];if(this.debug(`
globstar while`,i,u,e,f,d),this.matchOne(i.slice(u),e.slice(f),t))return this.debug("globstar found match!",u,o,d),!0;if(d==="."||d===".."||!r.dot&&d.charAt(0)==="."){this.debug("dot detected!",i,u,e,f);break}this.debug("globstar swallow a segment, and continue"),u++}return!!(t&&(this.debug(`
>>> no match, partial?`,i,u,e,f),u===o))}var m;if(typeof l=="string"?(m=c===l,this.debug("string match",l,c,m)):(m=c.match(l),this.debug("pattern match",l,c,m)),!m)return!1}if(n===o&&s===a)return!0;if(n===o)return t;if(s===a)return n===o-1&&i[n]==="";throw new Error("wtf?")};function b1(i){return i.replace(/\\(.)/g,"$1")}function _1(i){return i.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}});var tl=_((XI,Fp)=>{"use strict";var Dp=require("fs"),el;function w1(){try{return Dp.statSync("/.dockerenv"),!0}catch{return!1}}function x1(){try{return Dp.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Fp.exports=()=>(el===void 0&&(el=w1()||x1()),el)});var Up=_((e2,il)=>{"use strict";var S1=require("os"),E1=require("fs"),qp=tl(),jp=()=>{if(process.platform!=="linux")return!1;if(S1.release().toLowerCase().includes("microsoft"))return!qp();try{return E1.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!qp():!1}catch{return!1}};process.env.__IS_WSL_TEST__?il.exports=jp:il.exports=jp()});var Vp=_((t2,$p)=>{"use strict";$p.exports=(i,e,t)=>{let r=n=>Object.defineProperty(i,e,{value:n,enumerable:!0,writable:!0});return Object.defineProperty(i,e,{configurable:!0,enumerable:!0,get(){let n=t();return r(n),n},set(n){r(n)}}),i}});var Jp=_((i2,zp)=>{var O1=require("path"),k1=require("child_process"),{promises:rl,constants:Kp}=require("fs"),Ps=Up(),C1=tl(),nl=Vp(),Hp=O1.join(__dirname,"xdg-open"),{platform:Er,arch:Gp}=process,T1=(()=>{let i="/mnt/",e;return async function(){if(e)return e;let t="/etc/wsl.conf",r=!1;try{await rl.access(t,Kp.F_OK),r=!0}catch{}if(!r)return i;let n=await rl.readFile(t,{encoding:"utf8"}),s=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(n);return s?(e=s.groups.mountPoint.trim(),e=e.endsWith("/")?e:`${e}/`,e):i}})(),Wp=async(i,e)=>{let t;for(let r of i)try{return await e(r)}catch(n){t=n}throw t},Ms=async i=>{if(i={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...i},Array.isArray(i.app))return Wp(i.app,a=>Ms({...i,app:a}));let{name:e,arguments:t=[]}=i.app||{};if(t=[...t],Array.isArray(e))return Wp(e,a=>Ms({...i,app:{name:a,arguments:t}}));let r,n=[],s={};if(Er==="darwin")r="open",i.wait&&n.push("--wait-apps"),i.background&&n.push("--background"),i.newInstance&&n.push("--new"),e&&n.push("-a",e);else if(Er==="win32"||Ps&&!C1()){let a=await T1();r=Ps?`${a}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,n.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Ps||(s.windowsVerbatimArguments=!0);let l=["Start"];i.wait&&l.push("-Wait"),e?(l.push(`"\`"${e}\`""`,"-ArgumentList"),i.target&&t.unshift(i.target)):i.target&&l.push(`"${i.target}"`),t.length>0&&(t=t.map(c=>`"\`"${c}\`""`),l.push(t.join(","))),i.target=Buffer.from(l.join(" "),"utf16le").toString("base64")}else{if(e)r=e;else{let a=!__dirname||__dirname==="/",l=!1;try{await rl.access(Hp,Kp.X_OK),l=!0}catch{}r=process.versions.electron||Er==="android"||a||!l?"xdg-open":Hp}t.length>0&&n.push(...t),i.wait||(s.stdio="ignore",s.detached=!0)}i.target&&n.push(i.target),Er==="darwin"&&t.length>0&&n.push("--args",...t);let o=k1.spawn(r,n,s);return i.wait?new Promise((a,l)=>{o.once("error",l),o.once("close",c=>{if(i.allowNonzeroExitCode&&c>0){l(new Error(`Exited with code ${c}`));return}a(o)})}):(o.unref(),o)},sl=(i,e)=>{if(typeof i!="string")throw new TypeError("Expected a `target`");return Ms({...e,target:i})},A1=(i,e)=>{if(typeof i!="string")throw new TypeError("Expected a `name`");let{arguments:t=[]}=e||{};if(t!=null&&!Array.isArray(t))throw new TypeError("Expected `appArguments` as Array type");return Ms({...e,app:{name:i,arguments:t}})};function Yp(i){if(typeof i=="string"||Array.isArray(i))return i;let{[Gp]:e}=i;if(!e)throw new Error(`${Gp} is not supported`);return e}function ol({[Er]:i},{wsl:e}){if(e&&Ps)return Yp(e);if(!i)throw new Error(`${Er} is not supported`);return Yp(i)}var Ds={};nl(Ds,"chrome",()=>ol({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));nl(Ds,"firefox",()=>ol({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));nl(Ds,"edge",()=>ol({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));sl.apps=Ds;sl.openApp=A1;zp.exports=sl});var al=_((r2,Qp)=>{"use strict";var I1=require("util"),Zp=require("stream"),qt=Qp.exports=function(){Zp.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};I1.inherits(qt,Zp);qt.prototype.read=function(i,e){this._reads.push({length:Math.abs(i),allowLess:i<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))};qt.prototype.write=function(i,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let t;return Buffer.isBuffer(i)?t=i:t=Buffer.from(i,e||this._encoding),this._buffers.push(t),this._buffered+=t.length,this._process(),this._reads&&this._reads.length===0&&(this._paused=!0),this.writable&&!this._paused};qt.prototype.end=function(i,e){i&&this.write(i,e),this.writable=!1,this._buffers&&(this._buffers.length===0?this._end():(this._buffers.push(null),this._process()))};qt.prototype.destroySoon=qt.prototype.end;qt.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()};qt.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))};qt.prototype._processReadAllowingLess=function(i){this._reads.shift();let e=this._buffers[0];e.length>i.length?(this._buffered-=i.length,this._buffers[0]=e.slice(i.length),i.func.call(this,e.slice(0,i.length))):(this._buffered-=e.length,this._buffers.shift(),i.func.call(this,e))};qt.prototype._processRead=function(i){this._reads.shift();let e=0,t=0,r=Buffer.alloc(i.length);for(;e<i.length;){let n=this._buffers[t++],s=Math.min(n.length,i.length-e);n.copy(r,e,0,s),e+=s,s!==n.length&&(this._buffers[--t]=n.slice(s))}t>0&&this._buffers.splice(0,t),this._buffered-=i.length,i.func.call(this,r)};qt.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let i=this._reads[0];if(i.allowLess)this._processReadAllowingLess(i);else if(this._buffered>=i.length)this._processRead(i);else break}this._buffers&&!this.writable&&this._end()}catch(i){this.emit("error",i)}}});var cl=_(ll=>{"use strict";var gi=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];ll.getImagePasses=function(i,e){let t=[],r=i%8,n=e%8,s=(i-r)/8,o=(e-n)/8;for(let a=0;a<gi.length;a++){let l=gi[a],c=s*l.x.length,u=o*l.y.length;for(let f=0;f<l.x.length&&l.x[f]<r;f++)c++;for(let f=0;f<l.y.length&&l.y[f]<n;f++)u++;c>0&&u>0&&t.push({width:c,height:u,index:a})}return t};ll.getInterlaceIterator=function(i){return function(e,t,r){let n=e%gi[r].x.length,s=(e-n)/gi[r].x.length*8+gi[r].x[n],o=t%gi[r].y.length,a=(t-o)/gi[r].y.length*8+gi[r].y[o];return s*4+a*i*4}}});var ul=_((s2,Xp)=>{"use strict";Xp.exports=function(e,t,r){let n=e+t-r,s=Math.abs(n-e),o=Math.abs(n-t),a=Math.abs(n-r);return s<=o&&s<=a?e:o<=a?t:r}});var fl=_((o2,td)=>{"use strict";var N1=cl(),L1=ul();function ed(i,e,t){let r=i*e;return t!==8&&(r=Math.ceil(r/(8/t))),r}var Or=td.exports=function(i,e){let t=i.width,r=i.height,n=i.interlace,s=i.bpp,o=i.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],n){let a=N1.getImagePasses(t,r);for(let l=0;l<a.length;l++)this._images.push({byteWidth:ed(a[l].width,s,o),height:a[l].height,lineIndex:0})}else this._images.push({byteWidth:ed(t,s,o),height:r,lineIndex:0});o===8?this._xComparison=s:o===16?this._xComparison=s*2:this._xComparison=1};Or.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))};Or.prototype._unFilterType1=function(i,e,t){let r=this._xComparison,n=r-1;for(let s=0;s<t;s++){let o=i[1+s],a=s>n?e[s-r]:0;e[s]=o+a}};Or.prototype._unFilterType2=function(i,e,t){let r=this._lastLine;for(let n=0;n<t;n++){let s=i[1+n],o=r?r[n]:0;e[n]=s+o}};Or.prototype._unFilterType3=function(i,e,t){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<t;o++){let a=i[1+o],l=s?s[o]:0,c=o>n?e[o-r]:0,u=Math.floor((c+l)/2);e[o]=a+u}};Or.prototype._unFilterType4=function(i,e,t){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<t;o++){let a=i[1+o],l=s?s[o]:0,c=o>n?e[o-r]:0,u=o>n&&s?s[o-r]:0,f=L1(c,l,u);e[o]=a+f}};Or.prototype._reverseFilterLine=function(i){let e=i[0],t,r=this._images[this._imageIndex],n=r.byteWidth;if(e===0)t=i.slice(1,n+1);else switch(t=Buffer.alloc(n),e){case 1:this._unFilterType1(i,t,n);break;case 2:this._unFilterType2(i,t,n);break;case 3:this._unFilterType3(i,t,n);break;case 4:this._unFilterType4(i,t,n);break;default:throw new Error("Unrecognised filter type - "+e)}this.write(t),r.lineIndex++,r.lineIndex>=r.height?(this._lastLine=null,this._imageIndex++,r=this._images[this._imageIndex]):this._lastLine=t,r?this.read(r.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}});var nd=_((a2,rd)=>{"use strict";var B1=require("util"),id=al(),R1=fl(),P1=rd.exports=function(i){id.call(this);let e=[],t=this;this._filter=new R1(i,{read:this.read.bind(this),write:function(r){e.push(r)},complete:function(){t.emit("complete",Buffer.concat(e))}}),this._filter.start()};B1.inherits(P1,id)});var kr=_((l2,sd)=>{"use strict";sd.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}});var dl=_((c2,od)=>{"use strict";var hl=[];(function(){for(let i=0;i<256;i++){let e=i;for(let t=0;t<8;t++)e&1?e=3988292384^e>>>1:e=e>>>1;hl[i]=e}})();var pl=od.exports=function(){this._crc=-1};pl.prototype.write=function(i){for(let e=0;e<i.length;e++)this._crc=hl[(this._crc^i[e])&255]^this._crc>>>8;return!0};pl.prototype.crc32=function(){return this._crc^-1};pl.crc32=function(i){let e=-1;for(let t=0;t<i.length;t++)e=hl[(e^i[t])&255]^e>>>8;return e^-1}});var ml=_((u2,ad)=>{"use strict";var He=kr(),M1=dl(),ze=ad.exports=function(i,e){this._options=i,i.checkCRC=i.checkCRC!==!1,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[He.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[He.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[He.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[He.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[He.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[He.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};ze.prototype.start=function(){this.read(He.PNG_SIGNATURE.length,this._parseSignature.bind(this))};ze.prototype._parseSignature=function(i){let e=He.PNG_SIGNATURE;for(let t=0;t<e.length;t++)if(i[t]!==e[t]){this.error(new Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))};ze.prototype._parseChunkBegin=function(i){let e=i.readUInt32BE(0),t=i.readUInt32BE(4),r="";for(let s=4;s<8;s++)r+=String.fromCharCode(i[s]);let n=!!(i[4]&32);if(!this._hasIHDR&&t!==He.TYPE_IHDR){this.error(new Error("Expected IHDR on beggining"));return}if(this._crc=new M1,this._crc.write(Buffer.from(r)),this._chunks[t])return this._chunks[t](e);if(!n){this.error(new Error("Unsupported critical chunk type "+r));return}this.read(e+4,this._skipChunk.bind(this))};ze.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))};ze.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))};ze.prototype._parseChunkEnd=function(i){let e=i.readInt32BE(0),t=this._crc.crc32();if(this._options.checkCRC&&t!==e){this.error(new Error("Crc error - "+e+" - "+t));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))};ze.prototype._handleIHDR=function(i){this.read(i,this._parseIHDR.bind(this))};ze.prototype._parseIHDR=function(i){this._crc.write(i);let e=i.readUInt32BE(0),t=i.readUInt32BE(4),r=i[8],n=i[9],s=i[10],o=i[11],a=i[12];if(r!==8&&r!==4&&r!==2&&r!==1&&r!==16){this.error(new Error("Unsupported bit depth "+r));return}if(!(n in He.COLORTYPE_TO_BPP_MAP)){this.error(new Error("Unsupported color type"));return}if(s!==0){this.error(new Error("Unsupported compression method"));return}if(o!==0){this.error(new Error("Unsupported filter method"));return}if(a!==0&&a!==1){this.error(new Error("Unsupported interlace method"));return}this._colorType=n;let l=He.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:t,depth:r,interlace:!!a,palette:!!(n&He.COLORTYPE_PALETTE),color:!!(n&He.COLORTYPE_COLOR),alpha:!!(n&He.COLORTYPE_ALPHA),bpp:l,colorType:n}),this._handleChunkEnd()};ze.prototype._handlePLTE=function(i){this.read(i,this._parsePLTE.bind(this))};ze.prototype._parsePLTE=function(i){this._crc.write(i);let e=Math.floor(i.length/3);for(let t=0;t<e;t++)this._palette.push([i[t*3],i[t*3+1],i[t*3+2],255]);this.palette(this._palette),this._handleChunkEnd()};ze.prototype._handleTRNS=function(i){this.simpleTransparency(),this.read(i,this._parseTRNS.bind(this))};ze.prototype._parseTRNS=function(i){if(this._crc.write(i),this._colorType===He.COLORTYPE_PALETTE_COLOR){if(this._palette.length===0){this.error(new Error("Transparency chunk must be after palette"));return}if(i.length>this._palette.length){this.error(new Error("More transparent colors than palette size"));return}for(let e=0;e<i.length;e++)this._palette[e][3]=i[e];this.palette(this._palette)}this._colorType===He.COLORTYPE_GRAYSCALE&&this.transColor([i.readUInt16BE(0)]),this._colorType===He.COLORTYPE_COLOR&&this.transColor([i.readUInt16BE(0),i.readUInt16BE(2),i.readUInt16BE(4)]),this._handleChunkEnd()};ze.prototype._handleGAMA=function(i){this.read(i,this._parseGAMA.bind(this))};ze.prototype._parseGAMA=function(i){this._crc.write(i),this.gamma(i.readUInt32BE(0)/He.GAMMA_DIVISION),this._handleChunkEnd()};ze.prototype._handleIDAT=function(i){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-i,this._parseIDAT.bind(this,i))};ze.prototype._parseIDAT=function(i,e){if(this._crc.write(e),this._colorType===He.COLORTYPE_PALETTE_COLOR&&this._palette.length===0)throw new Error("Expected palette not found");this.inflateData(e);let t=i-e.length;t>0?this._handleIDAT(t):this._handleChunkEnd()};ze.prototype._handleIEND=function(i){this.read(i,this._parseIEND.bind(this))};ze.prototype._parseIEND=function(i){this._crc.write(i),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}});var gl=_(cd=>{"use strict";var ld=cl(),D1=[function(){},function(i,e,t,r){if(r===e.length)throw new Error("Ran out of data");let n=e[r];i[t]=n,i[t+1]=n,i[t+2]=n,i[t+3]=255},function(i,e,t,r){if(r+1>=e.length)throw new Error("Ran out of data");let n=e[r];i[t]=n,i[t+1]=n,i[t+2]=n,i[t+3]=e[r+1]},function(i,e,t,r){if(r+2>=e.length)throw new Error("Ran out of data");i[t]=e[r],i[t+1]=e[r+1],i[t+2]=e[r+2],i[t+3]=255},function(i,e,t,r){if(r+3>=e.length)throw new Error("Ran out of data");i[t]=e[r],i[t+1]=e[r+1],i[t+2]=e[r+2],i[t+3]=e[r+3]}],F1=[function(){},function(i,e,t,r){let n=e[0];i[t]=n,i[t+1]=n,i[t+2]=n,i[t+3]=r},function(i,e,t){let r=e[0];i[t]=r,i[t+1]=r,i[t+2]=r,i[t+3]=e[1]},function(i,e,t,r){i[t]=e[0],i[t+1]=e[1],i[t+2]=e[2],i[t+3]=r},function(i,e,t){i[t]=e[0],i[t+1]=e[1],i[t+2]=e[2],i[t+3]=e[3]}];function q1(i,e){let t=[],r=0;function n(){if(r===i.length)throw new Error("Ran out of data");let s=i[r];r++;let o,a,l,c,u,f,d,m;switch(e){default:throw new Error("unrecognised depth");case 16:d=i[r],r++,t.push((s<<8)+d);break;case 4:d=s&15,m=s>>4,t.push(m,d);break;case 2:u=s&3,f=s>>2&3,d=s>>4&3,m=s>>6&3,t.push(m,d,f,u);break;case 1:o=s&1,a=s>>1&1,l=s>>2&1,c=s>>3&1,u=s>>4&1,f=s>>5&1,d=s>>6&1,m=s>>7&1,t.push(m,d,f,u,c,l,a,o);break}}return{get:function(s){for(;t.length<s;)n();let o=t.slice(0,s);return t=t.slice(s),o},resetAfterLine:function(){t.length=0},end:function(){if(r!==i.length)throw new Error("extra data found")}}}function j1(i,e,t,r,n,s){let o=i.width,a=i.height,l=i.index;for(let c=0;c<a;c++)for(let u=0;u<o;u++){let f=t(u,c,l);D1[r](e,n,f,s),s+=r}return s}function U1(i,e,t,r,n,s){let o=i.width,a=i.height,l=i.index;for(let c=0;c<a;c++){for(let u=0;u<o;u++){let f=n.get(r),d=t(u,c,l);F1[r](e,f,d,s)}n.resetAfterLine()}}cd.dataToBitMap=function(i,e){let t=e.width,r=e.height,n=e.depth,s=e.bpp,o=e.interlace,a;n!==8&&(a=q1(i,n));let l;n<=8?l=Buffer.alloc(t*r*4):l=new Uint16Array(t*r*4);let c=Math.pow(2,n)-1,u=0,f,d;if(o)f=ld.getImagePasses(t,r),d=ld.getInterlaceIterator(t,r);else{let m=0;d=function(){let g=m;return m+=4,g},f=[{width:t,height:r}]}for(let m=0;m<f.length;m++)n===8?u=j1(f[m],l,d,s,i,u):U1(f[m],l,d,s,a,c);if(n===8){if(u!==i.length)throw new Error("extra data found")}else a.end();return l}});var vl=_((h2,ud)=>{"use strict";function $1(i,e,t,r,n){let s=0;for(let o=0;o<r;o++)for(let a=0;a<t;a++){let l=n[i[s]];if(!l)throw new Error("index "+i[s]+" not in palette");for(let c=0;c<4;c++)e[s+c]=l[c];s+=4}}function V1(i,e,t,r,n){let s=0;for(let o=0;o<r;o++)for(let a=0;a<t;a++){let l=!1;if(n.length===1?n[0]===i[s]&&(l=!0):n[0]===i[s]&&n[1]===i[s+1]&&n[2]===i[s+2]&&(l=!0),l)for(let c=0;c<4;c++)e[s+c]=0;s+=4}}function H1(i,e,t,r,n){let s=255,o=Math.pow(2,n)-1,a=0;for(let l=0;l<r;l++)for(let c=0;c<t;c++){for(let u=0;u<4;u++)e[a+u]=Math.floor(i[a+u]*s/o+.5);a+=4}}ud.exports=function(i,e,t=!1){let r=e.depth,n=e.width,s=e.height,o=e.colorType,a=e.transColor,l=e.palette,c=i;return o===3?$1(i,c,n,s,l):(a&&V1(i,c,n,s,a),r!==8&&!t&&(r===16&&(c=Buffer.alloc(n*s*4)),H1(i,c,n,s,r))),c}});var pd=_((p2,hd)=>{"use strict";var G1=require("util"),yl=require("zlib"),fd=al(),W1=nd(),Y1=ml(),K1=gl(),z1=vl(),Ht=hd.exports=function(i){fd.call(this),this._parser=new Y1(i,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=i,this.writable=!0,this._parser.start()};G1.inherits(Ht,fd);Ht.prototype._handleError=function(i){this.emit("error",i),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0};Ht.prototype._inflateData=function(i){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=yl.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let t=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,r=Math.max(t,yl.Z_MIN_CHUNK);this._inflate=yl.createInflate({chunkSize:r});let n=t,s=this.emit.bind(this,"error");this._inflate.on("error",function(a){n&&s(a)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(a){n&&(a.length>n&&(a=a.slice(0,n)),n-=a.length,o(a))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(i)};Ht.prototype._handleMetaData=function(i){this._metaData=i,this._bitmapInfo=Object.create(i),this._filter=new W1(this._bitmapInfo)};Ht.prototype._handleTransColor=function(i){this._bitmapInfo.transColor=i};Ht.prototype._handlePalette=function(i){this._bitmapInfo.palette=i};Ht.prototype._simpleTransparency=function(){this._metaData.alpha=!0};Ht.prototype._headersFinished=function(){this.emit("metadata",this._metaData)};Ht.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))};Ht.prototype._complete=function(i){if(this.errord)return;let e;try{let t=K1.dataToBitMap(i,this._bitmapInfo);e=z1(t,this._bitmapInfo,this._options.skipRescale),t=null}catch(t){this._handleError(t);return}this.emit("parsed",e)}});var md=_((d2,dd)=>{"use strict";var At=kr();dd.exports=function(i,e,t,r){let n=[At.COLORTYPE_COLOR_ALPHA,At.COLORTYPE_ALPHA].indexOf(r.colorType)!==-1;if(r.colorType===r.inputColorType){let g=function(){let y=new ArrayBuffer(2);return new DataView(y).setInt16(0,256,!0),new Int16Array(y)[0]!==256}();if(r.bitDepth===8||r.bitDepth===16&&g)return i}let s=r.bitDepth!==16?i:new Uint16Array(i.buffer),o=255,a=At.COLORTYPE_TO_BPP_MAP[r.inputColorType];a===4&&!r.inputHasAlpha&&(a=3);let l=At.COLORTYPE_TO_BPP_MAP[r.colorType];r.bitDepth===16&&(o=65535,l*=2);let c=Buffer.alloc(e*t*l),u=0,f=0,d=r.bgColor||{};d.red===void 0&&(d.red=o),d.green===void 0&&(d.green=o),d.blue===void 0&&(d.blue=o);function m(){let g,y,b,x=o;switch(r.inputColorType){case At.COLORTYPE_COLOR_ALPHA:x=s[u+3],g=s[u],y=s[u+1],b=s[u+2];break;case At.COLORTYPE_COLOR:g=s[u],y=s[u+1],b=s[u+2];break;case At.COLORTYPE_ALPHA:x=s[u+1],g=s[u],y=g,b=g;break;case At.COLORTYPE_GRAYSCALE:g=s[u],y=g,b=g;break;default:throw new Error("input color type:"+r.inputColorType+" is not supported at present")}return r.inputHasAlpha&&(n||(x/=o,g=Math.min(Math.max(Math.round((1-x)*d.red+x*g),0),o),y=Math.min(Math.max(Math.round((1-x)*d.green+x*y),0),o),b=Math.min(Math.max(Math.round((1-x)*d.blue+x*b),0),o))),{red:g,green:y,blue:b,alpha:x}}for(let g=0;g<t;g++)for(let y=0;y<e;y++){let b=m(s,u);switch(r.colorType){case At.COLORTYPE_COLOR_ALPHA:case At.COLORTYPE_COLOR:r.bitDepth===8?(c[f]=b.red,c[f+1]=b.green,c[f+2]=b.blue,n&&(c[f+3]=b.alpha)):(c.writeUInt16BE(b.red,f),c.writeUInt16BE(b.green,f+2),c.writeUInt16BE(b.blue,f+4),n&&c.writeUInt16BE(b.alpha,f+6));break;case At.COLORTYPE_ALPHA:case At.COLORTYPE_GRAYSCALE:{let x=(b.red+b.green+b.blue)/3;r.bitDepth===8?(c[f]=x,n&&(c[f+1]=b.alpha)):(c.writeUInt16BE(x,f),n&&c.writeUInt16BE(b.alpha,f+2));break}default:throw new Error("unrecognised color Type "+r.colorType)}u+=a,f+=l}return c}});var yd=_((m2,vd)=>{"use strict";var gd=ul();function J1(i,e,t,r,n){for(let s=0;s<t;s++)r[n+s]=i[e+s]}function Z1(i,e,t){let r=0,n=e+t;for(let s=e;s<n;s++)r+=Math.abs(i[s]);return r}function Q1(i,e,t,r,n,s){for(let o=0;o<t;o++){let a=o>=s?i[e+o-s]:0,l=i[e+o]-a;r[n+o]=l}}function X1(i,e,t,r){let n=0;for(let s=0;s<t;s++){let o=s>=r?i[e+s-r]:0,a=i[e+s]-o;n+=Math.abs(a)}return n}function ex(i,e,t,r,n){for(let s=0;s<t;s++){let o=e>0?i[e+s-t]:0,a=i[e+s]-o;r[n+s]=a}}function tx(i,e,t){let r=0,n=e+t;for(let s=e;s<n;s++){let o=e>0?i[s-t]:0,a=i[s]-o;r+=Math.abs(a)}return r}function ix(i,e,t,r,n,s){for(let o=0;o<t;o++){let a=o>=s?i[e+o-s]:0,l=e>0?i[e+o-t]:0,c=i[e+o]-(a+l>>1);r[n+o]=c}}function rx(i,e,t,r){let n=0;for(let s=0;s<t;s++){let o=s>=r?i[e+s-r]:0,a=e>0?i[e+s-t]:0,l=i[e+s]-(o+a>>1);n+=Math.abs(l)}return n}function nx(i,e,t,r,n,s){for(let o=0;o<t;o++){let a=o>=s?i[e+o-s]:0,l=e>0?i[e+o-t]:0,c=e>0&&o>=s?i[e+o-(t+s)]:0,u=i[e+o]-gd(a,l,c);r[n+o]=u}}function sx(i,e,t,r){let n=0;for(let s=0;s<t;s++){let o=s>=r?i[e+s-r]:0,a=e>0?i[e+s-t]:0,l=e>0&&s>=r?i[e+s-(t+r)]:0,c=i[e+s]-gd(o,a,l);n+=Math.abs(c)}return n}var ox={0:J1,1:Q1,2:ex,3:ix,4:nx},ax={0:Z1,1:X1,2:tx,3:rx,4:sx};vd.exports=function(i,e,t,r,n){let s;if(!("filterType"in r)||r.filterType===-1)s=[0,1,2,3,4];else if(typeof r.filterType=="number")s=[r.filterType];else throw new Error("unrecognised filter types");r.bitDepth===16&&(n*=2);let o=e*n,a=0,l=0,c=Buffer.alloc((o+1)*t),u=s[0];for(let f=0;f<t;f++){if(s.length>1){let d=1/0;for(let m=0;m<s.length;m++){let g=ax[s[m]](i,l,o,n);g<d&&(u=s[m],d=g)}}c[a]=u,a++,ox[u](i,l,o,c,a,n),a+=o,l+=o}return c}});var bl=_((g2,bd)=>{"use strict";var rt=kr(),lx=dl(),cx=md(),ux=yd(),fx=require("zlib"),vi=bd.exports=function(i){if(this._options=i,i.deflateChunkSize=i.deflateChunkSize||32*1024,i.deflateLevel=i.deflateLevel!=null?i.deflateLevel:9,i.deflateStrategy=i.deflateStrategy!=null?i.deflateStrategy:3,i.inputHasAlpha=i.inputHasAlpha!=null?i.inputHasAlpha:!0,i.deflateFactory=i.deflateFactory||fx.createDeflate,i.bitDepth=i.bitDepth||8,i.colorType=typeof i.colorType=="number"?i.colorType:rt.COLORTYPE_COLOR_ALPHA,i.inputColorType=typeof i.inputColorType=="number"?i.inputColorType:rt.COLORTYPE_COLOR_ALPHA,[rt.COLORTYPE_GRAYSCALE,rt.COLORTYPE_COLOR,rt.COLORTYPE_COLOR_ALPHA,rt.COLORTYPE_ALPHA].indexOf(i.colorType)===-1)throw new Error("option color type:"+i.colorType+" is not supported at present");if([rt.COLORTYPE_GRAYSCALE,rt.COLORTYPE_COLOR,rt.COLORTYPE_COLOR_ALPHA,rt.COLORTYPE_ALPHA].indexOf(i.inputColorType)===-1)throw new Error("option input color type:"+i.inputColorType+" is not supported at present");if(i.bitDepth!==8&&i.bitDepth!==16)throw new Error("option bit depth:"+i.bitDepth+" is not supported at present")};vi.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}};vi.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())};vi.prototype.filterData=function(i,e,t){let r=cx(i,e,t,this._options),n=rt.COLORTYPE_TO_BPP_MAP[this._options.colorType];return ux(r,e,t,this._options,n)};vi.prototype._packChunk=function(i,e){let t=e?e.length:0,r=Buffer.alloc(t+12);return r.writeUInt32BE(t,0),r.writeUInt32BE(i,4),e&&e.copy(r,8),r.writeInt32BE(lx.crc32(r.slice(4,r.length-4)),r.length-4),r};vi.prototype.packGAMA=function(i){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(i*rt.GAMMA_DIVISION),0),this._packChunk(rt.TYPE_gAMA,e)};vi.prototype.packIHDR=function(i,e){let t=Buffer.alloc(13);return t.writeUInt32BE(i,0),t.writeUInt32BE(e,4),t[8]=this._options.bitDepth,t[9]=this._options.colorType,t[10]=0,t[11]=0,t[12]=0,this._packChunk(rt.TYPE_IHDR,t)};vi.prototype.packIDAT=function(i){return this._packChunk(rt.TYPE_IDAT,i)};vi.prototype.packIEND=function(){return this._packChunk(rt.TYPE_IEND,null)}});var Sd=_((v2,xd)=>{"use strict";var hx=require("util"),_d=require("stream"),px=kr(),dx=bl(),wd=xd.exports=function(i){_d.call(this);let e=i||{};this._packer=new dx(e),this._deflate=this._packer.createDeflate(),this.readable=!0};hx.inherits(wd,_d);wd.prototype.pack=function(i,e,t,r){this.emit("data",Buffer.from(px.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,t)),r&&this.emit("data",this._packer.packGAMA(r));let n=this._packer.filterData(i,e,t);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(s){this.emit("data",this._packer.packIDAT(s))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(n)}});var Ad=_((cn,Td)=>{"use strict";var Ed=require("assert").ok,Cr=require("zlib"),mx=require("util"),Od=require("buffer").kMaxLength;function Gi(i){if(!(this instanceof Gi))return new Gi(i);i&&i.chunkSize<Cr.Z_MIN_CHUNK&&(i.chunkSize=Cr.Z_MIN_CHUNK),Cr.Inflate.call(this,i),this._offset=this._offset===void 0?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,i&&i.maxLength!=null&&(this._maxLength=i.maxLength)}function gx(i){return new Gi(i)}function kd(i,e){e&&process.nextTick(e),i._handle&&(i._handle.close(),i._handle=null)}Gi.prototype._processChunk=function(i,e,t){if(typeof t=="function")return Cr.Inflate._processChunk.call(this,i,e,t);let r=this,n=i&&i.length,s=this._chunkSize-this._offset,o=this._maxLength,a=0,l=[],c=0,u;this.on("error",function(g){u=g});function f(g,y){if(r._hadError)return;let b=s-y;if(Ed(b>=0,"have should not go down"),b>0){let x=r._buffer.slice(r._offset,r._offset+b);if(r._offset+=b,x.length>o&&(x=x.slice(0,o)),l.push(x),c+=x.length,o-=x.length,o===0)return!1}return(y===0||r._offset>=r._chunkSize)&&(s=r._chunkSize,r._offset=0,r._buffer=Buffer.allocUnsafe(r._chunkSize)),y===0?(a+=n-g,n=g,!0):!1}Ed(this._handle,"zlib binding closed");let d;do d=this._handle.writeSync(e,i,a,n,this._buffer,this._offset,s),d=d||this._writeState;while(!this._hadError&&f(d[0],d[1]));if(this._hadError)throw u;if(c>=Od)throw kd(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+Od.toString(16)+" bytes");let m=Buffer.concat(l,c);return kd(this),m};mx.inherits(Gi,Cr.Inflate);function vx(i,e){if(typeof e=="string"&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");let t=i._finishFlushFlag;return t==null&&(t=Cr.Z_FINISH),i._processChunk(e,t)}function Cd(i,e){return vx(new Gi(e),i)}Td.exports=cn=Cd;cn.Inflate=Gi;cn.createInflate=gx;cn.inflateSync=Cd});var _l=_((y2,Nd)=>{"use strict";var Id=Nd.exports=function(i){this._buffer=i,this._reads=[]};Id.prototype.read=function(i,e){this._reads.push({length:Math.abs(i),allowLess:i<0,func:e})};Id.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let i=this._reads[0];if(this._buffer.length&&(this._buffer.length>=i.length||i.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(i.length),i.func.call(this,e.slice(0,i.length))}else break}if(this._reads.length>0)throw new Error("There are some read requests waitng on finished stream");if(this._buffer.length>0)throw new Error("unrecognised content at end of stream")}});var Bd=_(Ld=>{"use strict";var yx=_l(),bx=fl();Ld.process=function(i,e){let t=[],r=new yx(i);return new bx(e,{read:r.read.bind(r),write:function(s){t.push(s)},complete:function(){}}).start(),r.process(),Buffer.concat(t)}});var Dd=_((_2,Md)=>{"use strict";var Rd=!0,Pd=require("zlib"),_x=Ad();Pd.deflateSync||(Rd=!1);var wx=_l(),xx=Bd(),Sx=ml(),Ex=gl(),Ox=vl();Md.exports=function(i,e){if(!Rd)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let t;function r(O){t=O}let n;function s(O){n=O}function o(O){n.transColor=O}function a(O){n.palette=O}function l(){n.alpha=!0}let c;function u(O){c=O}let f=[];function d(O){f.push(O)}let m=new wx(i);if(new Sx(e,{read:m.read.bind(m),error:r,metadata:s,gamma:u,palette:a,transColor:o,inflateData:d,simpleTransparency:l}).start(),m.process(),t)throw t;let y=Buffer.concat(f);f.length=0;let b;if(n.interlace)b=Pd.inflateSync(y);else{let S=((n.width*n.bpp*n.depth+7>>3)+1)*n.height;b=_x(y,{chunkSize:S,maxLength:S})}if(y=null,!b||!b.length)throw new Error("bad png - invalid inflate data response");let x=xx.process(b,n);y=null;let E=Ex.dataToBitMap(x,n);x=null;let k=Ox(E,n,e.skipRescale);return n.data=k,n.gamma=c||0,n}});var Ud=_((w2,jd)=>{"use strict";var Fd=!0,qd=require("zlib");qd.deflateSync||(Fd=!1);var kx=kr(),Cx=bl();jd.exports=function(i,e){if(!Fd)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let t=e||{},r=new Cx(t),n=[];n.push(Buffer.from(kx.PNG_SIGNATURE)),n.push(r.packIHDR(i.width,i.height)),i.gamma&&n.push(r.packGAMA(i.gamma));let s=r.filterData(i.data,i.width,i.height),o=qd.deflateSync(s,r.getDeflateOptions());if(s=null,!o||!o.length)throw new Error("bad png - invalid compressed data response");return n.push(r.packIDAT(o)),n.push(r.packIEND()),Buffer.concat(n)}});var $d=_(wl=>{"use strict";var Tx=Dd(),Ax=Ud();wl.read=function(i,e){return Tx(i,e||{})};wl.write=function(i,e){return Ax(i,e)}});var Gd=_(Hd=>{"use strict";var Ix=require("util"),Vd=require("stream"),Nx=pd(),Lx=Sd(),Bx=$d(),lt=Hd.PNG=function(i){Vd.call(this),i=i||{},this.width=i.width|0,this.height=i.height|0,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,i.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new Nx(i),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(e){this.data=e,this.emit("parsed",e)}.bind(this)),this._packer=new Lx(i),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};Ix.inherits(lt,Vd);lt.sync=Bx;lt.prototype.pack=function(){return!this.data||!this.data.length?(this.emit("error","No data provided"),this):(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this)};lt.prototype.parse=function(i,e){if(e){let t,r;t=function(n){this.removeListener("error",r),this.data=n,e(null,this)}.bind(this),r=function(n){this.removeListener("parsed",t),e(n,null)}.bind(this),this.once("parsed",t),this.once("error",r)}return this.end(i),this};lt.prototype.write=function(i){return this._parser.write(i),!0};lt.prototype.end=function(i){this._parser.end(i)};lt.prototype._metadata=function(i){this.width=i.width,this.height=i.height,this.emit("metadata",i)};lt.prototype._gamma=function(i){this.gamma=i};lt.prototype._handleClose=function(){!this._parser.writable&&!this._packer.readable&&this.emit("close")};lt.bitblt=function(i,e,t,r,n,s,o,a){if(t|=0,r|=0,n|=0,s|=0,o|=0,a|=0,t>i.width||r>i.height||t+n>i.width||r+s>i.height)throw new Error("bitblt reading outside image");if(o>e.width||a>e.height||o+n>e.width||a+s>e.height)throw new Error("bitblt writing outside image");for(let l=0;l<s;l++)i.data.copy(e.data,(a+l)*e.width+o<<2,(r+l)*i.width+t<<2,(r+l)*i.width+t+n<<2)};lt.prototype.bitblt=function(i,e,t,r,n,s,o){return lt.bitblt(this,i,e,t,r,n,s,o),this};lt.adjustGamma=function(i){if(i.gamma){for(let e=0;e<i.height;e++)for(let t=0;t<i.width;t++){let r=i.width*e+t<<2;for(let n=0;n<3;n++){let s=i.data[r+n]/255;s=Math.pow(s,1/2.2/i.gamma),i.data[r+n]=Math.round(s*255)}}i.gamma=0}};lt.prototype.adjustGamma=function(){lt.adjustGamma(this)}});var un=_(Sl=>{var Fs=class extends Error{constructor(e,t,r){super(r),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=t,this.exitCode=e,this.nestedError=void 0}},xl=class extends Fs{constructor(e){super(1,"commander.invalidArgument",e),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}};Sl.CommanderError=Fs;Sl.InvalidArgumentError=xl});var qs=_(Ol=>{var{InvalidArgumentError:Rx}=un(),El=class{constructor(e,t){switch(this.description=t||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,e[0]){case"<":this.required=!0,this._name=e.slice(1,-1);break;case"[":this.required=!1,this._name=e.slice(1,-1);break;default:this.required=!0,this._name=e;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(e,t){return t===this.defaultValue||!Array.isArray(t)?[e]:t.concat(e)}default(e,t){return this.defaultValue=e,this.defaultValueDescription=t,this}argParser(e){return this.parseArg=e,this}choices(e){return this.argChoices=e.slice(),this.parseArg=(t,r)=>{if(!this.argChoices.includes(t))throw new Rx(`Allowed choices are ${this.argChoices.join(", ")}.`);return this.variadic?this._concatValue(t,r):t},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}};function Px(i){let e=i.name()+(i.variadic===!0?"...":"");return i.required?"<"+e+">":"["+e+"]"}Ol.Argument=El;Ol.humanReadableArgName=Px});var Tl=_(Cl=>{var{humanReadableArgName:Mx}=qs(),kl=class{constructor(){this.helpWidth=void 0,this.minWidthToWrap=40,this.sortSubcommands=!1,this.sortOptions=!1,this.showGlobalOptions=!1}prepareContext(e){var t,r;this.helpWidth=(r=(t=this.helpWidth)!=null?t:e.helpWidth)!=null?r:80}visibleCommands(e){let t=e.commands.filter(n=>!n._hidden),r=e._getHelpCommand();return r&&!r._hidden&&t.push(r),this.sortSubcommands&&t.sort((n,s)=>n.name().localeCompare(s.name())),t}compareOptions(e,t){let r=n=>n.short?n.short.replace(/^-/,""):n.long.replace(/^--/,"");return r(e).localeCompare(r(t))}visibleOptions(e){let t=e.options.filter(n=>!n.hidden),r=e._getHelpOption();if(r&&!r.hidden){let n=r.short&&e._findOption(r.short),s=r.long&&e._findOption(r.long);!n&&!s?t.push(r):r.long&&!s?t.push(e.createOption(r.long,r.description)):r.short&&!n&&t.push(e.createOption(r.short,r.description))}return this.sortOptions&&t.sort(this.compareOptions),t}visibleGlobalOptions(e){if(!this.showGlobalOptions)return[];let t=[];for(let r=e.parent;r;r=r.parent){let n=r.options.filter(s=>!s.hidden);t.push(...n)}return this.sortOptions&&t.sort(this.compareOptions),t}visibleArguments(e){return e._argsDescription&&e.registeredArguments.forEach(t=>{t.description=t.description||e._argsDescription[t.name()]||""}),e.registeredArguments.find(t=>t.description)?e.registeredArguments:[]}subcommandTerm(e){let t=e.registeredArguments.map(r=>Mx(r)).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(t?" "+t:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,t){return t.visibleCommands(e).reduce((r,n)=>Math.max(r,this.displayWidth(t.styleSubcommandTerm(t.subcommandTerm(n)))),0)}longestOptionTermLength(e,t){return t.visibleOptions(e).reduce((r,n)=>Math.max(r,this.displayWidth(t.styleOptionTerm(t.optionTerm(n)))),0)}longestGlobalOptionTermLength(e,t){return t.visibleGlobalOptions(e).reduce((r,n)=>Math.max(r,this.displayWidth(t.styleOptionTerm(t.optionTerm(n)))),0)}longestArgumentTermLength(e,t){return t.visibleArguments(e).reduce((r,n)=>Math.max(r,this.displayWidth(t.styleArgumentTerm(t.argumentTerm(n)))),0)}commandUsage(e){let t=e._name;e._aliases[0]&&(t=t+"|"+e._aliases[0]);let r="";for(let n=e.parent;n;n=n.parent)r=n.name()+" "+r;return r+t+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.summary()||e.description()}optionDescription(e){let t=[];return e.argChoices&&t.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&(e.required||e.optional||e.isBoolean()&&typeof e.defaultValue=="boolean")&&t.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),e.presetArg!==void 0&&e.optional&&t.push(`preset: ${JSON.stringify(e.presetArg)}`),e.envVar!==void 0&&t.push(`env: ${e.envVar}`),t.length>0?`${e.description} (${t.join(", ")})`:e.description}argumentDescription(e){let t=[];if(e.argChoices&&t.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&t.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),t.length>0){let r=`(${t.join(", ")})`;return e.description?`${e.description} ${r}`:r}return e.description}formatHelp(e,t){var f;let r=t.padWidth(e,t),n=(f=t.helpWidth)!=null?f:80;function s(d,m){return t.formatItem(d,r,m,t)}let o=[`${t.styleTitle("Usage:")} ${t.styleUsage(t.commandUsage(e))}`,""],a=t.commandDescription(e);a.length>0&&(o=o.concat([t.boxWrap(t.styleCommandDescription(a),n),""]));let l=t.visibleArguments(e).map(d=>s(t.styleArgumentTerm(t.argumentTerm(d)),t.styleArgumentDescription(t.argumentDescription(d))));l.length>0&&(o=o.concat([t.styleTitle("Arguments:"),...l,""]));let c=t.visibleOptions(e).map(d=>s(t.styleOptionTerm(t.optionTerm(d)),t.styleOptionDescription(t.optionDescription(d))));if(c.length>0&&(o=o.concat([t.styleTitle("Options:"),...c,""])),t.showGlobalOptions){let d=t.visibleGlobalOptions(e).map(m=>s(t.styleOptionTerm(t.optionTerm(m)),t.styleOptionDescription(t.optionDescription(m))));d.length>0&&(o=o.concat([t.styleTitle("Global Options:"),...d,""]))}let u=t.visibleCommands(e).map(d=>s(t.styleSubcommandTerm(t.subcommandTerm(d)),t.styleSubcommandDescription(t.subcommandDescription(d))));return u.length>0&&(o=o.concat([t.styleTitle("Commands:"),...u,""])),o.join(`
`)}displayWidth(e){return Wd(e).length}styleTitle(e){return e}styleUsage(e){return e.split(" ").map(t=>t==="[options]"?this.styleOptionText(t):t==="[command]"?this.styleSubcommandText(t):t[0]==="["||t[0]==="<"?this.styleArgumentText(t):this.styleCommandText(t)).join(" ")}styleCommandDescription(e){return this.styleDescriptionText(e)}styleOptionDescription(e){return this.styleDescriptionText(e)}styleSubcommandDescription(e){return this.styleDescriptionText(e)}styleArgumentDescription(e){return this.styleDescriptionText(e)}styleDescriptionText(e){return e}styleOptionTerm(e){return this.styleOptionText(e)}styleSubcommandTerm(e){return e.split(" ").map(t=>t==="[options]"?this.styleOptionText(t):t[0]==="["||t[0]==="<"?this.styleArgumentText(t):this.styleSubcommandText(t)).join(" ")}styleArgumentTerm(e){return this.styleArgumentText(e)}styleOptionText(e){return e}styleArgumentText(e){return e}styleSubcommandText(e){return e}styleCommandText(e){return e}padWidth(e,t){return Math.max(t.longestOptionTermLength(e,t),t.longestGlobalOptionTermLength(e,t),t.longestSubcommandTermLength(e,t),t.longestArgumentTermLength(e,t))}preformatted(e){return/\n[^\S\r\n]/.test(e)}formatItem(e,t,r,n){var d;let o=" ".repeat(2);if(!r)return o+e;let a=e.padEnd(t+e.length-n.displayWidth(e)),l=2,u=((d=this.helpWidth)!=null?d:80)-t-l-2,f;return u<this.minWidthToWrap||n.preformatted(r)?f=r:f=n.boxWrap(r,u).replace(/\n/g,`
`+" ".repeat(t+l)),o+a+" ".repeat(l)+f.replace(/\n/g,`
${o}`)}boxWrap(e,t){if(t<this.minWidthToWrap)return e;let r=e.split(/\r\n|\n/),n=/[\s]*[^\s]+/g,s=[];return r.forEach(o=>{let a=o.match(n);if(a===null){s.push("");return}let l=[a.shift()],c=this.displayWidth(l[0]);a.forEach(u=>{let f=this.displayWidth(u);if(c+f<=t){l.push(u),c+=f;return}s.push(l.join(""));let d=u.trimStart();l=[d],c=this.displayWidth(d)}),s.push(l.join(""))}),s.join(`
`)}};function Wd(i){let e=/\x1b\[\d*(;\d*)*m/g;return i.replace(e,"")}Cl.Help=kl;Cl.stripColor=Wd});var Ll=_(Nl=>{var{InvalidArgumentError:Dx}=un(),Al=class{constructor(e,t){this.flags=e,this.description=t||"",this.required=e.includes("<"),this.optional=e.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(e),this.mandatory=!1;let r=Fx(e);this.short=r.shortFlag,this.long=r.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.presetArg=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0,this.conflictsWith=[],this.implied=void 0}default(e,t){return this.defaultValue=e,this.defaultValueDescription=t,this}preset(e){return this.presetArg=e,this}conflicts(e){return this.conflictsWith=this.conflictsWith.concat(e),this}implies(e){let t=e;return typeof e=="string"&&(t={[e]:!0}),this.implied=Object.assign(this.implied||{},t),this}env(e){return this.envVar=e,this}argParser(e){return this.parseArg=e,this}makeOptionMandatory(e=!0){return this.mandatory=!!e,this}hideHelp(e=!0){return this.hidden=!!e,this}_concatValue(e,t){return t===this.defaultValue||!Array.isArray(t)?[e]:t.concat(e)}choices(e){return this.argChoices=e.slice(),this.parseArg=(t,r)=>{if(!this.argChoices.includes(t))throw new Dx(`Allowed choices are ${this.argChoices.join(", ")}.`);return this.variadic?this._concatValue(t,r):t},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return this.negate?Yd(this.name().replace(/^no-/,"")):Yd(this.name())}is(e){return this.short===e||this.long===e}isBoolean(){return!this.required&&!this.optional&&!this.negate}},Il=class{constructor(e){this.positiveOptions=new Map,this.negativeOptions=new Map,this.dualOptions=new Set,e.forEach(t=>{t.negate?this.negativeOptions.set(t.attributeName(),t):this.positiveOptions.set(t.attributeName(),t)}),this.negativeOptions.forEach((t,r)=>{this.positiveOptions.has(r)&&this.dualOptions.add(r)})}valueFromOption(e,t){let r=t.attributeName();if(!this.dualOptions.has(r))return!0;let n=this.negativeOptions.get(r).presetArg,s=n!==void 0?n:!1;return t.negate===(s===e)}};function Yd(i){return i.split("-").reduce((e,t)=>e+t[0].toUpperCase()+t.slice(1))}function Fx(i){let e,t,r=/^-[^-]$/,n=/^--[^-]/,s=i.split(/[ |,]+/).concat("guard");if(r.test(s[0])&&(e=s.shift()),n.test(s[0])&&(t=s.shift()),!e&&r.test(s[0])&&(e=s.shift()),!e&&n.test(s[0])&&(e=t,t=s.shift()),s[0].startsWith("-")){let o=s[0],a=`option creation failed due to '${o}' in option flags '${i}'`;throw/^-[^-][^-]/.test(o)?new Error(`${a}
- a short flag is a single dash and a single character
  - either use a single dash and a single character (for a short flag)
  - or use a double dash for a long option (and can have two, like '--ws, --workspace')`):r.test(o)?new Error(`${a}
- too many short flags`):n.test(o)?new Error(`${a}
- too many long flags`):new Error(`${a}
- unrecognised flag format`)}if(e===void 0&&t===void 0)throw new Error(`option creation failed due to no flags found in '${i}'.`);return{shortFlag:e,longFlag:t}}Nl.Option=Al;Nl.DualOptions=Il});var zd=_(Kd=>{function qx(i,e){if(Math.abs(i.length-e.length)>3)return Math.max(i.length,e.length);let t=[];for(let r=0;r<=i.length;r++)t[r]=[r];for(let r=0;r<=e.length;r++)t[0][r]=r;for(let r=1;r<=e.length;r++)for(let n=1;n<=i.length;n++){let s=1;i[n-1]===e[r-1]?s=0:s=1,t[n][r]=Math.min(t[n-1][r]+1,t[n][r-1]+1,t[n-1][r-1]+s),n>1&&r>1&&i[n-1]===e[r-2]&&i[n-2]===e[r-1]&&(t[n][r]=Math.min(t[n][r],t[n-2][r-2]+1))}return t[i.length][e.length]}function jx(i,e){if(!e||e.length===0)return"";e=Array.from(new Set(e));let t=i.startsWith("--");t&&(i=i.slice(2),e=e.map(o=>o.slice(2)));let r=[],n=3,s=.4;return e.forEach(o=>{if(o.length<=1)return;let a=qx(i,o),l=Math.max(i.length,o.length);(l-a)/l>s&&(a<n?(n=a,r=[o]):a===n&&r.push(o))}),r.sort((o,a)=>o.localeCompare(a)),t&&(r=r.map(o=>`--${o}`)),r.length>1?`
(Did you mean one of ${r.join(", ")}?)`:r.length===1?`
(Did you mean ${r[0]}?)`:""}Kd.suggestSimilar=jx});var Xd=_(Dl=>{var Ux=require("node:events").EventEmitter,Bl=require("node:child_process"),ni=require("node:path"),js=require("node:fs"),Oe=require("node:process"),{Argument:$x,humanReadableArgName:Vx}=qs(),{CommanderError:Rl}=un(),{Help:Hx,stripColor:Gx}=Tl(),{Option:Jd,DualOptions:Wx}=Ll(),{suggestSimilar:Zd}=zd(),Pl=class i extends Ux{constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!1,this.registeredArguments=[],this._args=this.registeredArguments,this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._executableDir=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._summary="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!0,this._savedState=null,this._outputConfiguration={writeOut:t=>Oe.stdout.write(t),writeErr:t=>Oe.stderr.write(t),outputError:(t,r)=>r(t),getOutHelpWidth:()=>Oe.stdout.isTTY?Oe.stdout.columns:void 0,getErrHelpWidth:()=>Oe.stderr.isTTY?Oe.stderr.columns:void 0,getOutHasColors:()=>{var t,r,n;return(n=Ml())!=null?n:Oe.stdout.isTTY&&((r=(t=Oe.stdout).hasColors)==null?void 0:r.call(t))},getErrHasColors:()=>{var t,r,n;return(n=Ml())!=null?n:Oe.stderr.isTTY&&((r=(t=Oe.stderr).hasColors)==null?void 0:r.call(t))},stripColor:t=>Gx(t)},this._hidden=!1,this._helpOption=void 0,this._addImplicitHelpCommand=void 0,this._helpCommand=void 0,this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._helpOption=e._helpOption,this._helpCommand=e._helpCommand,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}_getCommandAndAncestors(){let e=[];for(let t=this;t;t=t.parent)e.push(t);return e}command(e,t,r){let n=t,s=r;typeof n=="object"&&n!==null&&(s=n,n=null),s=s||{};let[,o,a]=e.match(/([^ ]+) *(.*)/),l=this.createCommand(o);return n&&(l.description(n),l._executableHandler=!0),s.isDefault&&(this._defaultCommandName=l._name),l._hidden=!!(s.noHelp||s.hidden),l._executableFile=s.executableFile||null,a&&l.arguments(a),this._registerCommand(l),l.parent=this,l.copyInheritedSettings(this),n?this:l}createCommand(e){return new i(e)}createHelp(){return Object.assign(new Hx,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,t){if(!e._name)throw new Error(`Command passed to .addCommand() must have a name
- specify the name in Command constructor or using .name()`);return t=t||{},t.isDefault&&(this._defaultCommandName=e._name),(t.noHelp||t.hidden)&&(e._hidden=!0),this._registerCommand(e),e.parent=this,e._checkForBrokenPassThrough(),this}createArgument(e,t){return new $x(e,t)}argument(e,t,r,n){let s=this.createArgument(e,t);return typeof r=="function"?s.default(n).argParser(r):s.default(r),this.addArgument(s),this}arguments(e){return e.trim().split(/ +/).forEach(t=>{this.argument(t)}),this}addArgument(e){let t=this.registeredArguments.slice(-1)[0];if(t&&t.variadic)throw new Error(`only the last argument can be variadic '${t.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this.registeredArguments.push(e),this}helpCommand(e,t){if(typeof e=="boolean")return this._addImplicitHelpCommand=e,this;e=e!=null?e:"help [command]";let[,r,n]=e.match(/([^ ]+) *(.*)/),s=t!=null?t:"display help for command",o=this.createCommand(r);return o.helpOption(!1),n&&o.arguments(n),s&&o.description(s),this._addImplicitHelpCommand=!0,this._helpCommand=o,this}addHelpCommand(e,t){return typeof e!="object"?(this.helpCommand(e,t),this):(this._addImplicitHelpCommand=!0,this._helpCommand=e,this)}_getHelpCommand(){var t;return((t=this._addImplicitHelpCommand)!=null?t:this.commands.length&&!this._actionHandler&&!this._findCommand("help"))?(this._helpCommand===void 0&&this.helpCommand(void 0,void 0),this._helpCommand):null}hook(e,t){let r=["preSubcommand","preAction","postAction"];if(!r.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${r.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(t):this._lifeCycleHooks[e]=[t],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=t=>{if(t.code!=="commander.executeSubCommandAsync")throw t},this}_exit(e,t,r){this._exitCallback&&this._exitCallback(new Rl(e,t,r)),Oe.exit(e)}action(e){let t=r=>{let n=this.registeredArguments.length,s=r.slice(0,n);return this._storeOptionsAsProperties?s[n]=this:s[n]=this.opts(),s.push(this),e.apply(this,s)};return this._actionHandler=t,this}createOption(e,t){return new Jd(e,t)}_callParseArg(e,t,r,n){try{return e.parseArg(t,r)}catch(s){if(s.code==="commander.invalidArgument"){let o=`${n} ${s.message}`;this.error(o,{exitCode:s.exitCode,code:s.code})}throw s}}_registerOption(e){let t=e.short&&this._findOption(e.short)||e.long&&this._findOption(e.long);if(t){let r=e.long&&this._findOption(e.long)?e.long:e.short;throw new Error(`Cannot add option '${e.flags}'${this._name&&` to command '${this._name}'`} due to conflicting flag '${r}'
-  already used by option '${t.flags}'`)}this.options.push(e)}_registerCommand(e){let t=n=>[n.name()].concat(n.aliases()),r=t(e).find(n=>this._findCommand(n));if(r){let n=t(this._findCommand(r)).join("|"),s=t(e).join("|");throw new Error(`cannot add command '${s}' as already have command '${n}'`)}this.commands.push(e)}addOption(e){this._registerOption(e);let t=e.name(),r=e.attributeName();if(e.negate){let s=e.long.replace(/^--no-/,"--");this._findOption(s)||this.setOptionValueWithSource(r,e.defaultValue===void 0?!0:e.defaultValue,"default")}else e.defaultValue!==void 0&&this.setOptionValueWithSource(r,e.defaultValue,"default");let n=(s,o,a)=>{s==null&&e.presetArg!==void 0&&(s=e.presetArg);let l=this.getOptionValue(r);s!==null&&e.parseArg?s=this._callParseArg(e,s,l,o):s!==null&&e.variadic&&(s=e._concatValue(s,l)),s==null&&(e.negate?s=!1:e.isBoolean()||e.optional?s=!0:s=""),this.setOptionValueWithSource(r,s,a)};return this.on("option:"+t,s=>{let o=`error: option '${e.flags}' argument '${s}' is invalid.`;n(s,o,"cli")}),e.envVar&&this.on("optionEnv:"+t,s=>{let o=`error: option '${e.flags}' value '${s}' from env '${e.envVar}' is invalid.`;n(s,o,"env")}),this}_optionEx(e,t,r,n,s){if(typeof t=="object"&&t instanceof Jd)throw new Error("To add an Option object use addOption() instead of option() or requiredOption()");let o=this.createOption(t,r);if(o.makeOptionMandatory(!!e.mandatory),typeof n=="function")o.default(s).argParser(n);else if(n instanceof RegExp){let a=n;n=(l,c)=>{let u=a.exec(l);return u?u[0]:c},o.default(s).argParser(n)}else o.default(n);return this.addOption(o)}option(e,t,r,n){return this._optionEx({},e,t,r,n)}requiredOption(e,t,r,n){return this._optionEx({mandatory:!0},e,t,r,n)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){return this._passThroughOptions=!!e,this._checkForBrokenPassThrough(),this}_checkForBrokenPassThrough(){if(this.parent&&this._passThroughOptions&&!this.parent._enablePositionalOptions)throw new Error(`passThroughOptions cannot be used for '${this._name}' without turning on enablePositionalOptions for parent command(s)`)}storeOptionsAsProperties(e=!0){if(this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");if(Object.keys(this._optionValues).length)throw new Error("call .storeOptionsAsProperties() before setting option values");return this._storeOptionsAsProperties=!!e,this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,t){return this.setOptionValueWithSource(e,t,void 0)}setOptionValueWithSource(e,t,r){return this._storeOptionsAsProperties?this[e]=t:this._optionValues[e]=t,this._optionValueSources[e]=r,this}getOptionValueSource(e){return this._optionValueSources[e]}getOptionValueSourceWithGlobals(e){let t;return this._getCommandAndAncestors().forEach(r=>{r.getOptionValueSource(e)!==void 0&&(t=r.getOptionValueSource(e))}),t}_prepareUserArgs(e,t){var n,s;if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");if(t=t||{},e===void 0&&t.from===void 0){(n=Oe.versions)!=null&&n.electron&&(t.from="electron");let o=(s=Oe.execArgv)!=null?s:[];(o.includes("-e")||o.includes("--eval")||o.includes("-p")||o.includes("--print"))&&(t.from="eval")}e===void 0&&(e=Oe.argv),this.rawArgs=e.slice();let r;switch(t.from){case void 0:case"node":this._scriptPath=e[1],r=e.slice(2);break;case"electron":Oe.defaultApp?(this._scriptPath=e[1],r=e.slice(2)):r=e.slice(1);break;case"user":r=e.slice(0);break;case"eval":r=e.slice(1);break;default:throw new Error(`unexpected parse option { from: '${t.from}' }`)}return!this._name&&this._scriptPath&&this.nameFromFilename(this._scriptPath),this._name=this._name||"program",r}parse(e,t){this._prepareForParse();let r=this._prepareUserArgs(e,t);return this._parseCommand([],r),this}async parseAsync(e,t){this._prepareForParse();let r=this._prepareUserArgs(e,t);return await this._parseCommand([],r),this}_prepareForParse(){this._savedState===null?this.saveStateBeforeParse():this.restoreStateBeforeParse()}saveStateBeforeParse(){this._savedState={_name:this._name,_optionValues:{...this._optionValues},_optionValueSources:{...this._optionValueSources}}}restoreStateBeforeParse(){if(this._storeOptionsAsProperties)throw new Error(`Can not call parse again when storeOptionsAsProperties is true.
- either make a new Command for each call to parse, or stop storing options as properties`);this._name=this._savedState._name,this._scriptPath=null,this.rawArgs=[],this._optionValues={...this._savedState._optionValues},this._optionValueSources={...this._savedState._optionValueSources},this.args=[],this.processedArgs=[]}_checkForMissingExecutable(e,t,r){if(js.existsSync(e))return;let n=t?`searched for local subcommand relative to directory '${t}'`:"no directory for search for local subcommand, use .executableDir() to supply a custom directory",s=`'${e}' does not exist
 - if '${r}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name or path
 - ${n}`;throw new Error(s)}_executeSubCommand(e,t){t=t.slice();let r=!1,n=[".js",".ts",".tsx",".mjs",".cjs"];function s(u,f){let d=ni.resolve(u,f);if(js.existsSync(d))return d;if(n.includes(ni.extname(f)))return;let m=n.find(g=>js.existsSync(`${d}${g}`));if(m)return`${d}${m}`}this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();let o=e._executableFile||`${this._name}-${e._name}`,a=this._executableDir||"";if(this._scriptPath){let u;try{u=js.realpathSync(this._scriptPath)}catch{u=this._scriptPath}a=ni.resolve(ni.dirname(u),a)}if(a){let u=s(a,o);if(!u&&!e._executableFile&&this._scriptPath){let f=ni.basename(this._scriptPath,ni.extname(this._scriptPath));f!==this._name&&(u=s(a,`${f}-${e._name}`))}o=u||o}r=n.includes(ni.extname(o));let l;Oe.platform!=="win32"?r?(t.unshift(o),t=Qd(Oe.execArgv).concat(t),l=Bl.spawn(Oe.argv[0],t,{stdio:"inherit"})):l=Bl.spawn(o,t,{stdio:"inherit"}):(this._checkForMissingExecutable(o,a,e._name),t.unshift(o),t=Qd(Oe.execArgv).concat(t),l=Bl.spawn(Oe.execPath,t,{stdio:"inherit"})),l.killed||["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(f=>{Oe.on(f,()=>{l.killed===!1&&l.exitCode===null&&l.kill(f)})});let c=this._exitCallback;l.on("close",u=>{u=u!=null?u:1,c?c(new Rl(u,"commander.executeSubCommandAsync","(close)")):Oe.exit(u)}),l.on("error",u=>{if(u.code==="ENOENT")this._checkForMissingExecutable(o,a,e._name);else if(u.code==="EACCES")throw new Error(`'${o}' not executable`);if(!c)Oe.exit(1);else{let f=new Rl(1,"commander.executeSubCommandAsync","(error)");f.nestedError=u,c(f)}}),this.runningCommand=l}_dispatchSubcommand(e,t,r){let n=this._findCommand(e);n||this.help({error:!0}),n._prepareForParse();let s;return s=this._chainOrCallSubCommandHook(s,n,"preSubcommand"),s=this._chainOrCall(s,()=>{if(n._executableHandler)this._executeSubCommand(n,t.concat(r));else return n._parseCommand(t,r)}),s}_dispatchHelpCommand(e){var r,n,s,o;e||this.help();let t=this._findCommand(e);return t&&!t._executableHandler&&t.help(),this._dispatchSubcommand(e,[],[(o=(s=(r=this._getHelpOption())==null?void 0:r.long)!=null?s:(n=this._getHelpOption())==null?void 0:n.short)!=null?o:"--help"])}_checkNumberOfArguments(){this.registeredArguments.forEach((e,t)=>{e.required&&this.args[t]==null&&this.missingArgument(e.name())}),!(this.registeredArguments.length>0&&this.registeredArguments[this.registeredArguments.length-1].variadic)&&this.args.length>this.registeredArguments.length&&this._excessArguments(this.args)}_processArguments(){let e=(r,n,s)=>{let o=n;if(n!==null&&r.parseArg){let a=`error: command-argument value '${n}' is invalid for argument '${r.name()}'.`;o=this._callParseArg(r,n,s,a)}return o};this._checkNumberOfArguments();let t=[];this.registeredArguments.forEach((r,n)=>{let s=r.defaultValue;r.variadic?n<this.args.length?(s=this.args.slice(n),r.parseArg&&(s=s.reduce((o,a)=>e(r,a,o),r.defaultValue))):s===void 0&&(s=[]):n<this.args.length&&(s=this.args[n],r.parseArg&&(s=e(r,s,r.defaultValue))),t[n]=s}),this.processedArgs=t}_chainOrCall(e,t){return e&&e.then&&typeof e.then=="function"?e.then(()=>t()):t()}_chainOrCallHooks(e,t){let r=e,n=[];return this._getCommandAndAncestors().reverse().filter(s=>s._lifeCycleHooks[t]!==void 0).forEach(s=>{s._lifeCycleHooks[t].forEach(o=>{n.push({hookedCommand:s,callback:o})})}),t==="postAction"&&n.reverse(),n.forEach(s=>{r=this._chainOrCall(r,()=>s.callback(s.hookedCommand,this))}),r}_chainOrCallSubCommandHook(e,t,r){let n=e;return this._lifeCycleHooks[r]!==void 0&&this._lifeCycleHooks[r].forEach(s=>{n=this._chainOrCall(n,()=>s(this,t))}),n}_parseCommand(e,t){let r=this.parseOptions(t);if(this._parseOptionsEnv(),this._parseOptionsImplied(),e=e.concat(r.operands),t=r.unknown,this.args=e.concat(t),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),t);if(this._getHelpCommand()&&e[0]===this._getHelpCommand().name())return this._dispatchHelpCommand(e[1]);if(this._defaultCommandName)return this._outputHelpIfRequested(t),this._dispatchSubcommand(this._defaultCommandName,e,t);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),this._outputHelpIfRequested(r.unknown),this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();let n=()=>{r.unknown.length>0&&this.unknownOption(r.unknown[0])},s=`command:${this.name()}`;if(this._actionHandler){n(),this._processArguments();let o;return o=this._chainOrCallHooks(o,"preAction"),o=this._chainOrCall(o,()=>this._actionHandler(this.processedArgs)),this.parent&&(o=this._chainOrCall(o,()=>{this.parent.emit(s,e,t)})),o=this._chainOrCallHooks(o,"postAction"),o}if(this.parent&&this.parent.listenerCount(s))n(),this._processArguments(),this.parent.emit(s,e,t);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,t);this.listenerCount("command:*")?this.emit("command:*",e,t):this.commands.length?this.unknownCommand():(n(),this._processArguments())}else this.commands.length?(n(),this.help({error:!0})):(n(),this._processArguments())}_findCommand(e){if(e)return this.commands.find(t=>t._name===e||t._aliases.includes(e))}_findOption(e){return this.options.find(t=>t.is(e))}_checkForMissingMandatoryOptions(){this._getCommandAndAncestors().forEach(e=>{e.options.forEach(t=>{t.mandatory&&e.getOptionValue(t.attributeName())===void 0&&e.missingMandatoryOptionValue(t)})})}_checkForConflictingLocalOptions(){let e=this.options.filter(r=>{let n=r.attributeName();return this.getOptionValue(n)===void 0?!1:this.getOptionValueSource(n)!=="default"});e.filter(r=>r.conflictsWith.length>0).forEach(r=>{let n=e.find(s=>r.conflictsWith.includes(s.attributeName()));n&&this._conflictingOption(r,n)})}_checkForConflictingOptions(){this._getCommandAndAncestors().forEach(e=>{e._checkForConflictingLocalOptions()})}parseOptions(e){let t=[],r=[],n=t,s=e.slice();function o(l){return l.length>1&&l[0]==="-"}let a=null;for(;s.length;){let l=s.shift();if(l==="--"){n===r&&n.push(l),n.push(...s);break}if(a&&!o(l)){this.emit(`option:${a.name()}`,l);continue}if(a=null,o(l)){let c=this._findOption(l);if(c){if(c.required){let u=s.shift();u===void 0&&this.optionMissingArgument(c),this.emit(`option:${c.name()}`,u)}else if(c.optional){let u=null;s.length>0&&!o(s[0])&&(u=s.shift()),this.emit(`option:${c.name()}`,u)}else this.emit(`option:${c.name()}`);a=c.variadic?c:null;continue}}if(l.length>2&&l[0]==="-"&&l[1]!=="-"){let c=this._findOption(`-${l[1]}`);if(c){c.required||c.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${c.name()}`,l.slice(2)):(this.emit(`option:${c.name()}`),s.unshift(`-${l.slice(2)}`));continue}}if(/^--[^=]+=/.test(l)){let c=l.indexOf("="),u=this._findOption(l.slice(0,c));if(u&&(u.required||u.optional)){this.emit(`option:${u.name()}`,l.slice(c+1));continue}}if(o(l)&&(n=r),(this._enablePositionalOptions||this._passThroughOptions)&&t.length===0&&r.length===0){if(this._findCommand(l)){t.push(l),s.length>0&&r.push(...s);break}else if(this._getHelpCommand()&&l===this._getHelpCommand().name()){t.push(l),s.length>0&&t.push(...s);break}else if(this._defaultCommandName){r.push(l),s.length>0&&r.push(...s);break}}if(this._passThroughOptions){n.push(l),s.length>0&&n.push(...s);break}n.push(l)}return{operands:t,unknown:r}}opts(){if(this._storeOptionsAsProperties){let e={},t=this.options.length;for(let r=0;r<t;r++){let n=this.options[r].attributeName();e[n]=n===this._versionOptionName?this._version:this[n]}return e}return this._optionValues}optsWithGlobals(){return this._getCommandAndAncestors().reduce((e,t)=>Object.assign(e,t.opts()),{})}error(e,t){this._outputConfiguration.outputError(`${e}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0}));let r=t||{},n=r.exitCode||1,s=r.code||"commander.error";this._exit(n,s,e)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in Oe.env){let t=e.attributeName();(this.getOptionValue(t)===void 0||["default","config","env"].includes(this.getOptionValueSource(t)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,Oe.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}_parseOptionsImplied(){let e=new Wx(this.options),t=r=>this.getOptionValue(r)!==void 0&&!["default","implied"].includes(this.getOptionValueSource(r));this.options.filter(r=>r.implied!==void 0&&t(r.attributeName())&&e.valueFromOption(this.getOptionValue(r.attributeName()),r)).forEach(r=>{Object.keys(r.implied).filter(n=>!t(n)).forEach(n=>{this.setOptionValueWithSource(n,r.implied[n],"implied")})})}missingArgument(e){let t=`error: missing required argument '${e}'`;this.error(t,{code:"commander.missingArgument"})}optionMissingArgument(e){let t=`error: option '${e.flags}' argument missing`;this.error(t,{code:"commander.optionMissingArgument"})}missingMandatoryOptionValue(e){let t=`error: required option '${e.flags}' not specified`;this.error(t,{code:"commander.missingMandatoryOptionValue"})}_conflictingOption(e,t){let r=o=>{let a=o.attributeName(),l=this.getOptionValue(a),c=this.options.find(f=>f.negate&&a===f.attributeName()),u=this.options.find(f=>!f.negate&&a===f.attributeName());return c&&(c.presetArg===void 0&&l===!1||c.presetArg!==void 0&&l===c.presetArg)?c:u||o},n=o=>{let a=r(o),l=a.attributeName();return this.getOptionValueSource(l)==="env"?`environment variable '${a.envVar}'`:`option '${a.flags}'`},s=`error: ${n(e)} cannot be used with ${n(t)}`;this.error(s,{code:"commander.conflictingOption"})}unknownOption(e){if(this._allowUnknownOption)return;let t="";if(e.startsWith("--")&&this._showSuggestionAfterError){let n=[],s=this;do{let o=s.createHelp().visibleOptions(s).filter(a=>a.long).map(a=>a.long);n=n.concat(o),s=s.parent}while(s&&!s._enablePositionalOptions);t=Zd(e,n)}let r=`error: unknown option '${e}'${t}`;this.error(r,{code:"commander.unknownOption"})}_excessArguments(e){if(this._allowExcessArguments)return;let t=this.registeredArguments.length,r=t===1?"":"s",s=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${t} argument${r} but got ${e.length}.`;this.error(s,{code:"commander.excessArguments"})}unknownCommand(){let e=this.args[0],t="";if(this._showSuggestionAfterError){let n=[];this.createHelp().visibleCommands(this).forEach(s=>{n.push(s.name()),s.alias()&&n.push(s.alias())}),t=Zd(e,n)}let r=`error: unknown command '${e}'${t}`;this.error(r,{code:"commander.unknownCommand"})}version(e,t,r){if(e===void 0)return this._version;this._version=e,t=t||"-V, --version",r=r||"output the version number";let n=this.createOption(t,r);return this._versionOptionName=n.attributeName(),this._registerOption(n),this.on("option:"+n.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,t){return e===void 0&&t===void 0?this._description:(this._description=e,t&&(this._argsDescription=t),this)}summary(e){return e===void 0?this._summary:(this._summary=e,this)}alias(e){var n;if(e===void 0)return this._aliases[0];let t=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(t=this.commands[this.commands.length-1]),e===t._name)throw new Error("Command alias can't be the same as its name");let r=(n=this.parent)==null?void 0:n._findCommand(e);if(r){let s=[r.name()].concat(r.aliases()).join("|");throw new Error(`cannot add alias '${e}' to command '${this.name()}' as already have command '${s}'`)}return t._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(t=>this.alias(t)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;let t=this.registeredArguments.map(r=>Vx(r));return[].concat(this.options.length||this._helpOption!==null?"[options]":[],this.commands.length?"[command]":[],this.registeredArguments.length?t:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}nameFromFilename(e){return this._name=ni.basename(e,ni.extname(e)),this}executableDir(e){return e===void 0?this._executableDir:(this._executableDir=e,this)}helpInformation(e){let t=this.createHelp(),r=this._getOutputContext(e);t.prepareContext({error:r.error,helpWidth:r.helpWidth,outputHasColors:r.hasColors});let n=t.formatHelp(this,t);return r.hasColors?n:this._outputConfiguration.stripColor(n)}_getOutputContext(e){e=e||{};let t=!!e.error,r,n,s;return t?(r=a=>this._outputConfiguration.writeErr(a),n=this._outputConfiguration.getErrHasColors(),s=this._outputConfiguration.getErrHelpWidth()):(r=a=>this._outputConfiguration.writeOut(a),n=this._outputConfiguration.getOutHasColors(),s=this._outputConfiguration.getOutHelpWidth()),{error:t,write:a=>(n||(a=this._outputConfiguration.stripColor(a)),r(a)),hasColors:n,helpWidth:s}}outputHelp(e){var o;let t;typeof e=="function"&&(t=e,e=void 0);let r=this._getOutputContext(e),n={error:r.error,write:r.write,command:this};this._getCommandAndAncestors().reverse().forEach(a=>a.emit("beforeAllHelp",n)),this.emit("beforeHelp",n);let s=this.helpInformation({error:r.error});if(t&&(s=t(s),typeof s!="string"&&!Buffer.isBuffer(s)))throw new Error("outputHelp callback must return a string or a Buffer");r.write(s),(o=this._getHelpOption())!=null&&o.long&&this.emit(this._getHelpOption().long),this.emit("afterHelp",n),this._getCommandAndAncestors().forEach(a=>a.emit("afterAllHelp",n))}helpOption(e,t){var r;return typeof e=="boolean"?(e?this._helpOption=(r=this._helpOption)!=null?r:void 0:this._helpOption=null,this):(e=e!=null?e:"-h, --help",t=t!=null?t:"display help for command",this._helpOption=this.createOption(e,t),this)}_getHelpOption(){return this._helpOption===void 0&&this.helpOption(void 0,void 0),this._helpOption}addHelpOption(e){return this._helpOption=e,this}help(e){var r;this.outputHelp(e);let t=Number((r=Oe.exitCode)!=null?r:0);t===0&&e&&typeof e!="function"&&e.error&&(t=1),this._exit(t,"commander.help","(outputHelp)")}addHelpText(e,t){let r=["beforeAll","before","after","afterAll"];if(!r.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${r.join("', '")}'`);let n=`${e}Help`;return this.on(n,s=>{let o;typeof t=="function"?o=t({error:s.error,command:s.command}):o=t,o&&s.write(`${o}
`)}),this}_outputHelpIfRequested(e){let t=this._getHelpOption();t&&e.find(n=>t.is(n))&&(this.outputHelp(),this._exit(0,"commander.helpDisplayed","(outputHelp)"))}};function Qd(i){return i.map(e=>{if(!e.startsWith("--inspect"))return e;let t,r="127.0.0.1",n="9229",s;return(s=e.match(/^(--inspect(-brk)?)$/))!==null?t=s[1]:(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(t=s[1],/^\d+$/.test(s[3])?n=s[3]:r=s[3]):(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(t=s[1],r=s[3],n=s[4]),t&&n!=="0"?`${t}=${r}:${parseInt(n)+1}`:e})}function Ml(){if(Oe.env.NO_COLOR||Oe.env.FORCE_COLOR==="0"||Oe.env.FORCE_COLOR==="false")return!1;if(Oe.env.FORCE_COLOR||Oe.env.CLICOLOR_FORCE!==void 0)return!0}Dl.Command=Pl;Dl.useColor=Ml});var rm=_(It=>{var{Argument:em}=qs(),{Command:Fl}=Xd(),{CommanderError:Yx,InvalidArgumentError:tm}=un(),{Help:Kx}=Tl(),{Option:im}=Ll();It.program=new Fl;It.createCommand=i=>new Fl(i);It.createOption=(i,e)=>new im(i,e);It.createArgument=(i,e)=>new em(i,e);It.Command=Fl;It.Option=im;It.Argument=em;It.Help=Kx;It.CommanderError=Yx;It.InvalidArgumentError=tm;It.InvalidOptionArgumentError=tm});var lm=_((om,am)=>{om=am.exports=Tr;function Tr(i,e){if(this.stream=e.stream||process.stderr,typeof e=="number"){var t=e;e={},e.total=t}else{if(e=e||{},typeof i!="string")throw new Error("format required");if(typeof e.total!="number")throw new Error("total required")}this.fmt=i,this.curr=e.curr||0,this.total=e.total,this.width=e.width||this.total,this.clear=e.clear,this.chars={complete:e.complete||"=",incomplete:e.incomplete||"-",head:e.head||e.complete||"="},this.renderThrottle=e.renderThrottle!==0?e.renderThrottle||16:0,this.lastRender=-1/0,this.callback=e.callback||function(){},this.tokens={},this.lastDraw=""}Tr.prototype.tick=function(i,e){if(i!==0&&(i=i||1),typeof i=="object"&&(e=i,i=1),e&&(this.tokens=e),this.curr==0&&(this.start=new Date),this.curr+=i,this.render(),this.curr>=this.total){this.render(void 0,!0),this.complete=!0,this.terminate(),this.callback(this);return}};Tr.prototype.render=function(i,e){if(e=e!==void 0?e:!1,i&&(this.tokens=i),!!this.stream.isTTY){var t=Date.now(),r=t-this.lastRender;if(!(!e&&r<this.renderThrottle)){this.lastRender=t;var n=this.curr/this.total;n=Math.min(Math.max(n,0),1);var s=Math.floor(n*100),o,a,l,c=new Date-this.start,u=s==100?0:c*(this.total/this.curr-1),f=this.curr/(c/1e3),d=this.fmt.replace(":current",this.curr).replace(":total",this.total).replace(":elapsed",isNaN(c)?"0.0":(c/1e3).toFixed(1)).replace(":eta",isNaN(u)||!isFinite(u)?"0.0":(u/1e3).toFixed(1)).replace(":percent",s.toFixed(0)+"%").replace(":rate",Math.round(f)),m=Math.max(0,this.stream.columns-d.replace(":bar","").length);m&&process.platform==="win32"&&(m=m-1);var g=Math.min(this.width,m);if(l=Math.round(g*n),a=Array(Math.max(0,l+1)).join(this.chars.complete),o=Array(Math.max(0,g-l+1)).join(this.chars.incomplete),l>0&&(a=a.slice(0,-1)+this.chars.head),d=d.replace(":bar",a+o),this.tokens)for(var y in this.tokens)d=d.replace(":"+y,this.tokens[y]);this.lastDraw!==d&&(this.stream.cursorTo(0),this.stream.write(d),this.stream.clearLine(1),this.lastDraw=d)}}};Tr.prototype.update=function(i,e){var t=Math.floor(i*this.total),r=t-this.curr;this.tick(r,e)};Tr.prototype.interrupt=function(i){this.stream.clearLine(),this.stream.cursorTo(0),this.stream.write(i),this.stream.write(`
`),this.stream.write(this.lastDraw)};Tr.prototype.terminate=function(){this.clear?this.stream.clearLine&&(this.stream.clearLine(),this.stream.cursorTo(0)):this.stream.write(`
`)}});var um=_(($2,cm)=>{cm.exports=lm()});var dm=_(si=>{"use strict";Object.defineProperty(si,"__esModule",{value:!0});var fm=require("buffer"),Wi={INVALID_ENCODING:"Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.",INVALID_SMARTBUFFER_SIZE:"Invalid size provided. Size must be a valid integer greater than zero.",INVALID_SMARTBUFFER_BUFFER:"Invalid Buffer provided in SmartBufferOptions.",INVALID_SMARTBUFFER_OBJECT:"Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.",INVALID_OFFSET:"An invalid offset value was provided.",INVALID_OFFSET_NON_NUMBER:"An invalid offset value was provided. A numeric value is required.",INVALID_LENGTH:"An invalid length value was provided.",INVALID_LENGTH_NON_NUMBER:"An invalid length value was provived. A numeric value is required.",INVALID_TARGET_OFFSET:"Target offset is beyond the bounds of the internal SmartBuffer data.",INVALID_TARGET_LENGTH:"Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.",INVALID_READ_BEYOND_BOUNDS:"Attempted to read beyond the bounds of the managed data.",INVALID_WRITE_BEYOND_BOUNDS:"Attempted to write beyond the bounds of the managed data."};si.ERRORS=Wi;function zx(i){if(!fm.Buffer.isEncoding(i))throw new Error(Wi.INVALID_ENCODING)}si.checkEncoding=zx;function hm(i){return typeof i=="number"&&isFinite(i)&&Xx(i)}si.isFiniteInteger=hm;function pm(i,e){if(typeof i=="number"){if(!hm(i)||i<0)throw new Error(e?Wi.INVALID_OFFSET:Wi.INVALID_LENGTH)}else throw new Error(e?Wi.INVALID_OFFSET_NON_NUMBER:Wi.INVALID_LENGTH_NON_NUMBER)}function Jx(i){pm(i,!1)}si.checkLengthValue=Jx;function Zx(i){pm(i,!0)}si.checkOffsetValue=Zx;function Qx(i,e){if(i<0||i>e.length)throw new Error(Wi.INVALID_TARGET_OFFSET)}si.checkTargetOffset=Qx;function Xx(i){return typeof i=="number"&&isFinite(i)&&Math.floor(i)===i}function eS(i){if(typeof BigInt=="undefined")throw new Error("Platform does not support JS BigInt type.");if(typeof fm.Buffer.prototype[i]=="undefined")throw new Error(`Platform does not support Buffer.prototype.${i}.`)}si.bigIntAndBufferInt64Check=eS});var gm=_(jl=>{"use strict";Object.defineProperty(jl,"__esModule",{value:!0});var pe=dm(),mm=4096,tS="utf8",ql=class i{constructor(e){if(this.length=0,this._encoding=tS,this._writeOffset=0,this._readOffset=0,i.isSmartBufferOptions(e))if(e.encoding&&(pe.checkEncoding(e.encoding),this._encoding=e.encoding),e.size)if(pe.isFiniteInteger(e.size)&&e.size>0)this._buff=Buffer.allocUnsafe(e.size);else throw new Error(pe.ERRORS.INVALID_SMARTBUFFER_SIZE);else if(e.buff)if(Buffer.isBuffer(e.buff))this._buff=e.buff,this.length=e.buff.length;else throw new Error(pe.ERRORS.INVALID_SMARTBUFFER_BUFFER);else this._buff=Buffer.allocUnsafe(mm);else{if(typeof e!="undefined")throw new Error(pe.ERRORS.INVALID_SMARTBUFFER_OBJECT);this._buff=Buffer.allocUnsafe(mm)}}static fromSize(e,t){return new this({size:e,encoding:t})}static fromBuffer(e,t){return new this({buff:e,encoding:t})}static fromOptions(e){return new this(e)}static isSmartBufferOptions(e){let t=e;return t&&(t.encoding!==void 0||t.size!==void 0||t.buff!==void 0)}readInt8(e){return this._readNumberValue(Buffer.prototype.readInt8,1,e)}readInt16BE(e){return this._readNumberValue(Buffer.prototype.readInt16BE,2,e)}readInt16LE(e){return this._readNumberValue(Buffer.prototype.readInt16LE,2,e)}readInt32BE(e){return this._readNumberValue(Buffer.prototype.readInt32BE,4,e)}readInt32LE(e){return this._readNumberValue(Buffer.prototype.readInt32LE,4,e)}readBigInt64BE(e){return pe.bigIntAndBufferInt64Check("readBigInt64BE"),this._readNumberValue(Buffer.prototype.readBigInt64BE,8,e)}readBigInt64LE(e){return pe.bigIntAndBufferInt64Check("readBigInt64LE"),this._readNumberValue(Buffer.prototype.readBigInt64LE,8,e)}writeInt8(e,t){return this._writeNumberValue(Buffer.prototype.writeInt8,1,e,t),this}insertInt8(e,t){return this._insertNumberValue(Buffer.prototype.writeInt8,1,e,t)}writeInt16BE(e,t){return this._writeNumberValue(Buffer.prototype.writeInt16BE,2,e,t)}insertInt16BE(e,t){return this._insertNumberValue(Buffer.prototype.writeInt16BE,2,e,t)}writeInt16LE(e,t){return this._writeNumberValue(Buffer.prototype.writeInt16LE,2,e,t)}insertInt16LE(e,t){return this._insertNumberValue(Buffer.prototype.writeInt16LE,2,e,t)}writeInt32BE(e,t){return this._writeNumberValue(Buffer.prototype.writeInt32BE,4,e,t)}insertInt32BE(e,t){return this._insertNumberValue(Buffer.prototype.writeInt32BE,4,e,t)}writeInt32LE(e,t){return this._writeNumberValue(Buffer.prototype.writeInt32LE,4,e,t)}insertInt32LE(e,t){return this._insertNumberValue(Buffer.prototype.writeInt32LE,4,e,t)}writeBigInt64BE(e,t){return pe.bigIntAndBufferInt64Check("writeBigInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigInt64BE,8,e,t)}insertBigInt64BE(e,t){return pe.bigIntAndBufferInt64Check("writeBigInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigInt64BE,8,e,t)}writeBigInt64LE(e,t){return pe.bigIntAndBufferInt64Check("writeBigInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigInt64LE,8,e,t)}insertBigInt64LE(e,t){return pe.bigIntAndBufferInt64Check("writeBigInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigInt64LE,8,e,t)}readUInt8(e){return this._readNumberValue(Buffer.prototype.readUInt8,1,e)}readUInt16BE(e){return this._readNumberValue(Buffer.prototype.readUInt16BE,2,e)}readUInt16LE(e){return this._readNumberValue(Buffer.prototype.readUInt16LE,2,e)}readUInt32BE(e){return this._readNumberValue(Buffer.prototype.readUInt32BE,4,e)}readUInt32LE(e){return this._readNumberValue(Buffer.prototype.readUInt32LE,4,e)}readBigUInt64BE(e){return pe.bigIntAndBufferInt64Check("readBigUInt64BE"),this._readNumberValue(Buffer.prototype.readBigUInt64BE,8,e)}readBigUInt64LE(e){return pe.bigIntAndBufferInt64Check("readBigUInt64LE"),this._readNumberValue(Buffer.prototype.readBigUInt64LE,8,e)}writeUInt8(e,t){return this._writeNumberValue(Buffer.prototype.writeUInt8,1,e,t)}insertUInt8(e,t){return this._insertNumberValue(Buffer.prototype.writeUInt8,1,e,t)}writeUInt16BE(e,t){return this._writeNumberValue(Buffer.prototype.writeUInt16BE,2,e,t)}insertUInt16BE(e,t){return this._insertNumberValue(Buffer.prototype.writeUInt16BE,2,e,t)}writeUInt16LE(e,t){return this._writeNumberValue(Buffer.prototype.writeUInt16LE,2,e,t)}insertUInt16LE(e,t){return this._insertNumberValue(Buffer.prototype.writeUInt16LE,2,e,t)}writeUInt32BE(e,t){return this._writeNumberValue(Buffer.prototype.writeUInt32BE,4,e,t)}insertUInt32BE(e,t){return this._insertNumberValue(Buffer.prototype.writeUInt32BE,4,e,t)}writeUInt32LE(e,t){return this._writeNumberValue(Buffer.prototype.writeUInt32LE,4,e,t)}insertUInt32LE(e,t){return this._insertNumberValue(Buffer.prototype.writeUInt32LE,4,e,t)}writeBigUInt64BE(e,t){return pe.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,t)}insertBigUInt64BE(e,t){return pe.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,t)}writeBigUInt64LE(e,t){return pe.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,t)}insertBigUInt64LE(e,t){return pe.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,t)}readFloatBE(e){return this._readNumberValue(Buffer.prototype.readFloatBE,4,e)}readFloatLE(e){return this._readNumberValue(Buffer.prototype.readFloatLE,4,e)}writeFloatBE(e,t){return this._writeNumberValue(Buffer.prototype.writeFloatBE,4,e,t)}insertFloatBE(e,t){return this._insertNumberValue(Buffer.prototype.writeFloatBE,4,e,t)}writeFloatLE(e,t){return this._writeNumberValue(Buffer.prototype.writeFloatLE,4,e,t)}insertFloatLE(e,t){return this._insertNumberValue(Buffer.prototype.writeFloatLE,4,e,t)}readDoubleBE(e){return this._readNumberValue(Buffer.prototype.readDoubleBE,8,e)}readDoubleLE(e){return this._readNumberValue(Buffer.prototype.readDoubleLE,8,e)}writeDoubleBE(e,t){return this._writeNumberValue(Buffer.prototype.writeDoubleBE,8,e,t)}insertDoubleBE(e,t){return this._insertNumberValue(Buffer.prototype.writeDoubleBE,8,e,t)}writeDoubleLE(e,t){return this._writeNumberValue(Buffer.prototype.writeDoubleLE,8,e,t)}insertDoubleLE(e,t){return this._insertNumberValue(Buffer.prototype.writeDoubleLE,8,e,t)}readString(e,t){let r;typeof e=="number"?(pe.checkLengthValue(e),r=Math.min(e,this.length-this._readOffset)):(t=e,r=this.length-this._readOffset),typeof t!="undefined"&&pe.checkEncoding(t);let n=this._buff.slice(this._readOffset,this._readOffset+r).toString(t||this._encoding);return this._readOffset+=r,n}insertString(e,t,r){return pe.checkOffsetValue(t),this._handleString(e,!0,t,r)}writeString(e,t,r){return this._handleString(e,!1,t,r)}readStringNT(e){typeof e!="undefined"&&pe.checkEncoding(e);let t=this.length;for(let n=this._readOffset;n<this.length;n++)if(this._buff[n]===0){t=n;break}let r=this._buff.slice(this._readOffset,t);return this._readOffset=t+1,r.toString(e||this._encoding)}insertStringNT(e,t,r){return pe.checkOffsetValue(t),this.insertString(e,t,r),this.insertUInt8(0,t+e.length),this}writeStringNT(e,t,r){return this.writeString(e,t,r),this.writeUInt8(0,typeof t=="number"?t+e.length:this.writeOffset),this}readBuffer(e){typeof e!="undefined"&&pe.checkLengthValue(e);let t=typeof e=="number"?e:this.length,r=Math.min(this.length,this._readOffset+t),n=this._buff.slice(this._readOffset,r);return this._readOffset=r,n}insertBuffer(e,t){return pe.checkOffsetValue(t),this._handleBuffer(e,!0,t)}writeBuffer(e,t){return this._handleBuffer(e,!1,t)}readBufferNT(){let e=this.length;for(let r=this._readOffset;r<this.length;r++)if(this._buff[r]===0){e=r;break}let t=this._buff.slice(this._readOffset,e);return this._readOffset=e+1,t}insertBufferNT(e,t){return pe.checkOffsetValue(t),this.insertBuffer(e,t),this.insertUInt8(0,t+e.length),this}writeBufferNT(e,t){return typeof t!="undefined"&&pe.checkOffsetValue(t),this.writeBuffer(e,t),this.writeUInt8(0,typeof t=="number"?t+e.length:this._writeOffset),this}clear(){return this._writeOffset=0,this._readOffset=0,this.length=0,this}remaining(){return this.length-this._readOffset}get readOffset(){return this._readOffset}set readOffset(e){pe.checkOffsetValue(e),pe.checkTargetOffset(e,this),this._readOffset=e}get writeOffset(){return this._writeOffset}set writeOffset(e){pe.checkOffsetValue(e),pe.checkTargetOffset(e,this),this._writeOffset=e}get encoding(){return this._encoding}set encoding(e){pe.checkEncoding(e),this._encoding=e}get internalBuffer(){return this._buff}toBuffer(){return this._buff.slice(0,this.length)}toString(e){let t=typeof e=="string"?e:this._encoding;return pe.checkEncoding(t),this._buff.toString(t,0,this.length)}destroy(){return this.clear(),this}_handleString(e,t,r,n){let s=this._writeOffset,o=this._encoding;typeof r=="number"?s=r:typeof r=="string"&&(pe.checkEncoding(r),o=r),typeof n=="string"&&(pe.checkEncoding(n),o=n);let a=Buffer.byteLength(e,o);return t?this.ensureInsertable(a,s):this._ensureWriteable(a,s),this._buff.write(e,s,a,o),t?this._writeOffset+=a:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,s+a):this._writeOffset+=a,this}_handleBuffer(e,t,r){let n=typeof r=="number"?r:this._writeOffset;return t?this.ensureInsertable(e.length,n):this._ensureWriteable(e.length,n),e.copy(this._buff,n),t?this._writeOffset+=e.length:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,n+e.length):this._writeOffset+=e.length,this}ensureReadable(e,t){let r=this._readOffset;if(typeof t!="undefined"&&(pe.checkOffsetValue(t),r=t),r<0||r+e>this.length)throw new Error(pe.ERRORS.INVALID_READ_BEYOND_BOUNDS)}ensureInsertable(e,t){pe.checkOffsetValue(t),this._ensureCapacity(this.length+e),t<this.length&&this._buff.copy(this._buff,t+e,t,this._buff.length),t+e>this.length?this.length=t+e:this.length+=e}_ensureWriteable(e,t){let r=typeof t=="number"?t:this._writeOffset;this._ensureCapacity(r+e),r+e>this.length&&(this.length=r+e)}_ensureCapacity(e){let t=this._buff.length;if(e>t){let r=this._buff,n=t*3/2+1;n<e&&(n=e),this._buff=Buffer.allocUnsafe(n),r.copy(this._buff,0,0,t)}}_readNumberValue(e,t,r){this.ensureReadable(t,r);let n=e.call(this._buff,typeof r=="number"?r:this._readOffset);return typeof r=="undefined"&&(this._readOffset+=t),n}_insertNumberValue(e,t,r,n){return pe.checkOffsetValue(n),this.ensureInsertable(t,n),e.call(this._buff,r,n),this._writeOffset+=t,this}_writeNumberValue(e,t,r,n){if(typeof n=="number"){if(n<0)throw new Error(pe.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);pe.checkOffsetValue(n)}let s=typeof n=="number"?n:this._writeOffset;return this._ensureWriteable(t,s),e.call(this._buff,r,s),typeof n=="number"?this._writeOffset=Math.max(this._writeOffset,s+t):this._writeOffset+=t,this}};jl.SmartBuffer=ql});var Ul=_(Le=>{"use strict";Object.defineProperty(Le,"__esModule",{value:!0});Le.SOCKS5_NO_ACCEPTABLE_AUTH=Le.SOCKS5_CUSTOM_AUTH_END=Le.SOCKS5_CUSTOM_AUTH_START=Le.SOCKS_INCOMING_PACKET_SIZES=Le.SocksClientState=Le.Socks5Response=Le.Socks5HostType=Le.Socks5Auth=Le.Socks4Response=Le.SocksCommand=Le.ERRORS=Le.DEFAULT_TIMEOUT=void 0;var iS=3e4;Le.DEFAULT_TIMEOUT=iS;var rS={InvalidSocksCommand:"An invalid SOCKS command was provided. Valid options are connect, bind, and associate.",InvalidSocksCommandForOperation:"An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.",InvalidSocksCommandChain:"An invalid SOCKS command was provided. Chaining currently only supports the connect command.",InvalidSocksClientOptionsDestination:"An invalid destination host was provided.",InvalidSocksClientOptionsExistingSocket:"An invalid existing socket was provided. This should be an instance of stream.Duplex.",InvalidSocksClientOptionsProxy:"Invalid SOCKS proxy details were provided.",InvalidSocksClientOptionsTimeout:"An invalid timeout value was provided. Please enter a value above 0 (in ms).",InvalidSocksClientOptionsProxiesLength:"At least two socks proxies must be provided for chaining.",InvalidSocksClientOptionsCustomAuthRange:"Custom auth must be a value between 0x80 and 0xFE.",InvalidSocksClientOptionsCustomAuthOptions:"When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.",NegotiationError:"Negotiation error",SocketClosed:"Socket closed",ProxyConnectionTimedOut:"Proxy connection timed out",InternalError:"SocksClient internal error (this should not happen)",InvalidSocks4HandshakeResponse:"Received invalid Socks4 handshake response",Socks4ProxyRejectedConnection:"Socks4 Proxy rejected connection",InvalidSocks4IncomingConnectionResponse:"Socks4 invalid incoming connection response",Socks4ProxyRejectedIncomingBoundConnection:"Socks4 Proxy rejected incoming bound connection",InvalidSocks5InitialHandshakeResponse:"Received invalid Socks5 initial handshake response",InvalidSocks5IntiailHandshakeSocksVersion:"Received invalid Socks5 initial handshake (invalid socks version)",InvalidSocks5InitialHandshakeNoAcceptedAuthType:"Received invalid Socks5 initial handshake (no accepted authentication type)",InvalidSocks5InitialHandshakeUnknownAuthType:"Received invalid Socks5 initial handshake (unknown authentication type)",Socks5AuthenticationFailed:"Socks5 Authentication failed",InvalidSocks5FinalHandshake:"Received invalid Socks5 final handshake response",InvalidSocks5FinalHandshakeRejected:"Socks5 proxy rejected connection",InvalidSocks5IncomingConnectionResponse:"Received invalid Socks5 incoming connection response",Socks5ProxyRejectedIncomingBoundConnection:"Socks5 Proxy rejected incoming bound connection"};Le.ERRORS=rS;var nS={Socks5InitialHandshakeResponse:2,Socks5UserPassAuthenticationResponse:2,Socks5ResponseHeader:5,Socks5ResponseIPv4:10,Socks5ResponseIPv6:22,Socks5ResponseHostname:i=>i+7,Socks4Response:8};Le.SOCKS_INCOMING_PACKET_SIZES=nS;var vm;(function(i){i[i.connect=1]="connect",i[i.bind=2]="bind",i[i.associate=3]="associate"})(vm||(Le.SocksCommand=vm={}));var ym;(function(i){i[i.Granted=90]="Granted",i[i.Failed=91]="Failed",i[i.Rejected=92]="Rejected",i[i.RejectedIdent=93]="RejectedIdent"})(ym||(Le.Socks4Response=ym={}));var bm;(function(i){i[i.NoAuth=0]="NoAuth",i[i.GSSApi=1]="GSSApi",i[i.UserPass=2]="UserPass"})(bm||(Le.Socks5Auth=bm={}));var sS=128;Le.SOCKS5_CUSTOM_AUTH_START=sS;var oS=254;Le.SOCKS5_CUSTOM_AUTH_END=oS;var aS=255;Le.SOCKS5_NO_ACCEPTABLE_AUTH=aS;var _m;(function(i){i[i.Granted=0]="Granted",i[i.Failure=1]="Failure",i[i.NotAllowed=2]="NotAllowed",i[i.NetworkUnreachable=3]="NetworkUnreachable",i[i.HostUnreachable=4]="HostUnreachable",i[i.ConnectionRefused=5]="ConnectionRefused",i[i.TTLExpired=6]="TTLExpired",i[i.CommandNotSupported=7]="CommandNotSupported",i[i.AddressNotSupported=8]="AddressNotSupported"})(_m||(Le.Socks5Response=_m={}));var wm;(function(i){i[i.IPv4=1]="IPv4",i[i.Hostname=3]="Hostname",i[i.IPv6=4]="IPv6"})(wm||(Le.Socks5HostType=wm={}));var xm;(function(i){i[i.Created=0]="Created",i[i.Connecting=1]="Connecting",i[i.Connected=2]="Connected",i[i.SentInitialHandshake=3]="SentInitialHandshake",i[i.ReceivedInitialHandshakeResponse=4]="ReceivedInitialHandshakeResponse",i[i.SentAuthentication=5]="SentAuthentication",i[i.ReceivedAuthenticationResponse=6]="ReceivedAuthenticationResponse",i[i.SentFinalHandshake=7]="SentFinalHandshake",i[i.ReceivedFinalResponse=8]="ReceivedFinalResponse",i[i.BoundWaitingForConnection=9]="BoundWaitingForConnection",i[i.Established=10]="Established",i[i.Disconnected=11]="Disconnected",i[i.Error=99]="Error"})(xm||(Le.SocksClientState=xm={}))});var Vl=_(Ar=>{"use strict";Object.defineProperty(Ar,"__esModule",{value:!0});Ar.shuffleArray=Ar.SocksClientError=void 0;var $l=class extends Error{constructor(e,t){super(e),this.options=t}};Ar.SocksClientError=$l;function lS(i){for(let e=i.length-1;e>0;e--){let t=Math.floor(Math.random()*(e+1));[i[e],i[t]]=[i[t],i[e]]}}Ar.shuffleArray=lS});var Hl=_(Ir=>{"use strict";Object.defineProperty(Ir,"__esModule",{value:!0});Ir.isCorrect=Ir.isInSubnet=void 0;function cS(i){return this.subnetMask<i.subnetMask?!1:this.mask(i.subnetMask)===i.mask()}Ir.isInSubnet=cS;function uS(i){return function(){return this.addressMinusSuffix!==this.correctForm()?!1:this.subnetMask===i&&!this.parsedSubnet?!0:this.parsedSubnet===String(this.subnetMask)}}Ir.isCorrect=uS});var Gl=_(Gt=>{"use strict";Object.defineProperty(Gt,"__esModule",{value:!0});Gt.RE_SUBNET_STRING=Gt.RE_ADDRESS=Gt.GROUPS=Gt.BITS=void 0;Gt.BITS=32;Gt.GROUPS=4;Gt.RE_ADDRESS=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;Gt.RE_SUBNET_STRING=/\/\d{1,2}$/});var $s=_(Us=>{"use strict";Object.defineProperty(Us,"__esModule",{value:!0});Us.AddressError=void 0;var Wl=class extends Error{constructor(e,t){super(e),this.name="AddressError",t!==null&&(this.parseMessage=t)}};Us.AddressError=Wl});var Yl=_((Vs,Sm)=>{(function(){var i,e=0xdeadbeefcafe,t=(e&16777215)==15715070;function r(h,p,v){h!=null&&(typeof h=="number"?this.fromNumber(h,p,v):p==null&&typeof h!="string"?this.fromString(h,256):this.fromString(h,p))}function n(){return new r(null)}function s(h,p,v,w,B,M){for(;--M>=0;){var G=p*this[h++]+v[w]+B;B=Math.floor(G/67108864),v[w++]=G&67108863}return B}function o(h,p,v,w,B,M){for(var G=p&32767,K=p>>15;--M>=0;){var Pe=this[h]&32767,Ye=this[h++]>>15,kt=K*Pe+Ye*G;Pe=G*Pe+((kt&32767)<<15)+v[w]+(B&1073741823),B=(Pe>>>30)+(kt>>>15)+K*Ye+(B>>>30),v[w++]=Pe&1073741823}return B}function a(h,p,v,w,B,M){for(var G=p&16383,K=p>>14;--M>=0;){var Pe=this[h]&16383,Ye=this[h++]>>14,kt=K*Pe+Ye*G;Pe=G*Pe+((kt&16383)<<14)+v[w]+B,B=(Pe>>28)+(kt>>14)+K*Ye,v[w++]=Pe&268435455}return B}var l=typeof navigator!="undefined";l&&t&&navigator.appName=="Microsoft Internet Explorer"?(r.prototype.am=o,i=30):l&&t&&navigator.appName!="Netscape"?(r.prototype.am=s,i=26):(r.prototype.am=a,i=28),r.prototype.DB=i,r.prototype.DM=(1<<i)-1,r.prototype.DV=1<<i;var c=52;r.prototype.FV=Math.pow(2,c),r.prototype.F1=c-i,r.prototype.F2=2*i-c;var u="0123456789abcdefghijklmnopqrstuvwxyz",f=new Array,d,m;for(d=48,m=0;m<=9;++m)f[d++]=m;for(d=97,m=10;m<36;++m)f[d++]=m;for(d=65,m=10;m<36;++m)f[d++]=m;function g(h){return u.charAt(h)}function y(h,p){var v=f[h.charCodeAt(p)];return v==null?-1:v}function b(h){for(var p=this.t-1;p>=0;--p)h[p]=this[p];h.t=this.t,h.s=this.s}function x(h){this.t=1,this.s=h<0?-1:0,h>0?this[0]=h:h<-1?this[0]=h+this.DV:this.t=0}function E(h){var p=n();return p.fromInt(h),p}function k(h,p){var v;if(p==16)v=4;else if(p==8)v=3;else if(p==256)v=8;else if(p==2)v=1;else if(p==32)v=5;else if(p==4)v=2;else{this.fromRadix(h,p);return}this.t=0,this.s=0;for(var w=h.length,B=!1,M=0;--w>=0;){var G=v==8?h[w]&255:y(h,w);if(G<0){h.charAt(w)=="-"&&(B=!0);continue}B=!1,M==0?this[this.t++]=G:M+v>this.DB?(this[this.t-1]|=(G&(1<<this.DB-M)-1)<<M,this[this.t++]=G>>this.DB-M):this[this.t-1]|=G<<M,M+=v,M>=this.DB&&(M-=this.DB)}v==8&&(h[0]&128)!=0&&(this.s=-1,M>0&&(this[this.t-1]|=(1<<this.DB-M)-1<<M)),this.clamp(),B&&r.ZERO.subTo(this,this)}function O(){for(var h=this.s&this.DM;this.t>0&&this[this.t-1]==h;)--this.t}function S(h){if(this.s<0)return"-"+this.negate().toString(h);var p;if(h==16)p=4;else if(h==8)p=3;else if(h==2)p=1;else if(h==32)p=5;else if(h==4)p=2;else return this.toRadix(h);var v=(1<<p)-1,w,B=!1,M="",G=this.t,K=this.DB-G*this.DB%p;if(G-- >0)for(K<this.DB&&(w=this[G]>>K)>0&&(B=!0,M=g(w));G>=0;)K<p?(w=(this[G]&(1<<K)-1)<<p-K,w|=this[--G]>>(K+=this.DB-p)):(w=this[G]>>(K-=p)&v,K<=0&&(K+=this.DB,--G)),w>0&&(B=!0),B&&(M+=g(w));return B?M:"0"}function R(){var h=n();return r.ZERO.subTo(this,h),h}function T(){return this.s<0?this.negate():this}function A(h){var p=this.s-h.s;if(p!=0)return p;var v=this.t;if(p=v-h.t,p!=0)return this.s<0?-p:p;for(;--v>=0;)if((p=this[v]-h[v])!=0)return p;return 0}function C(h){var p=1,v;return(v=h>>>16)!=0&&(h=v,p+=16),(v=h>>8)!=0&&(h=v,p+=8),(v=h>>4)!=0&&(h=v,p+=4),(v=h>>2)!=0&&(h=v,p+=2),(v=h>>1)!=0&&(h=v,p+=1),p}function L(){return this.t<=0?0:this.DB*(this.t-1)+C(this[this.t-1]^this.s&this.DM)}function P(h,p){var v;for(v=this.t-1;v>=0;--v)p[v+h]=this[v];for(v=h-1;v>=0;--v)p[v]=0;p.t=this.t+h,p.s=this.s}function U(h,p){for(var v=h;v<this.t;++v)p[v-h]=this[v];p.t=Math.max(this.t-h,0),p.s=this.s}function F(h,p){var v=h%this.DB,w=this.DB-v,B=(1<<w)-1,M=Math.floor(h/this.DB),G=this.s<<v&this.DM,K;for(K=this.t-1;K>=0;--K)p[K+M+1]=this[K]>>w|G,G=(this[K]&B)<<v;for(K=M-1;K>=0;--K)p[K]=0;p[M]=G,p.t=this.t+M+1,p.s=this.s,p.clamp()}function H(h,p){p.s=this.s;var v=Math.floor(h/this.DB);if(v>=this.t){p.t=0;return}var w=h%this.DB,B=this.DB-w,M=(1<<w)-1;p[0]=this[v]>>w;for(var G=v+1;G<this.t;++G)p[G-v-1]|=(this[G]&M)<<B,p[G-v]=this[G]>>w;w>0&&(p[this.t-v-1]|=(this.s&M)<<B),p.t=this.t-v,p.clamp()}function j(h,p){for(var v=0,w=0,B=Math.min(h.t,this.t);v<B;)w+=this[v]-h[v],p[v++]=w&this.DM,w>>=this.DB;if(h.t<this.t){for(w-=h.s;v<this.t;)w+=this[v],p[v++]=w&this.DM,w>>=this.DB;w+=this.s}else{for(w+=this.s;v<h.t;)w-=h[v],p[v++]=w&this.DM,w>>=this.DB;w-=h.s}p.s=w<0?-1:0,w<-1?p[v++]=this.DV+w:w>0&&(p[v++]=w),p.t=v,p.clamp()}function V(h,p){var v=this.abs(),w=h.abs(),B=v.t;for(p.t=B+w.t;--B>=0;)p[B]=0;for(B=0;B<w.t;++B)p[B+v.t]=v.am(0,w[B],p,B,0,v.t);p.s=0,p.clamp(),this.s!=h.s&&r.ZERO.subTo(p,p)}function Y(h){for(var p=this.abs(),v=h.t=2*p.t;--v>=0;)h[v]=0;for(v=0;v<p.t-1;++v){var w=p.am(v,p[v],h,2*v,0,1);(h[v+p.t]+=p.am(v+1,2*p[v],h,2*v+1,w,p.t-v-1))>=p.DV&&(h[v+p.t]-=p.DV,h[v+p.t+1]=1)}h.t>0&&(h[h.t-1]+=p.am(v,p[v],h,2*v,0,1)),h.s=0,h.clamp()}function Q(h,p,v){var w=h.abs();if(!(w.t<=0)){var B=this.abs();if(B.t<w.t){p!=null&&p.fromInt(0),v!=null&&this.copyTo(v);return}v==null&&(v=n());var M=n(),G=this.s,K=h.s,Pe=this.DB-C(w[w.t-1]);Pe>0?(w.lShiftTo(Pe,M),B.lShiftTo(Pe,v)):(w.copyTo(M),B.copyTo(v));var Ye=M.t,kt=M[Ye-1];if(kt!=0){var _t=kt*(1<<this.F1)+(Ye>1?M[Ye-2]>>this.F2:0),ti=this.FV/_t,ps=(1<<this.F1)/_t,Mt=1<<this.F2,Dt=v.t,ds=Dt-Ye,di=p==null?n():p;for(M.dlShiftTo(ds,di),v.compareTo(di)>=0&&(v[v.t++]=1,v.subTo(di,v)),r.ONE.dlShiftTo(Ye,di),di.subTo(M,M);M.t<Ye;)M[M.t++]=0;for(;--ds>=0;){var xa=v[--Dt]==kt?this.DM:Math.floor(v[Dt]*ti+(v[Dt-1]+Mt)*ps);if((v[Dt]+=M.am(0,xa,v,ds,0,Ye))<xa)for(M.dlShiftTo(ds,di),v.subTo(di,v);v[Dt]<--xa;)v.subTo(di,v)}p!=null&&(v.drShiftTo(Ye,p),G!=K&&r.ZERO.subTo(p,p)),v.t=Ye,v.clamp(),Pe>0&&v.rShiftTo(Pe,v),G<0&&r.ZERO.subTo(v,v)}}}function W(h){var p=n();return this.abs().divRemTo(h,null,p),this.s<0&&p.compareTo(r.ZERO)>0&&h.subTo(p,p),p}function de(h){this.m=h}function ae(h){return h.s<0||h.compareTo(this.m)>=0?h.mod(this.m):h}function ne(h){return h}function ue(h){h.divRemTo(this.m,null,h)}function N(h,p,v){h.multiplyTo(p,v),this.reduce(v)}function X(h,p){h.squareTo(p),this.reduce(p)}de.prototype.convert=ae,de.prototype.revert=ne,de.prototype.reduce=ue,de.prototype.mulTo=N,de.prototype.sqrTo=X;function ke(){if(this.t<1)return 0;var h=this[0];if((h&1)==0)return 0;var p=h&3;return p=p*(2-(h&15)*p)&15,p=p*(2-(h&255)*p)&255,p=p*(2-((h&65535)*p&65535))&65535,p=p*(2-h*p%this.DV)%this.DV,p>0?this.DV-p:-p}function be(h){this.m=h,this.mp=h.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<h.DB-15)-1,this.mt2=2*h.t}function ge(h){var p=n();return h.abs().dlShiftTo(this.m.t,p),p.divRemTo(this.m,null,p),h.s<0&&p.compareTo(r.ZERO)>0&&this.m.subTo(p,p),p}function ve(h){var p=n();return h.copyTo(p),this.reduce(p),p}function fe(h){for(;h.t<=this.mt2;)h[h.t++]=0;for(var p=0;p<this.m.t;++p){var v=h[p]&32767,w=v*this.mpl+((v*this.mph+(h[p]>>15)*this.mpl&this.um)<<15)&h.DM;for(v=p+this.m.t,h[v]+=this.m.am(0,w,h,p,0,this.m.t);h[v]>=h.DV;)h[v]-=h.DV,h[++v]++}h.clamp(),h.drShiftTo(this.m.t,h),h.compareTo(this.m)>=0&&h.subTo(this.m,h)}function z(h,p){h.squareTo(p),this.reduce(p)}function $(h,p,v){h.multiplyTo(p,v),this.reduce(v)}be.prototype.convert=ge,be.prototype.revert=ve,be.prototype.reduce=fe,be.prototype.mulTo=$,be.prototype.sqrTo=z;function Te(){return(this.t>0?this[0]&1:this.s)==0}function re(h,p){if(h>4294967295||h<1)return r.ONE;var v=n(),w=n(),B=p.convert(this),M=C(h)-1;for(B.copyTo(v);--M>=0;)if(p.sqrTo(v,w),(h&1<<M)>0)p.mulTo(w,B,v);else{var G=v;v=w,w=G}return p.revert(v)}function he(h,p){var v;return h<256||p.isEven()?v=new de(p):v=new be(p),this.exp(h,v)}r.prototype.copyTo=b,r.prototype.fromInt=x,r.prototype.fromString=k,r.prototype.clamp=O,r.prototype.dlShiftTo=P,r.prototype.drShiftTo=U,r.prototype.lShiftTo=F,r.prototype.rShiftTo=H,r.prototype.subTo=j,r.prototype.multiplyTo=V,r.prototype.squareTo=Y,r.prototype.divRemTo=Q,r.prototype.invDigit=ke,r.prototype.isEven=Te,r.prototype.exp=re,r.prototype.toString=S,r.prototype.negate=R,r.prototype.abs=T,r.prototype.compareTo=A,r.prototype.bitLength=L,r.prototype.mod=W,r.prototype.modPowInt=he,r.ZERO=E(0),r.ONE=E(1);function ht(){var h=n();return this.copyTo(h),h}function yt(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function I(){return this.t==0?this.s:this[0]<<24>>24}function Z(){return this.t==0?this.s:this[0]<<16>>16}function te(h){return Math.floor(Math.LN2*this.DB/Math.log(h))}function ee(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function le(h){if(h==null&&(h=10),this.signum()==0||h<2||h>36)return"0";var p=this.chunkSize(h),v=Math.pow(h,p),w=E(v),B=n(),M=n(),G="";for(this.divRemTo(w,B,M);B.signum()>0;)G=(v+M.intValue()).toString(h).substr(1)+G,B.divRemTo(w,B,M);return M.intValue().toString(h)+G}function ce(h,p){this.fromInt(0),p==null&&(p=10);for(var v=this.chunkSize(p),w=Math.pow(p,v),B=!1,M=0,G=0,K=0;K<h.length;++K){var Pe=y(h,K);if(Pe<0){h.charAt(K)=="-"&&this.signum()==0&&(B=!0);continue}G=p*G+Pe,++M>=v&&(this.dMultiply(w),this.dAddOffset(G,0),M=0,G=0)}M>0&&(this.dMultiply(Math.pow(p,M)),this.dAddOffset(G,0)),B&&r.ZERO.subTo(this,this)}function _e(h,p,v){if(typeof p=="number")if(h<2)this.fromInt(1);else for(this.fromNumber(h,v),this.testBit(h-1)||this.bitwiseTo(r.ONE.shiftLeft(h-1),oe,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(p);)this.dAddOffset(2,0),this.bitLength()>h&&this.subTo(r.ONE.shiftLeft(h-1),this);else{var w=new Array,B=h&7;w.length=(h>>3)+1,p.nextBytes(w),B>0?w[0]&=(1<<B)-1:w[0]=0,this.fromString(w,256)}}function we(){var h=this.t,p=new Array;p[0]=this.s;var v=this.DB-h*this.DB%8,w,B=0;if(h-- >0)for(v<this.DB&&(w=this[h]>>v)!=(this.s&this.DM)>>v&&(p[B++]=w|this.s<<this.DB-v);h>=0;)v<8?(w=(this[h]&(1<<v)-1)<<8-v,w|=this[--h]>>(v+=this.DB-8)):(w=this[h]>>(v-=8)&255,v<=0&&(v+=this.DB,--h)),(w&128)!=0&&(w|=-256),B==0&&(this.s&128)!=(w&128)&&++B,(B>0||w!=this.s)&&(p[B++]=w);return p}function Re(h){return this.compareTo(h)==0}function Ae(h){return this.compareTo(h)<0?this:h}function q(h){return this.compareTo(h)>0?this:h}function J(h,p,v){var w,B,M=Math.min(h.t,this.t);for(w=0;w<M;++w)v[w]=p(this[w],h[w]);if(h.t<this.t){for(B=h.s&this.DM,w=M;w<this.t;++w)v[w]=p(this[w],B);v.t=this.t}else{for(B=this.s&this.DM,w=M;w<h.t;++w)v[w]=p(B,h[w]);v.t=h.t}v.s=p(this.s,h.s),v.clamp()}function se(h,p){return h&p}function Ne(h){var p=n();return this.bitwiseTo(h,se,p),p}function oe(h,p){return h|p}function me(h){var p=n();return this.bitwiseTo(h,oe,p),p}function Ee(h,p){return h^p}function ie(h){var p=n();return this.bitwiseTo(h,Ee,p),p}function xe(h,p){return h&~p}function Ue(h){var p=n();return this.bitwiseTo(h,xe,p),p}function Ie(){for(var h=n(),p=0;p<this.t;++p)h[p]=this.DM&~this[p];return h.t=this.t,h.s=~this.s,h}function pt(h){var p=n();return h<0?this.rShiftTo(-h,p):this.lShiftTo(h,p),p}function Ot(h){var p=n();return h<0?this.lShiftTo(-h,p):this.rShiftTo(h,p),p}function Xt(h){if(h==0)return-1;var p=0;return(h&65535)==0&&(h>>=16,p+=16),(h&255)==0&&(h>>=8,p+=8),(h&15)==0&&(h>>=4,p+=4),(h&3)==0&&(h>>=2,p+=2),(h&1)==0&&++p,p}function hi(){for(var h=0;h<this.t;++h)if(this[h]!=0)return h*this.DB+Xt(this[h]);return this.s<0?this.t*this.DB:-1}function pi(h){for(var p=0;h!=0;)h&=h-1,++p;return p}function Bi(){for(var h=0,p=this.s&this.DM,v=0;v<this.t;++v)h+=pi(this[v]^p);return h}function Ri(h){var p=Math.floor(h/this.DB);return p>=this.t?this.s!=0:(this[p]&1<<h%this.DB)!=0}function hr(h,p){var v=r.ONE.shiftLeft(h);return this.bitwiseTo(v,p,v),v}function Pi(h){return this.changeBit(h,oe)}function Mi(h){return this.changeBit(h,xe)}function Di(h){return this.changeBit(h,Ee)}function Fi(h,p){for(var v=0,w=0,B=Math.min(h.t,this.t);v<B;)w+=this[v]+h[v],p[v++]=w&this.DM,w>>=this.DB;if(h.t<this.t){for(w+=h.s;v<this.t;)w+=this[v],p[v++]=w&this.DM,w>>=this.DB;w+=this.s}else{for(w+=this.s;v<h.t;)w+=h[v],p[v++]=w&this.DM,w>>=this.DB;w+=h.s}p.s=w<0?-1:0,w>0?p[v++]=w:w<-1&&(p[v++]=this.DV+w),p.t=v,p.clamp()}function es(h){var p=n();return this.addTo(h,p),p}function Xr(h){var p=n();return this.subTo(h,p),p}function ts(h){var p=n();return this.multiplyTo(h,p),p}function is(){var h=n();return this.squareTo(h),h}function rs(h){var p=n();return this.divRemTo(h,p,null),p}function ns(h){var p=n();return this.divRemTo(h,null,p),p}function ss(h){var p=n(),v=n();return this.divRemTo(h,p,v),new Array(p,v)}function ya(h){this[this.t]=this.am(0,h-1,this,0,0,this.t),++this.t,this.clamp()}function qi(h,p){if(h!=0){for(;this.t<=p;)this[this.t++]=0;for(this[p]+=h;this[p]>=this.DV;)this[p]-=this.DV,++p>=this.t&&(this[this.t++]=0),++this[p]}}function ei(){}function ji(h){return h}function pr(h,p,v){h.multiplyTo(p,v)}function os(h,p){h.squareTo(p)}ei.prototype.convert=ji,ei.prototype.revert=ji,ei.prototype.mulTo=pr,ei.prototype.sqrTo=os;function as(h){return this.exp(h,new ei)}function ls(h,p,v){var w=Math.min(this.t+h.t,p);for(v.s=0,v.t=w;w>0;)v[--w]=0;var B;for(B=v.t-this.t;w<B;++w)v[w+this.t]=this.am(0,h[w],v,w,0,this.t);for(B=Math.min(h.t,p);w<B;++w)this.am(0,h[w],v,w,0,p-w);v.clamp()}function cs(h,p,v){--p;var w=v.t=this.t+h.t-p;for(v.s=0;--w>=0;)v[w]=0;for(w=Math.max(p-this.t,0);w<h.t;++w)v[this.t+w-p]=this.am(p-w,h[w],v,0,0,this.t+w-p);v.clamp(),v.drShiftTo(1,v)}function Vt(h){this.r2=n(),this.q3=n(),r.ONE.dlShiftTo(2*h.t,this.r2),this.mu=this.r2.divide(h),this.m=h}function us(h){if(h.s<0||h.t>2*this.m.t)return h.mod(this.m);if(h.compareTo(this.m)<0)return h;var p=n();return h.copyTo(p),this.reduce(p),p}function fs(h){return h}function dr(h){for(h.drShiftTo(this.m.t-1,this.r2),h.t>this.m.t+1&&(h.t=this.m.t+1,h.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);h.compareTo(this.r2)<0;)h.dAddOffset(1,this.m.t+1);for(h.subTo(this.r2,h);h.compareTo(this.m)>=0;)h.subTo(this.m,h)}function Cb(h,p){h.squareTo(p),this.reduce(p)}function Tb(h,p,v){h.multiplyTo(p,v),this.reduce(v)}Vt.prototype.convert=us,Vt.prototype.revert=fs,Vt.prototype.reduce=dr,Vt.prototype.mulTo=Tb,Vt.prototype.sqrTo=Cb;function Ab(h,p){var v=h.bitLength(),w,B=E(1),M;if(v<=0)return B;v<18?w=1:v<48?w=3:v<144?w=4:v<768?w=5:w=6,v<8?M=new de(p):p.isEven()?M=new Vt(p):M=new be(p);var G=new Array,K=3,Pe=w-1,Ye=(1<<w)-1;if(G[1]=M.convert(this),w>1){var kt=n();for(M.sqrTo(G[1],kt);K<=Ye;)G[K]=n(),M.mulTo(kt,G[K-2],G[K]),K+=2}var _t=h.t-1,ti,ps=!0,Mt=n(),Dt;for(v=C(h[_t])-1;_t>=0;){for(v>=Pe?ti=h[_t]>>v-Pe&Ye:(ti=(h[_t]&(1<<v+1)-1)<<Pe-v,_t>0&&(ti|=h[_t-1]>>this.DB+v-Pe)),K=w;(ti&1)==0;)ti>>=1,--K;if((v-=K)<0&&(v+=this.DB,--_t),ps)G[ti].copyTo(B),ps=!1;else{for(;K>1;)M.sqrTo(B,Mt),M.sqrTo(Mt,B),K-=2;K>0?M.sqrTo(B,Mt):(Dt=B,B=Mt,Mt=Dt),M.mulTo(Mt,G[ti],B)}for(;_t>=0&&(h[_t]&1<<v)==0;)M.sqrTo(B,Mt),Dt=B,B=Mt,Mt=Dt,--v<0&&(v=this.DB-1,--_t)}return M.revert(B)}function Ib(h){var p=this.s<0?this.negate():this.clone(),v=h.s<0?h.negate():h.clone();if(p.compareTo(v)<0){var w=p;p=v,v=w}var B=p.getLowestSetBit(),M=v.getLowestSetBit();if(M<0)return p;for(B<M&&(M=B),M>0&&(p.rShiftTo(M,p),v.rShiftTo(M,v));p.signum()>0;)(B=p.getLowestSetBit())>0&&p.rShiftTo(B,p),(B=v.getLowestSetBit())>0&&v.rShiftTo(B,v),p.compareTo(v)>=0?(p.subTo(v,p),p.rShiftTo(1,p)):(v.subTo(p,v),v.rShiftTo(1,v));return M>0&&v.lShiftTo(M,v),v}function Nb(h){if(h<=0)return 0;var p=this.DV%h,v=this.s<0?h-1:0;if(this.t>0)if(p==0)v=this[0]%h;else for(var w=this.t-1;w>=0;--w)v=(p*v+this[w])%h;return v}function Lb(h){var p=h.isEven();if(this.isEven()&&p||h.signum()==0)return r.ZERO;for(var v=h.clone(),w=this.clone(),B=E(1),M=E(0),G=E(0),K=E(1);v.signum()!=0;){for(;v.isEven();)v.rShiftTo(1,v),p?((!B.isEven()||!M.isEven())&&(B.addTo(this,B),M.subTo(h,M)),B.rShiftTo(1,B)):M.isEven()||M.subTo(h,M),M.rShiftTo(1,M);for(;w.isEven();)w.rShiftTo(1,w),p?((!G.isEven()||!K.isEven())&&(G.addTo(this,G),K.subTo(h,K)),G.rShiftTo(1,G)):K.isEven()||K.subTo(h,K),K.rShiftTo(1,K);v.compareTo(w)>=0?(v.subTo(w,v),p&&B.subTo(G,B),M.subTo(K,M)):(w.subTo(v,w),p&&G.subTo(B,G),K.subTo(M,K))}if(w.compareTo(r.ONE)!=0)return r.ZERO;if(K.compareTo(h)>=0)return K.subtract(h);if(K.signum()<0)K.addTo(h,K);else return K;return K.signum()<0?K.add(h):K}var ot=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Bb=(1<<26)/ot[ot.length-1];function Rb(h){var p,v=this.abs();if(v.t==1&&v[0]<=ot[ot.length-1]){for(p=0;p<ot.length;++p)if(v[0]==ot[p])return!0;return!1}if(v.isEven())return!1;for(p=1;p<ot.length;){for(var w=ot[p],B=p+1;B<ot.length&&w<Bb;)w*=ot[B++];for(w=v.modInt(w);p<B;)if(w%ot[p++]==0)return!1}return v.millerRabin(h)}function Pb(h){var p=this.subtract(r.ONE),v=p.getLowestSetBit();if(v<=0)return!1;var w=p.shiftRight(v);h=h+1>>1,h>ot.length&&(h=ot.length);for(var B=n(),M=0;M<h;++M){B.fromInt(ot[Math.floor(Math.random()*ot.length)]);var G=B.modPow(w,this);if(G.compareTo(r.ONE)!=0&&G.compareTo(p)!=0){for(var K=1;K++<v&&G.compareTo(p)!=0;)if(G=G.modPowInt(2,this),G.compareTo(r.ONE)==0)return!1;if(G.compareTo(p)!=0)return!1}}return!0}r.prototype.chunkSize=te,r.prototype.toRadix=le,r.prototype.fromRadix=ce,r.prototype.fromNumber=_e,r.prototype.bitwiseTo=J,r.prototype.changeBit=hr,r.prototype.addTo=Fi,r.prototype.dMultiply=ya,r.prototype.dAddOffset=qi,r.prototype.multiplyLowerTo=ls,r.prototype.multiplyUpperTo=cs,r.prototype.modInt=Nb,r.prototype.millerRabin=Pb,r.prototype.clone=ht,r.prototype.intValue=yt,r.prototype.byteValue=I,r.prototype.shortValue=Z,r.prototype.signum=ee,r.prototype.toByteArray=we,r.prototype.equals=Re,r.prototype.min=Ae,r.prototype.max=q,r.prototype.and=Ne,r.prototype.or=me,r.prototype.xor=ie,r.prototype.andNot=Ue,r.prototype.not=Ie,r.prototype.shiftLeft=pt,r.prototype.shiftRight=Ot,r.prototype.getLowestSetBit=hi,r.prototype.bitCount=Bi,r.prototype.testBit=Ri,r.prototype.setBit=Pi,r.prototype.clearBit=Mi,r.prototype.flipBit=Di,r.prototype.add=es,r.prototype.subtract=Xr,r.prototype.multiply=ts,r.prototype.divide=rs,r.prototype.remainder=ns,r.prototype.divideAndRemainder=ss,r.prototype.modPow=Ab,r.prototype.modInverse=Lb,r.prototype.pow=as,r.prototype.gcd=Ib,r.prototype.isProbablePrime=Rb,r.prototype.square=is,r.prototype.Barrett=Vt;var hs,bt,We;function Mb(h){bt[We++]^=h&255,bt[We++]^=h>>8&255,bt[We++]^=h>>16&255,bt[We++]^=h>>24&255,We>=wa&&(We-=wa)}function Of(){Mb(new Date().getTime())}if(bt==null){bt=new Array,We=0;var Pt;if(typeof window!="undefined"&&window.crypto){if(window.crypto.getRandomValues){var kf=new Uint8Array(32);for(window.crypto.getRandomValues(kf),Pt=0;Pt<32;++Pt)bt[We++]=kf[Pt]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var Cf=window.crypto.random(32);for(Pt=0;Pt<Cf.length;++Pt)bt[We++]=Cf.charCodeAt(Pt)&255}}for(;We<wa;)Pt=Math.floor(65536*Math.random()),bt[We++]=Pt>>>8,bt[We++]=Pt&255;We=0,Of()}function Db(){if(hs==null){for(Of(),hs=Ub(),hs.init(bt),We=0;We<bt.length;++We)bt[We]=0;We=0}return hs.next()}function Fb(h){var p;for(p=0;p<h.length;++p)h[p]=Db()}function ba(){}ba.prototype.nextBytes=Fb;function _a(){this.i=0,this.j=0,this.S=new Array}function qb(h){var p,v,w;for(p=0;p<256;++p)this.S[p]=p;for(v=0,p=0;p<256;++p)v=v+this.S[p]+h[p%h.length]&255,w=this.S[p],this.S[p]=this.S[v],this.S[v]=w;this.i=0,this.j=0}function jb(){var h;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,h=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=h,this.S[h+this.S[this.i]&255]}_a.prototype.init=qb,_a.prototype.next=jb;function Ub(){return new _a}var wa=256;typeof Vs!="undefined"?Vs=Sm.exports={default:r,BigInteger:r,SecureRandom:ba}:this.jsbn={BigInteger:r,SecureRandom:ba}}).call(Vs)});var fn=_(Hs=>{(function(){"use strict";var i={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function e(o){return r(s(o),arguments)}function t(o,a){return e.apply(null,[o].concat(a||[]))}function r(o,a){var l=1,c=o.length,u,f="",d,m,g,y,b,x,E,k;for(d=0;d<c;d++)if(typeof o[d]=="string")f+=o[d];else if(typeof o[d]=="object"){if(g=o[d],g.keys)for(u=a[l],m=0;m<g.keys.length;m++){if(u==null)throw new Error(e('[sprintf] Cannot access property "%s" of undefined value "%s"',g.keys[m],g.keys[m-1]));u=u[g.keys[m]]}else g.param_no?u=a[g.param_no]:u=a[l++];if(i.not_type.test(g.type)&&i.not_primitive.test(g.type)&&u instanceof Function&&(u=u()),i.numeric_arg.test(g.type)&&typeof u!="number"&&isNaN(u))throw new TypeError(e("[sprintf] expecting number but found %T",u));switch(i.number.test(g.type)&&(E=u>=0),g.type){case"b":u=parseInt(u,10).toString(2);break;case"c":u=String.fromCharCode(parseInt(u,10));break;case"d":case"i":u=parseInt(u,10);break;case"j":u=JSON.stringify(u,null,g.width?parseInt(g.width):0);break;case"e":u=g.precision?parseFloat(u).toExponential(g.precision):parseFloat(u).toExponential();break;case"f":u=g.precision?parseFloat(u).toFixed(g.precision):parseFloat(u);break;case"g":u=g.precision?String(Number(u.toPrecision(g.precision))):parseFloat(u);break;case"o":u=(parseInt(u,10)>>>0).toString(8);break;case"s":u=String(u),u=g.precision?u.substring(0,g.precision):u;break;case"t":u=String(!!u),u=g.precision?u.substring(0,g.precision):u;break;case"T":u=Object.prototype.toString.call(u).slice(8,-1).toLowerCase(),u=g.precision?u.substring(0,g.precision):u;break;case"u":u=parseInt(u,10)>>>0;break;case"v":u=u.valueOf(),u=g.precision?u.substring(0,g.precision):u;break;case"x":u=(parseInt(u,10)>>>0).toString(16);break;case"X":u=(parseInt(u,10)>>>0).toString(16).toUpperCase();break}i.json.test(g.type)?f+=u:(i.number.test(g.type)&&(!E||g.sign)?(k=E?"+":"-",u=u.toString().replace(i.sign,"")):k="",b=g.pad_char?g.pad_char==="0"?"0":g.pad_char.charAt(1):" ",x=g.width-(k+u).length,y=g.width&&x>0?b.repeat(x):"",f+=g.align?k+u+y:b==="0"?k+y+u:y+k+u)}return f}var n=Object.create(null);function s(o){if(n[o])return n[o];for(var a=o,l,c=[],u=0;a;){if((l=i.text.exec(a))!==null)c.push(l[0]);else if((l=i.modulo.exec(a))!==null)c.push("%");else if((l=i.placeholder.exec(a))!==null){if(l[2]){u|=1;var f=[],d=l[2],m=[];if((m=i.key.exec(d))!==null)for(f.push(m[1]);(d=d.substring(m[0].length))!=="";)if((m=i.key_access.exec(d))!==null)f.push(m[1]);else if((m=i.index_access.exec(d))!==null)f.push(m[1]);else throw new SyntaxError("[sprintf] failed to parse named argument key");else throw new SyntaxError("[sprintf] failed to parse named argument key");l[2]=f}else u|=2;if(u===3)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");c.push({placeholder:l[0],param_no:l[1],keys:l[2],sign:l[3],pad_char:l[4],align:l[5],width:l[6],precision:l[7],type:l[8]})}else throw new SyntaxError("[sprintf] unexpected placeholder");a=a.substring(l[0].length)}return n[o]=c}typeof Hs!="undefined"&&(Hs.sprintf=e,Hs.vsprintf=t),typeof window!="undefined"&&(window.sprintf=e,window.vsprintf=t,typeof define=="function"&&define.amd&&define(function(){return{sprintf:e,vsprintf:t}}))})()});var zl=_(Wt=>{"use strict";var fS=Wt&&Wt.__createBinding||(Object.create?function(i,e,t,r){r===void 0&&(r=t);var n=Object.getOwnPropertyDescriptor(e,t);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(i,r,n)}:function(i,e,t,r){r===void 0&&(r=t),i[r]=e[t]}),hS=Wt&&Wt.__setModuleDefault||(Object.create?function(i,e){Object.defineProperty(i,"default",{enumerable:!0,value:e})}:function(i,e){i.default=e}),km=Wt&&Wt.__importStar||function(i){if(i&&i.__esModule)return i;var e={};if(i!=null)for(var t in i)t!=="default"&&Object.prototype.hasOwnProperty.call(i,t)&&fS(e,i,t);return hS(e,i),e};Object.defineProperty(Wt,"__esModule",{value:!0});Wt.Address4=void 0;var Em=km(Hl()),jt=km(Gl()),Om=$s(),hn=Yl(),Nr=fn(),Kl=class i{constructor(e){this.groups=jt.GROUPS,this.parsedAddress=[],this.parsedSubnet="",this.subnet="/32",this.subnetMask=32,this.v4=!0,this.isCorrect=Em.isCorrect(jt.BITS),this.isInSubnet=Em.isInSubnet,this.address=e;let t=jt.RE_SUBNET_STRING.exec(e);if(t){if(this.parsedSubnet=t[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,this.subnetMask<0||this.subnetMask>jt.BITS)throw new Om.AddressError("Invalid subnet mask.");e=e.replace(jt.RE_SUBNET_STRING,"")}this.addressMinusSuffix=e,this.parsedAddress=this.parse(e)}static isValid(e){try{return new i(e),!0}catch{return!1}}parse(e){let t=e.split(".");if(!e.match(jt.RE_ADDRESS))throw new Om.AddressError("Invalid IPv4 address.");return t}correctForm(){return this.parsedAddress.map(e=>parseInt(e,10)).join(".")}static fromHex(e){let t=e.replace(/:/g,"").padStart(8,"0"),r=[],n;for(n=0;n<8;n+=2){let s=t.slice(n,n+2);r.push(parseInt(s,16))}return new i(r.join("."))}static fromInteger(e){return i.fromHex(e.toString(16))}static fromArpa(e){let r=e.replace(/(\.in-addr\.arpa)?\.$/,"").split(".").reverse().join(".");return new i(r)}toHex(){return this.parsedAddress.map(e=>(0,Nr.sprintf)("%02x",parseInt(e,10))).join(":")}toArray(){return this.parsedAddress.map(e=>parseInt(e,10))}toGroup6(){let e=[],t;for(t=0;t<jt.GROUPS;t+=2){let r=(0,Nr.sprintf)("%02x%02x",parseInt(this.parsedAddress[t],10),parseInt(this.parsedAddress[t+1],10));e.push((0,Nr.sprintf)("%x",parseInt(r,16)))}return e.join(":")}bigInteger(){return new hn.BigInteger(this.parsedAddress.map(e=>(0,Nr.sprintf)("%02x",parseInt(e,10))).join(""),16)}_startAddress(){return new hn.BigInteger(this.mask()+"0".repeat(jt.BITS-this.subnetMask),2)}startAddress(){return i.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new hn.BigInteger("1");return i.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new hn.BigInteger(this.mask()+"1".repeat(jt.BITS-this.subnetMask),2)}endAddress(){return i.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new hn.BigInteger("1");return i.fromBigInteger(this._endAddress().subtract(e))}static fromBigInteger(e){return i.fromInteger(parseInt(e.toString(),10))}mask(e){return e===void 0&&(e=this.subnetMask),this.getBitsBase2(0,e)}getBitsBase2(e,t){return this.binaryZeroPad().slice(e,t)}reverseForm(e){e||(e={});let t=this.correctForm().split(".").reverse().join(".");return e.omitSuffix?t:(0,Nr.sprintf)("%s.in-addr.arpa.",t)}isMulticast(){return this.isInSubnet(new i("*********/4"))}binaryZeroPad(){return this.bigInteger().toString(2).padStart(jt.BITS,"0")}groupForV6(){let e=this.parsedAddress;return this.address.replace(jt.RE_ADDRESS,(0,Nr.sprintf)('<span class="hover-group group-v4 group-6">%s</span>.<span class="hover-group group-v4 group-7">%s</span>',e.slice(0,2).join("."),e.slice(2,4).join(".")))}};Wt.Address4=Kl});var Jl=_(Fe=>{"use strict";Object.defineProperty(Fe,"__esModule",{value:!0});Fe.RE_URL_WITH_PORT=Fe.RE_URL=Fe.RE_ZONE_STRING=Fe.RE_SUBNET_STRING=Fe.RE_BAD_ADDRESS=Fe.RE_BAD_CHARACTERS=Fe.TYPES=Fe.SCOPES=Fe.GROUPS=Fe.BITS=void 0;Fe.BITS=128;Fe.GROUPS=8;Fe.SCOPES={0:"Reserved",1:"Interface local",2:"Link local",4:"Admin local",5:"Site local",8:"Organization local",14:"Global",15:"Reserved"};Fe.TYPES={"ff01::1/128":"Multicast (All nodes on this interface)","ff01::2/128":"Multicast (All routers on this interface)","ff02::1/128":"Multicast (All nodes on this link)","ff02::2/128":"Multicast (All routers on this link)","ff05::2/128":"Multicast (All routers in this site)","ff02::5/128":"Multicast (OSPFv3 AllSPF routers)","ff02::6/128":"Multicast (OSPFv3 AllDR routers)","ff02::9/128":"Multicast (RIP routers)","ff02::a/128":"Multicast (EIGRP routers)","ff02::d/128":"Multicast (PIM routers)","ff02::16/128":"Multicast (MLDv2 reports)","ff01::fb/128":"Multicast (mDNSv6)","ff02::fb/128":"Multicast (mDNSv6)","ff05::fb/128":"Multicast (mDNSv6)","ff02::1:2/128":"Multicast (All DHCP servers and relay agents on this link)","ff05::1:2/128":"Multicast (All DHCP servers and relay agents in this site)","ff02::1:3/128":"Multicast (All DHCP servers on this link)","ff05::1:3/128":"Multicast (All DHCP servers in this site)","::/128":"Unspecified","::1/128":"Loopback","ff00::/8":"Multicast","fe80::/10":"Link-local unicast"};Fe.RE_BAD_CHARACTERS=/([^0-9a-f:/%])/gi;Fe.RE_BAD_ADDRESS=/([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\/$)/gi;Fe.RE_SUBNET_STRING=/\/\d{1,3}(?=%|$)/;Fe.RE_ZONE_STRING=/%.*$/;Fe.RE_URL=new RegExp(/^\[{0,1}([0-9a-f:]+)\]{0,1}/);Fe.RE_URL_WITH_PORT=new RegExp(/\[([0-9a-f:]+)\]:([0-9]{1,5})/)});var Zl=_(Yt=>{"use strict";Object.defineProperty(Yt,"__esModule",{value:!0});Yt.simpleGroup=Yt.spanLeadingZeroes=Yt.spanAll=Yt.spanAllZeroes=void 0;var Cm=fn();function Tm(i){return i.replace(/(0+)/g,'<span class="zero">$1</span>')}Yt.spanAllZeroes=Tm;function pS(i,e=0){return i.split("").map((r,n)=>(0,Cm.sprintf)('<span class="digit value-%s position-%d">%s</span>',r,n+e,Tm(r))).join("")}Yt.spanAll=pS;function Am(i){return i.replace(/^(0+)/,'<span class="zero">$1</span>')}function dS(i){return i.split(":").map(t=>Am(t)).join(":")}Yt.spanLeadingZeroes=dS;function mS(i,e=0){return i.split(":").map((r,n)=>/group-v4/.test(r)?r:(0,Cm.sprintf)('<span class="hover-group group-%d">%s</span>',n+e,Am(r)))}Yt.simpleGroup=mS});var Im=_(Je=>{"use strict";var gS=Je&&Je.__createBinding||(Object.create?function(i,e,t,r){r===void 0&&(r=t);var n=Object.getOwnPropertyDescriptor(e,t);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(i,r,n)}:function(i,e,t,r){r===void 0&&(r=t),i[r]=e[t]}),vS=Je&&Je.__setModuleDefault||(Object.create?function(i,e){Object.defineProperty(i,"default",{enumerable:!0,value:e})}:function(i,e){i.default=e}),yS=Je&&Je.__importStar||function(i){if(i&&i.__esModule)return i;var e={};if(i!=null)for(var t in i)t!=="default"&&Object.prototype.hasOwnProperty.call(i,t)&&gS(e,i,t);return vS(e,i),e};Object.defineProperty(Je,"__esModule",{value:!0});Je.possibleElisions=Je.simpleRegularExpression=Je.ADDRESS_BOUNDARY=Je.padGroup=Je.groupPossibilities=void 0;var bS=yS(Jl()),Lr=fn();function Ws(i){return(0,Lr.sprintf)("(%s)",i.join("|"))}Je.groupPossibilities=Ws;function Gs(i){return i.length<4?(0,Lr.sprintf)("0{0,%d}%s",4-i.length,i):i}Je.padGroup=Gs;Je.ADDRESS_BOUNDARY="[^A-Fa-f0-9:]";function _S(i){let e=[];i.forEach((r,n)=>{parseInt(r,16)===0&&e.push(n)});let t=e.map(r=>i.map((n,s)=>{if(s===r){let o=s===0||s===bS.GROUPS-1?":":"";return Ws([Gs(n),o])}return Gs(n)}).join(":"));return t.push(i.map(Gs).join(":")),Ws(t)}Je.simpleRegularExpression=_S;function wS(i,e,t){let r=e?"":":",n=t?"":":",s=[];!e&&!t&&s.push("::"),e&&t&&s.push(""),(t&&!e||!t&&e)&&s.push(":"),s.push((0,Lr.sprintf)("%s(:0{1,4}){1,%d}",r,i-1)),s.push((0,Lr.sprintf)("(0{1,4}:){1,%d}%s",i-1,n)),s.push((0,Lr.sprintf)("(0{1,4}:){%d}0{1,4}",i-1));for(let o=1;o<i-1;o++)for(let a=1;a<i-o;a++)s.push((0,Lr.sprintf)("(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}",a,i-a-o-1));return Ws(s)}Je.possibleElisions=wS});var Rm=_(Kt=>{"use strict";var xS=Kt&&Kt.__createBinding||(Object.create?function(i,e,t,r){r===void 0&&(r=t);var n=Object.getOwnPropertyDescriptor(e,t);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(i,r,n)}:function(i,e,t,r){r===void 0&&(r=t),i[r]=e[t]}),SS=Kt&&Kt.__setModuleDefault||(Object.create?function(i,e){Object.defineProperty(i,"default",{enumerable:!0,value:e})}:function(i,e){i.default=e}),Ks=Kt&&Kt.__importStar||function(i){if(i&&i.__esModule)return i;var e={};if(i!=null)for(var t in i)t!=="default"&&Object.prototype.hasOwnProperty.call(i,t)&&xS(e,i,t);return SS(e,i),e};Object.defineProperty(Kt,"__esModule",{value:!0});Kt.Address6=void 0;var Nm=Ks(Hl()),Ql=Ks(Gl()),Be=Ks(Jl()),Xl=Ks(Zl()),Yi=zl(),Ki=Im(),oi=$s(),ct=Yl(),ut=fn();function Ys(i){if(!i)throw new Error("Assertion failed.")}function ES(i){let e=/(\d+)(\d{3})/;for(;e.test(i);)i=i.replace(e,"$1,$2");return i}function OS(i){return i=i.replace(/^(0{1,})([1-9]+)$/,'<span class="parse-error">$1</span>$2'),i=i.replace(/^(0{1,})(0)$/,'<span class="parse-error">$1</span>$2'),i}function kS(i,e){let t=[],r=[],n;for(n=0;n<i.length;n++)n<e[0]?t.push(i[n]):n>e[1]&&r.push(i[n]);return t.concat(["compact"]).concat(r)}function Lm(i){return(0,ut.sprintf)("%04x",parseInt(i,16))}function Bm(i){return i&255}var ec=class i{constructor(e,t){this.addressMinusSuffix="",this.parsedSubnet="",this.subnet="/128",this.subnetMask=128,this.v4=!1,this.zone="",this.isInSubnet=Nm.isInSubnet,this.isCorrect=Nm.isCorrect(Be.BITS),t===void 0?this.groups=Be.GROUPS:this.groups=t,this.address=e;let r=Be.RE_SUBNET_STRING.exec(e);if(r){if(this.parsedSubnet=r[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,Number.isNaN(this.subnetMask)||this.subnetMask<0||this.subnetMask>Be.BITS)throw new oi.AddressError("Invalid subnet mask.");e=e.replace(Be.RE_SUBNET_STRING,"")}else if(/\//.test(e))throw new oi.AddressError("Invalid subnet mask.");let n=Be.RE_ZONE_STRING.exec(e);n&&(this.zone=n[0],e=e.replace(Be.RE_ZONE_STRING,"")),this.addressMinusSuffix=e,this.parsedAddress=this.parse(this.addressMinusSuffix)}static isValid(e){try{return new i(e),!0}catch{return!1}}static fromBigInteger(e){let t=e.toString(16).padStart(32,"0"),r=[],n;for(n=0;n<Be.GROUPS;n++)r.push(t.slice(n*4,(n+1)*4));return new i(r.join(":"))}static fromURL(e){let t,r=null,n;if(e.indexOf("[")!==-1&&e.indexOf("]:")!==-1){if(n=Be.RE_URL_WITH_PORT.exec(e),n===null)return{error:"failed to parse address with port",address:null,port:null};t=n[1],r=n[2]}else if(e.indexOf("/")!==-1){if(e=e.replace(/^[a-z0-9]+:\/\//,""),n=Be.RE_URL.exec(e),n===null)return{error:"failed to parse address from URL",address:null,port:null};t=n[1]}else t=e;return r?(r=parseInt(r,10),(r<0||r>65536)&&(r=null)):r=null,{address:new i(t),port:r}}static fromAddress4(e){let t=new Yi.Address4(e),r=Be.BITS-(Ql.BITS-t.subnetMask);return new i(`::ffff:${t.correctForm()}/${r}`)}static fromArpa(e){let t=e.replace(/(\.ip6\.arpa)?\.$/,""),r=7;if(t.length!==63)throw new oi.AddressError("Invalid 'ip6.arpa' form.");let n=t.split(".").reverse();for(let s=r;s>0;s--){let o=s*4;n.splice(o,0,":")}return t=n.join(""),new i(t)}microsoftTranscription(){return(0,ut.sprintf)("%s.ipv6-literal.net",this.correctForm().replace(/:/g,"-"))}mask(e=this.subnetMask){return this.getBitsBase2(0,e)}possibleSubnets(e=128){let t=Be.BITS-this.subnetMask,r=Math.abs(e-Be.BITS),n=t-r;return n<0?"0":ES(new ct.BigInteger("2",10).pow(n).toString(10))}_startAddress(){return new ct.BigInteger(this.mask()+"0".repeat(Be.BITS-this.subnetMask),2)}startAddress(){return i.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new ct.BigInteger("1");return i.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new ct.BigInteger(this.mask()+"1".repeat(Be.BITS-this.subnetMask),2)}endAddress(){return i.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new ct.BigInteger("1");return i.fromBigInteger(this._endAddress().subtract(e))}getScope(){let e=Be.SCOPES[this.getBits(12,16).intValue()];return this.getType()==="Global unicast"&&e!=="Link local"&&(e="Global"),e||"Unknown"}getType(){for(let e of Object.keys(Be.TYPES))if(this.isInSubnet(new i(e)))return Be.TYPES[e];return"Global unicast"}getBits(e,t){return new ct.BigInteger(this.getBitsBase2(e,t),2)}getBitsBase2(e,t){return this.binaryZeroPad().slice(e,t)}getBitsBase16(e,t){let r=t-e;if(r%4!==0)throw new Error("Length of bits to retrieve must be divisible by four");return this.getBits(e,t).toString(16).padStart(r/4,"0")}getBitsPastSubnet(){return this.getBitsBase2(this.subnetMask,Be.BITS)}reverseForm(e){e||(e={});let t=Math.floor(this.subnetMask/4),r=this.canonicalForm().replace(/:/g,"").split("").slice(0,t).reverse().join(".");return t>0?e.omitSuffix?r:(0,ut.sprintf)("%s.ip6.arpa.",r):e.omitSuffix?"":"ip6.arpa."}correctForm(){let e,t=[],r=0,n=[];for(e=0;e<this.parsedAddress.length;e++){let a=parseInt(this.parsedAddress[e],16);a===0&&r++,a!==0&&r>0&&(r>1&&n.push([e-r,e-1]),r=0)}r>1&&n.push([this.parsedAddress.length-r,this.parsedAddress.length-1]);let s=n.map(a=>a[1]-a[0]+1);if(n.length>0){let a=s.indexOf(Math.max(...s));t=kS(this.parsedAddress,n[a])}else t=this.parsedAddress;for(e=0;e<t.length;e++)t[e]!=="compact"&&(t[e]=parseInt(t[e],16).toString(16));let o=t.join(":");return o=o.replace(/^compact$/,"::"),o=o.replace(/^compact|compact$/,":"),o=o.replace(/compact/,""),o}binaryZeroPad(){return this.bigInteger().toString(2).padStart(Be.BITS,"0")}parse4in6(e){let t=e.split(":"),n=t.slice(-1)[0].match(Ql.RE_ADDRESS);if(n){this.parsedAddress4=n[0],this.address4=new Yi.Address4(this.parsedAddress4);for(let s=0;s<this.address4.groups;s++)if(/^0[0-9]+/.test(this.address4.parsedAddress[s]))throw new oi.AddressError("IPv4 addresses can't have leading zeroes.",e.replace(Ql.RE_ADDRESS,this.address4.parsedAddress.map(OS).join(".")));this.v4=!0,t[t.length-1]=this.address4.toGroup6(),e=t.join(":")}return e}parse(e){e=this.parse4in6(e);let t=e.match(Be.RE_BAD_CHARACTERS);if(t)throw new oi.AddressError((0,ut.sprintf)("Bad character%s detected in address: %s",t.length>1?"s":"",t.join("")),e.replace(Be.RE_BAD_CHARACTERS,'<span class="parse-error">$1</span>'));let r=e.match(Be.RE_BAD_ADDRESS);if(r)throw new oi.AddressError((0,ut.sprintf)("Address failed regex: %s",r.join("")),e.replace(Be.RE_BAD_ADDRESS,'<span class="parse-error">$1</span>'));let n=[],s=e.split("::");if(s.length===2){let o=s[0].split(":"),a=s[1].split(":");o.length===1&&o[0]===""&&(o=[]),a.length===1&&a[0]===""&&(a=[]);let l=this.groups-(o.length+a.length);if(!l)throw new oi.AddressError("Error parsing groups");this.elidedGroups=l,this.elisionBegin=o.length,this.elisionEnd=o.length+this.elidedGroups,n=n.concat(o);for(let c=0;c<l;c++)n.push("0");n=n.concat(a)}else if(s.length===1)n=e.split(":"),this.elidedGroups=0;else throw new oi.AddressError("Too many :: groups found");if(n=n.map(o=>(0,ut.sprintf)("%x",parseInt(o,16))),n.length!==this.groups)throw new oi.AddressError("Incorrect number of groups found");return n}canonicalForm(){return this.parsedAddress.map(Lm).join(":")}decimal(){return this.parsedAddress.map(e=>(0,ut.sprintf)("%05d",parseInt(e,16))).join(":")}bigInteger(){return new ct.BigInteger(this.parsedAddress.map(Lm).join(""),16)}to4(){let e=this.binaryZeroPad().split("");return Yi.Address4.fromHex(new ct.BigInteger(e.slice(96,128).join(""),2).toString(16))}to4in6(){let e=this.to4(),r=new i(this.parsedAddress.slice(0,6).join(":"),6).correctForm(),n="";return/:$/.test(r)||(n=":"),r+n+e.address}inspectTeredo(){let e=this.getBitsBase16(0,32),t=this.getBits(80,96).xor(new ct.BigInteger("ffff",16)).toString(),r=Yi.Address4.fromHex(this.getBitsBase16(32,64)),n=Yi.Address4.fromHex(this.getBits(96,128).xor(new ct.BigInteger("ffffffff",16)).toString(16)),s=this.getBits(64,80),o=this.getBitsBase2(64,80),a=s.testBit(15),l=s.testBit(14),c=s.testBit(8),u=s.testBit(9),f=new ct.BigInteger(o.slice(2,6)+o.slice(8,16),2).toString(10);return{prefix:(0,ut.sprintf)("%s:%s",e.slice(0,4),e.slice(4,8)),server4:r.address,client4:n.address,flags:o,coneNat:a,microsoft:{reserved:l,universalLocal:u,groupIndividual:c,nonce:f},udpPort:t}}inspect6to4(){let e=this.getBitsBase16(0,16),t=Yi.Address4.fromHex(this.getBitsBase16(16,48));return{prefix:(0,ut.sprintf)("%s",e.slice(0,4)),gateway:t.address}}to6to4(){if(!this.is4())return null;let e=["2002",this.getBitsBase16(96,112),this.getBitsBase16(112,128),"","/16"].join(":");return new i(e)}toByteArray(){let e=this.bigInteger().toByteArray();return e.length===17&&e[0]===0?e.slice(1):e}toUnsignedByteArray(){return this.toByteArray().map(Bm)}static fromByteArray(e){return this.fromUnsignedByteArray(e.map(Bm))}static fromUnsignedByteArray(e){let t=new ct.BigInteger("256",10),r=new ct.BigInteger("0",10),n=new ct.BigInteger("1",10);for(let s=e.length-1;s>=0;s--)r=r.add(n.multiply(new ct.BigInteger(e[s].toString(10),10))),n=n.multiply(t);return i.fromBigInteger(r)}isCanonical(){return this.addressMinusSuffix===this.canonicalForm()}isLinkLocal(){return this.getBitsBase2(0,64)==="1111111010000000000000000000000000000000000000000000000000000000"}isMulticast(){return this.getType()==="Multicast"}is4(){return this.v4}isTeredo(){return this.isInSubnet(new i("2001::/32"))}is6to4(){return this.isInSubnet(new i("2002::/16"))}isLoopback(){return this.getType()==="Loopback"}href(e){return e===void 0?e="":e=(0,ut.sprintf)(":%s",e),(0,ut.sprintf)("http://[%s]%s/",this.correctForm(),e)}link(e){e||(e={}),e.className===void 0&&(e.className=""),e.prefix===void 0&&(e.prefix="/#address="),e.v4===void 0&&(e.v4=!1);let t=this.correctForm;return e.v4&&(t=this.to4in6),e.className?(0,ut.sprintf)('<a href="%1$s%2$s" class="%3$s">%2$s</a>',e.prefix,t.call(this),e.className):(0,ut.sprintf)('<a href="%1$s%2$s">%2$s</a>',e.prefix,t.call(this))}group(){if(this.elidedGroups===0)return Xl.simpleGroup(this.address).join(":");Ys(typeof this.elidedGroups=="number"),Ys(typeof this.elisionBegin=="number");let e=[],[t,r]=this.address.split("::");t.length?e.push(...Xl.simpleGroup(t)):e.push("");let n=["hover-group"];for(let s=this.elisionBegin;s<this.elisionBegin+this.elidedGroups;s++)n.push((0,ut.sprintf)("group-%d",s));return e.push((0,ut.sprintf)('<span class="%s"></span>',n.join(" "))),r.length?e.push(...Xl.simpleGroup(r,this.elisionEnd)):e.push(""),this.is4()&&(Ys(this.address4 instanceof Yi.Address4),e.pop(),e.push(this.address4.groupForV6())),e.join(":")}regularExpressionString(e=!1){let t=[],r=new i(this.correctForm());if(r.elidedGroups===0)t.push((0,Ki.simpleRegularExpression)(r.parsedAddress));else if(r.elidedGroups===Be.GROUPS)t.push((0,Ki.possibleElisions)(Be.GROUPS));else{let n=r.address.split("::");n[0].length&&t.push((0,Ki.simpleRegularExpression)(n[0].split(":"))),Ys(typeof r.elidedGroups=="number"),t.push((0,Ki.possibleElisions)(r.elidedGroups,n[0].length!==0,n[1].length!==0)),n[1].length&&t.push((0,Ki.simpleRegularExpression)(n[1].split(":"))),t=[t.join(":")]}return e||(t=["(?=^|",Ki.ADDRESS_BOUNDARY,"|[^\\w\\:])(",...t,")(?=[^\\w\\:]|",Ki.ADDRESS_BOUNDARY,"|$)"]),t.join("")}regularExpression(e=!1){return new RegExp(this.regularExpressionString(e),"i")}};Kt.Address6=ec});var tc=_(nt=>{"use strict";var CS=nt&&nt.__createBinding||(Object.create?function(i,e,t,r){r===void 0&&(r=t);var n=Object.getOwnPropertyDescriptor(e,t);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(i,r,n)}:function(i,e,t,r){r===void 0&&(r=t),i[r]=e[t]}),TS=nt&&nt.__setModuleDefault||(Object.create?function(i,e){Object.defineProperty(i,"default",{enumerable:!0,value:e})}:function(i,e){i.default=e}),AS=nt&&nt.__importStar||function(i){if(i&&i.__esModule)return i;var e={};if(i!=null)for(var t in i)t!=="default"&&Object.prototype.hasOwnProperty.call(i,t)&&CS(e,i,t);return TS(e,i),e};Object.defineProperty(nt,"__esModule",{value:!0});nt.v6=nt.AddressError=nt.Address6=nt.Address4=void 0;var IS=zl();Object.defineProperty(nt,"Address4",{enumerable:!0,get:function(){return IS.Address4}});var NS=Rm();Object.defineProperty(nt,"Address6",{enumerable:!0,get:function(){return NS.Address6}});var LS=$s();Object.defineProperty(nt,"AddressError",{enumerable:!0,get:function(){return LS.AddressError}});var BS=AS(Zl());nt.v6={helpers:BS}});var jm=_(Nt=>{"use strict";Object.defineProperty(Nt,"__esModule",{value:!0});Nt.ipToBuffer=Nt.int32ToIpv4=Nt.ipv4ToInt32=Nt.validateSocksClientChainOptions=Nt.validateSocksClientOptions=void 0;var ft=Vl(),Ze=Ul(),RS=require("stream"),ic=tc(),Pm=require("net");function PS(i,e=["connect","bind","associate"]){if(!Ze.SocksCommand[i.command])throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksCommand,i);if(e.indexOf(i.command)===-1)throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksCommandForOperation,i);if(!Dm(i.destination))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsDestination,i);if(!Fm(i.proxy))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsProxy,i);if(Mm(i.proxy,i),i.timeout&&!qm(i.timeout))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsTimeout,i);if(i.existing_socket&&!(i.existing_socket instanceof RS.Duplex))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsExistingSocket,i)}Nt.validateSocksClientOptions=PS;function MS(i){if(i.command!=="connect")throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksCommandChain,i);if(!Dm(i.destination))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsDestination,i);if(!(i.proxies&&Array.isArray(i.proxies)&&i.proxies.length>=2))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsProxiesLength,i);if(i.proxies.forEach(e=>{if(!Fm(e))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsProxy,i);Mm(e,i)}),i.timeout&&!qm(i.timeout))throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsTimeout,i)}Nt.validateSocksClientChainOptions=MS;function Mm(i,e){if(i.custom_auth_method!==void 0){if(i.custom_auth_method<Ze.SOCKS5_CUSTOM_AUTH_START||i.custom_auth_method>Ze.SOCKS5_CUSTOM_AUTH_END)throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsCustomAuthRange,e);if(i.custom_auth_request_handler===void 0||typeof i.custom_auth_request_handler!="function")throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(i.custom_auth_response_size===void 0)throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(i.custom_auth_response_handler===void 0||typeof i.custom_auth_response_handler!="function")throw new ft.SocksClientError(Ze.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e)}}function Dm(i){return i&&typeof i.host=="string"&&typeof i.port=="number"&&i.port>=0&&i.port<=65535}function Fm(i){return i&&(typeof i.host=="string"||typeof i.ipaddress=="string")&&typeof i.port=="number"&&i.port>=0&&i.port<=65535&&(i.type===4||i.type===5)}function qm(i){return typeof i=="number"&&i>0}function DS(i){return new ic.Address4(i).toArray().reduce((t,r)=>(t<<8)+r,0)}Nt.ipv4ToInt32=DS;function FS(i){let e=i>>>24&255,t=i>>>16&255,r=i>>>8&255,n=i&255;return[e,t,r,n].join(".")}Nt.int32ToIpv4=FS;function qS(i){if(Pm.isIPv4(i)){let e=new ic.Address4(i);return Buffer.from(e.toArray())}else if(Pm.isIPv6(i)){let e=new ic.Address6(i);return Buffer.from(e.canonicalForm().split(":").map(t=>t.padStart(4,"0")).join(""),"hex")}else throw new Error("Invalid IP address format")}Nt.ipToBuffer=qS});var Um=_(zs=>{"use strict";Object.defineProperty(zs,"__esModule",{value:!0});zs.ReceiveBuffer=void 0;var rc=class{constructor(e=4096){this.buffer=Buffer.allocUnsafe(e),this.offset=0,this.originalSize=e}get length(){return this.offset}append(e){if(!Buffer.isBuffer(e))throw new Error("Attempted to append a non-buffer instance to ReceiveBuffer.");if(this.offset+e.length>=this.buffer.length){let t=this.buffer;this.buffer=Buffer.allocUnsafe(Math.max(this.buffer.length+this.originalSize,this.buffer.length+e.length)),t.copy(this.buffer)}return e.copy(this.buffer,this.offset),this.offset+=e.length}peek(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");return this.buffer.slice(0,e)}get(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");let t=Buffer.allocUnsafe(e);return this.buffer.slice(0,e).copy(t),this.buffer.copyWithin(0,e,e+this.offset-e),this.offset-=e,t}};zs.ReceiveBuffer=rc});var $m=_(yi=>{"use strict";var Br=yi&&yi.__awaiter||function(i,e,t,r){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(u){try{c(r.next(u))}catch(f){o(f)}}function l(u){try{c(r.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})};Object.defineProperty(yi,"__esModule",{value:!0});yi.SocksClientError=yi.SocksClient=void 0;var jS=require("events"),Rr=require("net"),mt=gm(),D=Ul(),St=jm(),US=Um(),sc=Vl();Object.defineProperty(yi,"SocksClientError",{enumerable:!0,get:function(){return sc.SocksClientError}});var nc=tc(),oc=class i extends jS.EventEmitter{constructor(e){super(),this.options=Object.assign({},e),(0,St.validateSocksClientOptions)(e),this.setState(D.SocksClientState.Created)}static createConnection(e,t){return new Promise((r,n)=>{try{(0,St.validateSocksClientOptions)(e,["connect"])}catch(o){return typeof t=="function"?(t(o),r(o)):n(o)}let s=new i(e);s.connect(e.existing_socket),s.once("established",o=>{s.removeAllListeners(),typeof t=="function"&&t(null,o),r(o)}),s.once("error",o=>{s.removeAllListeners(),typeof t=="function"?(t(o),r(o)):n(o)})})}static createConnectionChain(e,t){return new Promise((r,n)=>Br(this,void 0,void 0,function*(){try{(0,St.validateSocksClientChainOptions)(e)}catch(s){return typeof t=="function"?(t(s),r(s)):n(s)}e.randomizeChain&&(0,sc.shuffleArray)(e.proxies);try{let s;for(let o=0;o<e.proxies.length;o++){let a=e.proxies[o],l=o===e.proxies.length-1?e.destination:{host:e.proxies[o+1].host||e.proxies[o+1].ipaddress,port:e.proxies[o+1].port},c=yield i.createConnection({command:"connect",proxy:a,destination:l,existing_socket:s});s=s||c.socket}typeof t=="function"?(t(null,{socket:s}),r({socket:s})):r({socket:s})}catch(s){typeof t=="function"?(t(s),r(s)):n(s)}}))}static createUDPFrame(e){let t=new mt.SmartBuffer;return t.writeUInt16BE(0),t.writeUInt8(e.frameNumber||0),Rr.isIPv4(e.remoteHost.host)?(t.writeUInt8(D.Socks5HostType.IPv4),t.writeUInt32BE((0,St.ipv4ToInt32)(e.remoteHost.host))):Rr.isIPv6(e.remoteHost.host)?(t.writeUInt8(D.Socks5HostType.IPv6),t.writeBuffer((0,St.ipToBuffer)(e.remoteHost.host))):(t.writeUInt8(D.Socks5HostType.Hostname),t.writeUInt8(Buffer.byteLength(e.remoteHost.host)),t.writeString(e.remoteHost.host)),t.writeUInt16BE(e.remoteHost.port),t.writeBuffer(e.data),t.toBuffer()}static parseUDPFrame(e){let t=mt.SmartBuffer.fromBuffer(e);t.readOffset=2;let r=t.readUInt8(),n=t.readUInt8(),s;n===D.Socks5HostType.IPv4?s=(0,St.int32ToIpv4)(t.readUInt32BE()):n===D.Socks5HostType.IPv6?s=nc.Address6.fromByteArray(Array.from(t.readBuffer(16))).canonicalForm():s=t.readString(t.readUInt8());let o=t.readUInt16BE();return{frameNumber:r,remoteHost:{host:s,port:o},data:t.readBuffer()}}setState(e){this.state!==D.SocksClientState.Error&&(this.state=e)}connect(e){this.onDataReceived=r=>this.onDataReceivedHandler(r),this.onClose=()=>this.onCloseHandler(),this.onError=r=>this.onErrorHandler(r),this.onConnect=()=>this.onConnectHandler();let t=setTimeout(()=>this.onEstablishedTimeout(),this.options.timeout||D.DEFAULT_TIMEOUT);t.unref&&typeof t.unref=="function"&&t.unref(),e?this.socket=e:this.socket=new Rr.Socket,this.socket.once("close",this.onClose),this.socket.once("error",this.onError),this.socket.once("connect",this.onConnect),this.socket.on("data",this.onDataReceived),this.setState(D.SocksClientState.Connecting),this.receiveBuffer=new US.ReceiveBuffer,e?this.socket.emit("connect"):(this.socket.connect(this.getSocketOptions()),this.options.set_tcp_nodelay!==void 0&&this.options.set_tcp_nodelay!==null&&this.socket.setNoDelay(!!this.options.set_tcp_nodelay)),this.prependOnceListener("established",r=>{setImmediate(()=>{if(this.receiveBuffer.length>0){let n=this.receiveBuffer.get(this.receiveBuffer.length);r.socket.emit("data",n)}r.socket.resume()})})}getSocketOptions(){return Object.assign(Object.assign({},this.options.socket_options),{host:this.options.proxy.host||this.options.proxy.ipaddress,port:this.options.proxy.port})}onEstablishedTimeout(){this.state!==D.SocksClientState.Established&&this.state!==D.SocksClientState.BoundWaitingForConnection&&this.closeSocket(D.ERRORS.ProxyConnectionTimedOut)}onConnectHandler(){this.setState(D.SocksClientState.Connected),this.options.proxy.type===4?this.sendSocks4InitialHandshake():this.sendSocks5InitialHandshake(),this.setState(D.SocksClientState.SentInitialHandshake)}onDataReceivedHandler(e){this.receiveBuffer.append(e),this.processData()}processData(){for(;this.state!==D.SocksClientState.Established&&this.state!==D.SocksClientState.Error&&this.receiveBuffer.length>=this.nextRequiredPacketBufferSize;)if(this.state===D.SocksClientState.SentInitialHandshake)this.options.proxy.type===4?this.handleSocks4FinalHandshakeResponse():this.handleInitialSocks5HandshakeResponse();else if(this.state===D.SocksClientState.SentAuthentication)this.handleInitialSocks5AuthenticationHandshakeResponse();else if(this.state===D.SocksClientState.SentFinalHandshake)this.handleSocks5FinalHandshakeResponse();else if(this.state===D.SocksClientState.BoundWaitingForConnection)this.options.proxy.type===4?this.handleSocks4IncomingConnectionResponse():this.handleSocks5IncomingConnectionResponse();else{this.closeSocket(D.ERRORS.InternalError);break}}onCloseHandler(){this.closeSocket(D.ERRORS.SocketClosed)}onErrorHandler(e){this.closeSocket(e.message)}removeInternalSocketHandlers(){this.socket.pause(),this.socket.removeListener("data",this.onDataReceived),this.socket.removeListener("close",this.onClose),this.socket.removeListener("error",this.onError),this.socket.removeListener("connect",this.onConnect)}closeSocket(e){this.state!==D.SocksClientState.Error&&(this.setState(D.SocksClientState.Error),this.socket.destroy(),this.removeInternalSocketHandlers(),this.emit("error",new sc.SocksClientError(e,this.options)))}sendSocks4InitialHandshake(){let e=this.options.proxy.userId||"",t=new mt.SmartBuffer;t.writeUInt8(4),t.writeUInt8(D.SocksCommand[this.options.command]),t.writeUInt16BE(this.options.destination.port),Rr.isIPv4(this.options.destination.host)?(t.writeBuffer((0,St.ipToBuffer)(this.options.destination.host)),t.writeStringNT(e)):(t.writeUInt8(0),t.writeUInt8(0),t.writeUInt8(0),t.writeUInt8(1),t.writeStringNT(e),t.writeStringNT(this.options.destination.host)),this.nextRequiredPacketBufferSize=D.SOCKS_INCOMING_PACKET_SIZES.Socks4Response,this.socket.write(t.toBuffer())}handleSocks4FinalHandshakeResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==D.Socks4Response.Granted)this.closeSocket(`${D.ERRORS.Socks4ProxyRejectedConnection} - (${D.Socks4Response[e[1]]})`);else if(D.SocksCommand[this.options.command]===D.SocksCommand.bind){let t=mt.SmartBuffer.fromBuffer(e);t.readOffset=2;let r={port:t.readUInt16BE(),host:(0,St.int32ToIpv4)(t.readUInt32BE())};r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress),this.setState(D.SocksClientState.BoundWaitingForConnection),this.emit("bound",{remoteHost:r,socket:this.socket})}else this.setState(D.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{socket:this.socket})}handleSocks4IncomingConnectionResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==D.Socks4Response.Granted)this.closeSocket(`${D.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${D.Socks4Response[e[1]]})`);else{let t=mt.SmartBuffer.fromBuffer(e);t.readOffset=2;let r={port:t.readUInt16BE(),host:(0,St.int32ToIpv4)(t.readUInt32BE())};this.setState(D.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}sendSocks5InitialHandshake(){let e=new mt.SmartBuffer,t=[D.Socks5Auth.NoAuth];(this.options.proxy.userId||this.options.proxy.password)&&t.push(D.Socks5Auth.UserPass),this.options.proxy.custom_auth_method!==void 0&&t.push(this.options.proxy.custom_auth_method),e.writeUInt8(5),e.writeUInt8(t.length);for(let r of t)e.writeUInt8(r);this.nextRequiredPacketBufferSize=D.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse,this.socket.write(e.toBuffer()),this.setState(D.SocksClientState.SentInitialHandshake)}handleInitialSocks5HandshakeResponse(){let e=this.receiveBuffer.get(2);e[0]!==5?this.closeSocket(D.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion):e[1]===D.SOCKS5_NO_ACCEPTABLE_AUTH?this.closeSocket(D.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType):e[1]===D.Socks5Auth.NoAuth?(this.socks5ChosenAuthType=D.Socks5Auth.NoAuth,this.sendSocks5CommandRequest()):e[1]===D.Socks5Auth.UserPass?(this.socks5ChosenAuthType=D.Socks5Auth.UserPass,this.sendSocks5UserPassAuthentication()):e[1]===this.options.proxy.custom_auth_method?(this.socks5ChosenAuthType=this.options.proxy.custom_auth_method,this.sendSocks5CustomAuthentication()):this.closeSocket(D.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType)}sendSocks5UserPassAuthentication(){let e=this.options.proxy.userId||"",t=this.options.proxy.password||"",r=new mt.SmartBuffer;r.writeUInt8(1),r.writeUInt8(Buffer.byteLength(e)),r.writeString(e),r.writeUInt8(Buffer.byteLength(t)),r.writeString(t),this.nextRequiredPacketBufferSize=D.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse,this.socket.write(r.toBuffer()),this.setState(D.SocksClientState.SentAuthentication)}sendSocks5CustomAuthentication(){return Br(this,void 0,void 0,function*(){this.nextRequiredPacketBufferSize=this.options.proxy.custom_auth_response_size,this.socket.write(yield this.options.proxy.custom_auth_request_handler()),this.setState(D.SocksClientState.SentAuthentication)})}handleSocks5CustomAuthHandshakeResponse(e){return Br(this,void 0,void 0,function*(){return yield this.options.proxy.custom_auth_response_handler(e)})}handleSocks5AuthenticationNoAuthHandshakeResponse(e){return Br(this,void 0,void 0,function*(){return e[1]===0})}handleSocks5AuthenticationUserPassHandshakeResponse(e){return Br(this,void 0,void 0,function*(){return e[1]===0})}handleInitialSocks5AuthenticationHandshakeResponse(){return Br(this,void 0,void 0,function*(){this.setState(D.SocksClientState.ReceivedAuthenticationResponse);let e=!1;this.socks5ChosenAuthType===D.Socks5Auth.NoAuth?e=yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===D.Socks5Auth.UserPass?e=yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===this.options.proxy.custom_auth_method&&(e=yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size))),e?this.sendSocks5CommandRequest():this.closeSocket(D.ERRORS.Socks5AuthenticationFailed)})}sendSocks5CommandRequest(){let e=new mt.SmartBuffer;e.writeUInt8(5),e.writeUInt8(D.SocksCommand[this.options.command]),e.writeUInt8(0),Rr.isIPv4(this.options.destination.host)?(e.writeUInt8(D.Socks5HostType.IPv4),e.writeBuffer((0,St.ipToBuffer)(this.options.destination.host))):Rr.isIPv6(this.options.destination.host)?(e.writeUInt8(D.Socks5HostType.IPv6),e.writeBuffer((0,St.ipToBuffer)(this.options.destination.host))):(e.writeUInt8(D.Socks5HostType.Hostname),e.writeUInt8(this.options.destination.host.length),e.writeString(this.options.destination.host)),e.writeUInt16BE(this.options.destination.port),this.nextRequiredPacketBufferSize=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.socket.write(e.toBuffer()),this.setState(D.SocksClientState.SentFinalHandshake)}handleSocks5FinalHandshakeResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==D.Socks5Response.Granted)this.closeSocket(`${D.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${D.Socks5Response[e[1]]}`);else{let t=e[3],r,n;if(t===D.Socks5HostType.IPv4){let s=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=mt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,St.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(t===D.Socks5HostType.Hostname){let s=e[4],o=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=mt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(t===D.Socks5HostType.IPv6){let s=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=mt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:nc.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(D.SocksClientState.ReceivedFinalResponse),D.SocksCommand[this.options.command]===D.SocksCommand.connect?(this.setState(D.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})):D.SocksCommand[this.options.command]===D.SocksCommand.bind?(this.setState(D.SocksClientState.BoundWaitingForConnection),this.nextRequiredPacketBufferSize=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.emit("bound",{remoteHost:r,socket:this.socket})):D.SocksCommand[this.options.command]===D.SocksCommand.associate&&(this.setState(D.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket}))}}handleSocks5IncomingConnectionResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==D.Socks5Response.Granted)this.closeSocket(`${D.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${D.Socks5Response[e[1]]}`);else{let t=e[3],r,n;if(t===D.Socks5HostType.IPv4){let s=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=mt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,St.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(t===D.Socks5HostType.Hostname){let s=e[4],o=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=mt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(t===D.Socks5HostType.IPv6){let s=D.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=mt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:nc.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(D.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}get socksClientOptions(){return Object.assign({},this.options)}};yi.SocksClient=oc});var Vm=_(zi=>{"use strict";var $S=zi&&zi.__createBinding||(Object.create?function(i,e,t,r){r===void 0&&(r=t);var n=Object.getOwnPropertyDescriptor(e,t);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(i,r,n)}:function(i,e,t,r){r===void 0&&(r=t),i[r]=e[t]}),VS=zi&&zi.__exportStar||function(i,e){for(var t in i)t!=="default"&&!Object.prototype.hasOwnProperty.call(e,t)&&$S(e,i,t)};Object.defineProperty(zi,"__esModule",{value:!0});VS($m(),zi)});var Hm=_(Ji=>{"use strict";var HS=Ji&&Ji.__awaiter||function(i,e,t,r){function n(s){return s instanceof t?s:new t(function(o){o(s)})}return new(t||(t=Promise))(function(s,o){function a(u){try{c(r.next(u))}catch(f){o(f)}}function l(u){try{c(r.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((r=r.apply(i,e||[])).next())})},Js=Ji&&Ji.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Ji,"__esModule",{value:!0});var GS=Js(require("dns")),WS=Js(require("tls")),YS=Js(require("url")),KS=Js(br()),zS=$a(),JS=Vm(),ac=KS.default("socks-proxy-agent");function ZS(i){return new Promise((e,t)=>{GS.default.lookup(i,(r,n)=>{r?t(r):e(n)})})}function QS(i){let e=0,t=!1,r=5,n=i.hostname||i.host;if(!n)throw new TypeError('No "host"');if(typeof i.port=="number"?e=i.port:typeof i.port=="string"&&(e=parseInt(i.port,10)),e||(e=1080),i.protocol)switch(i.protocol.replace(":","")){case"socks4":t=!0;case"socks4a":r=4;break;case"socks5":t=!0;case"socks":case"socks5h":r=5;break;default:throw new TypeError(`A "socks" protocol must be specified! Got: ${i.protocol}`)}if(typeof i.type!="undefined")if(i.type===4||i.type===5)r=i.type;else throw new TypeError(`"type" must be 4 or 5, got: ${i.type}`);let s={host:n,port:e,type:r},o=i.userId||i.username,a=i.password;if(i.auth){let l=i.auth.split(":");o=l[0],a=l[1]}return o&&Object.defineProperty(s,"userId",{value:o,enumerable:!1}),a&&Object.defineProperty(s,"password",{value:a,enumerable:!1}),{lookup:t,proxy:s}}var lc=class extends zS.Agent{constructor(e){let t;if(typeof e=="string"?t=YS.default.parse(e):t=e,!t)throw new TypeError("a SOCKS proxy server `host` and `port` must be specified!");super(t);let r=QS(t);this.lookup=r.lookup,this.proxy=r.proxy,this.tlsConnectionOptions=t.tls||{}}callback(e,t){return HS(this,void 0,void 0,function*(){let{lookup:r,proxy:n}=this,{host:s,port:o,timeout:a}=t;if(!s)throw new Error("No `host` defined!");r&&(s=yield ZS(s));let l={proxy:n,destination:{host:s,port:o},command:"connect",timeout:a};ac("Creating socks proxy connection: %o",l);let{socket:c}=yield JS.SocksClient.createConnection(l);if(ac("Successfully created socks proxy connection"),t.secureEndpoint){ac("Upgrading socket connection to TLS");let u=t.servername||t.host;return WS.default.connect(Object.assign(Object.assign(Object.assign({},XS(t,"host","hostname","path","port")),{socket:c,servername:u}),this.tlsConnectionOptions))}return c})}};Ji.default=lc;function XS(i,...e){let t={},r;for(r in i)e.includes(r)||(t[r]=i[r]);return t}});var Wm=_((fc,Gm)=>{"use strict";var eE=fc&&fc.__importDefault||function(i){return i&&i.__esModule?i:{default:i}},cc=eE(Hm());function uc(i){return new cc.default(i)}(function(i){i.SocksProxyAgent=cc.default,i.prototype=cc.default.prototype})(uc||(uc={}));Gm.exports=uc});var Se=_(tt=>{"use strict";var hc=Symbol.for("yaml.alias"),Ym=Symbol.for("yaml.document"),Zs=Symbol.for("yaml.map"),Km=Symbol.for("yaml.pair"),pc=Symbol.for("yaml.scalar"),Qs=Symbol.for("yaml.seq"),ai=Symbol.for("yaml.node.type"),tE=i=>!!i&&typeof i=="object"&&i[ai]===hc,iE=i=>!!i&&typeof i=="object"&&i[ai]===Ym,rE=i=>!!i&&typeof i=="object"&&i[ai]===Zs,nE=i=>!!i&&typeof i=="object"&&i[ai]===Km,zm=i=>!!i&&typeof i=="object"&&i[ai]===pc,sE=i=>!!i&&typeof i=="object"&&i[ai]===Qs;function Jm(i){if(i&&typeof i=="object")switch(i[ai]){case Zs:case Qs:return!0}return!1}function oE(i){if(i&&typeof i=="object")switch(i[ai]){case hc:case Zs:case pc:case Qs:return!0}return!1}var aE=i=>(zm(i)||Jm(i))&&!!i.anchor;tt.ALIAS=hc;tt.DOC=Ym;tt.MAP=Zs;tt.NODE_TYPE=ai;tt.PAIR=Km;tt.SCALAR=pc;tt.SEQ=Qs;tt.hasAnchor=aE;tt.isAlias=tE;tt.isCollection=Jm;tt.isDocument=iE;tt.isMap=rE;tt.isNode=oE;tt.isPair=nE;tt.isScalar=zm;tt.isSeq=sE});var pn=_(dc=>{"use strict";var Ge=Se(),gt=Symbol("break visit"),Zm=Symbol("skip children"),zt=Symbol("remove node");function Xs(i,e){let t=Qm(e);Ge.isDocument(i)?Pr(null,i.contents,t,Object.freeze([i]))===zt&&(i.contents=null):Pr(null,i,t,Object.freeze([]))}Xs.BREAK=gt;Xs.SKIP=Zm;Xs.REMOVE=zt;function Pr(i,e,t,r){let n=Xm(i,e,t,r);if(Ge.isNode(n)||Ge.isPair(n))return eg(i,r,n),Pr(i,n,t,r);if(typeof n!="symbol"){if(Ge.isCollection(e)){r=Object.freeze(r.concat(e));for(let s=0;s<e.items.length;++s){let o=Pr(s,e.items[s],t,r);if(typeof o=="number")s=o-1;else{if(o===gt)return gt;o===zt&&(e.items.splice(s,1),s-=1)}}}else if(Ge.isPair(e)){r=Object.freeze(r.concat(e));let s=Pr("key",e.key,t,r);if(s===gt)return gt;s===zt&&(e.key=null);let o=Pr("value",e.value,t,r);if(o===gt)return gt;o===zt&&(e.value=null)}}return n}async function eo(i,e){let t=Qm(e);Ge.isDocument(i)?await Mr(null,i.contents,t,Object.freeze([i]))===zt&&(i.contents=null):await Mr(null,i,t,Object.freeze([]))}eo.BREAK=gt;eo.SKIP=Zm;eo.REMOVE=zt;async function Mr(i,e,t,r){let n=await Xm(i,e,t,r);if(Ge.isNode(n)||Ge.isPair(n))return eg(i,r,n),Mr(i,n,t,r);if(typeof n!="symbol"){if(Ge.isCollection(e)){r=Object.freeze(r.concat(e));for(let s=0;s<e.items.length;++s){let o=await Mr(s,e.items[s],t,r);if(typeof o=="number")s=o-1;else{if(o===gt)return gt;o===zt&&(e.items.splice(s,1),s-=1)}}}else if(Ge.isPair(e)){r=Object.freeze(r.concat(e));let s=await Mr("key",e.key,t,r);if(s===gt)return gt;s===zt&&(e.key=null);let o=await Mr("value",e.value,t,r);if(o===gt)return gt;o===zt&&(e.value=null)}}return n}function Qm(i){return typeof i=="object"&&(i.Collection||i.Node||i.Value)?Object.assign({Alias:i.Node,Map:i.Node,Scalar:i.Node,Seq:i.Node},i.Value&&{Map:i.Value,Scalar:i.Value,Seq:i.Value},i.Collection&&{Map:i.Collection,Seq:i.Collection},i):i}function Xm(i,e,t,r){var n,s,o,a,l;if(typeof t=="function")return t(i,e,r);if(Ge.isMap(e))return(n=t.Map)==null?void 0:n.call(t,i,e,r);if(Ge.isSeq(e))return(s=t.Seq)==null?void 0:s.call(t,i,e,r);if(Ge.isPair(e))return(o=t.Pair)==null?void 0:o.call(t,i,e,r);if(Ge.isScalar(e))return(a=t.Scalar)==null?void 0:a.call(t,i,e,r);if(Ge.isAlias(e))return(l=t.Alias)==null?void 0:l.call(t,i,e,r)}function eg(i,e,t){let r=e[e.length-1];if(Ge.isCollection(r))r.items[i]=t;else if(Ge.isPair(r))i==="key"?r.key=t:r.value=t;else if(Ge.isDocument(r))r.contents=t;else{let n=Ge.isAlias(r)?"alias":"scalar";throw new Error(`Cannot replace node with ${n} parent`)}}dc.visit=Xs;dc.visitAsync=eo});var mc=_(ig=>{"use strict";var tg=Se(),lE=pn(),cE={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},uE=i=>i.replace(/[!,[\]{}]/g,e=>cE[e]),dn=class i{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},i.defaultYaml,e),this.tags=Object.assign({},i.defaultTags,t)}clone(){let e=new i(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){let e=new i(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:i.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},i.defaultTags);break}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:i.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},i.defaultTags),this.atNextDocument=!1);let r=e.trim().split(/[ \t]+/),n=r.shift();switch(n){case"%TAG":{if(r.length!==2&&(t(0,"%TAG directive should contain exactly two parts"),r.length<2))return!1;let[s,o]=r;return this.tags[s]=o,!0}case"%YAML":{if(this.yaml.explicit=!0,r.length!==1)return t(0,"%YAML directive should contain exactly one part"),!1;let[s]=r;if(s==="1.1"||s==="1.2")return this.yaml.version=s,!0;{let o=/^\d+\.\d+$/.test(s);return t(6,`Unsupported YAML version ${s}`,o),!1}}default:return t(0,`Unknown directive ${n}`,!0),!1}}tagName(e,t){if(e==="!")return"!";if(e[0]!=="!")return t(`Not a valid tag: ${e}`),null;if(e[1]==="<"){let o=e.slice(2,-1);return o==="!"||o==="!!"?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(e[e.length-1]!==">"&&t("Verbatim tags must end with a >"),o)}let[,r,n]=e.match(/^(.*!)([^!]*)$/s);n||t(`The ${e} tag has no suffix`);let s=this.tags[r];if(s)try{return s+decodeURIComponent(n)}catch(o){return t(String(o)),null}return r==="!"?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(let[t,r]of Object.entries(this.tags))if(e.startsWith(r))return t+uE(e.substring(r.length));return e[0]==="!"?e:`!<${e}>`}toString(e){let t=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],r=Object.entries(this.tags),n;if(e&&r.length>0&&tg.isNode(e.contents)){let s={};lE.visit(e.contents,(o,a)=>{tg.isNode(a)&&a.tag&&(s[a.tag]=!0)}),n=Object.keys(s)}else n=[];for(let[s,o]of r)s==="!!"&&o==="tag:yaml.org,2002:"||(!e||n.some(a=>a.startsWith(o)))&&t.push(`%TAG ${s} ${o}`);return t.join(`
`)}};dn.defaultYaml={explicit:!1,version:"1.2"};dn.defaultTags={"!!":"tag:yaml.org,2002:"};ig.Directives=dn});var to=_(mn=>{"use strict";var rg=Se(),fE=pn();function hE(i){if(/[\x00-\x19\s,[\]{}]/.test(i)){let t=`Anchor must not contain whitespace or control characters: ${JSON.stringify(i)}`;throw new Error(t)}return!0}function ng(i){let e=new Set;return fE.visit(i,{Value(t,r){r.anchor&&e.add(r.anchor)}}),e}function sg(i,e){for(let t=1;;++t){let r=`${i}${t}`;if(!e.has(r))return r}}function pE(i,e){let t=[],r=new Map,n=null;return{onAnchor:s=>{t.push(s),n||(n=ng(i));let o=sg(e,n);return n.add(o),o},setAnchors:()=>{for(let s of t){let o=r.get(s);if(typeof o=="object"&&o.anchor&&(rg.isScalar(o.node)||rg.isCollection(o.node)))o.node.anchor=o.anchor;else{let a=new Error("Failed to resolve repeated object (this should not happen)");throw a.source=s,a}}},sourceObjects:r}}mn.anchorIsValid=hE;mn.anchorNames=ng;mn.createNodeAnchors=pE;mn.findNewAnchor=sg});var gc=_(og=>{"use strict";function gn(i,e,t,r){if(r&&typeof r=="object")if(Array.isArray(r))for(let n=0,s=r.length;n<s;++n){let o=r[n],a=gn(i,r,String(n),o);a===void 0?delete r[n]:a!==o&&(r[n]=a)}else if(r instanceof Map)for(let n of Array.from(r.keys())){let s=r.get(n),o=gn(i,r,n,s);o===void 0?r.delete(n):o!==s&&r.set(n,o)}else if(r instanceof Set)for(let n of Array.from(r)){let s=gn(i,r,n,n);s===void 0?r.delete(n):s!==n&&(r.delete(n),r.add(s))}else for(let[n,s]of Object.entries(r)){let o=gn(i,r,n,s);o===void 0?delete r[n]:o!==s&&(r[n]=o)}return i.call(e,t,r)}og.applyReviver=gn});var bi=_(lg=>{"use strict";var dE=Se();function ag(i,e,t){if(Array.isArray(i))return i.map((r,n)=>ag(r,String(n),t));if(i&&typeof i.toJSON=="function"){if(!t||!dE.hasAnchor(i))return i.toJSON(e,t);let r={aliasCount:0,count:1,res:void 0};t.anchors.set(i,r),t.onCreate=s=>{r.res=s,delete t.onCreate};let n=i.toJSON(e,t);return t.onCreate&&t.onCreate(n),n}return typeof i=="bigint"&&!(t!=null&&t.keep)?Number(i):i}lg.toJS=ag});var io=_(ug=>{"use strict";var mE=gc(),cg=Se(),gE=bi(),vc=class{constructor(e){Object.defineProperty(this,cg.NODE_TYPE,{value:e})}clone(){let e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:t,maxAliasCount:r,onAnchor:n,reviver:s}={}){if(!cg.isDocument(e))throw new TypeError("A document argument is required");let o={anchors:new Map,doc:e,keep:!0,mapAsMap:t===!0,mapKeyWarned:!1,maxAliasCount:typeof r=="number"?r:100},a=gE.toJS(this,"",o);if(typeof n=="function")for(let{count:l,res:c}of o.anchors.values())n(c,l);return typeof s=="function"?mE.applyReviver(s,{"":a},"",a):a}};ug.NodeBase=vc});var vn=_(hg=>{"use strict";var vE=to(),fg=pn(),ro=Se(),yE=io(),bE=bi(),yc=class extends yE.NodeBase{constructor(e){super(ro.ALIAS),this.source=e,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(e){let t;return fg.visit(e,{Node:(r,n)=>{if(n===this)return fg.visit.BREAK;n.anchor===this.source&&(t=n)}}),t}toJSON(e,t){if(!t)return{source:this.source};let{anchors:r,doc:n,maxAliasCount:s}=t,o=this.resolve(n);if(!o){let l=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(l)}let a=r.get(o);if(a||(bE.toJS(o,null,t),a=r.get(o)),!a||a.res===void 0){let l="This should not happen: Alias anchor was not resolved?";throw new ReferenceError(l)}if(s>=0&&(a.count+=1,a.aliasCount===0&&(a.aliasCount=no(n,o,r)),a.count*a.aliasCount>s)){let l="Excessive alias count indicates a resource exhaustion attack";throw new ReferenceError(l)}return a.res}toString(e,t,r){let n=`*${this.source}`;if(e){if(vE.anchorIsValid(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source)){let s=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(s)}if(e.implicitKey)return`${n} `}return n}};function no(i,e,t){if(ro.isAlias(e)){let r=e.resolve(i),n=t&&r&&t.get(r);return n?n.count*n.aliasCount:0}else if(ro.isCollection(e)){let r=0;for(let n of e.items){let s=no(i,n,t);s>r&&(r=s)}return r}else if(ro.isPair(e)){let r=no(i,e.key,t),n=no(i,e.value,t);return Math.max(r,n)}return 1}hg.Alias=yc});var je=_(bc=>{"use strict";var _E=Se(),wE=io(),xE=bi(),SE=i=>!i||typeof i!="function"&&typeof i!="object",_i=class extends wE.NodeBase{constructor(e){super(_E.SCALAR),this.value=e}toJSON(e,t){return t!=null&&t.keep?this.value:xE.toJS(this.value,e,t)}toString(){return String(this.value)}};_i.BLOCK_FOLDED="BLOCK_FOLDED";_i.BLOCK_LITERAL="BLOCK_LITERAL";_i.PLAIN="PLAIN";_i.QUOTE_DOUBLE="QUOTE_DOUBLE";_i.QUOTE_SINGLE="QUOTE_SINGLE";bc.Scalar=_i;bc.isScalarValue=SE});var yn=_(dg=>{"use strict";var EE=vn(),Zi=Se(),pg=je(),OE="tag:yaml.org,2002:";function kE(i,e,t){var r;if(e){let n=t.filter(o=>o.tag===e),s=(r=n.find(o=>!o.format))!=null?r:n[0];if(!s)throw new Error(`Tag ${e} not found`);return s}return t.find(n=>{var s;return((s=n.identify)==null?void 0:s.call(n,i))&&!n.format})}function CE(i,e,t){var f,d,m;if(Zi.isDocument(i)&&(i=i.contents),Zi.isNode(i))return i;if(Zi.isPair(i)){let g=(d=(f=t.schema[Zi.MAP]).createNode)==null?void 0:d.call(f,t.schema,null,t);return g.items.push(i),g}(i instanceof String||i instanceof Number||i instanceof Boolean||typeof BigInt!="undefined"&&i instanceof BigInt)&&(i=i.valueOf());let{aliasDuplicateObjects:r,onAnchor:n,onTagObj:s,schema:o,sourceObjects:a}=t,l;if(r&&i&&typeof i=="object"){if(l=a.get(i),l)return l.anchor||(l.anchor=n(i)),new EE.Alias(l.anchor);l={anchor:null,node:null},a.set(i,l)}e!=null&&e.startsWith("!!")&&(e=OE+e.slice(2));let c=kE(i,e,o.tags);if(!c){if(i&&typeof i.toJSON=="function"&&(i=i.toJSON()),!i||typeof i!="object"){let g=new pg.Scalar(i);return l&&(l.node=g),g}c=i instanceof Map?o[Zi.MAP]:Symbol.iterator in Object(i)?o[Zi.SEQ]:o[Zi.MAP]}s&&(s(c),delete t.onTagObj);let u=c!=null&&c.createNode?c.createNode(t.schema,i,t):typeof((m=c==null?void 0:c.nodeClass)==null?void 0:m.from)=="function"?c.nodeClass.from(t.schema,i,t):new pg.Scalar(i);return e?u.tag=e:c.default||(u.tag=c.tag),l&&(l.node=u),u}dg.createNode=CE});var oo=_(so=>{"use strict";var TE=yn(),Jt=Se(),AE=io();function _c(i,e,t){let r=t;for(let n=e.length-1;n>=0;--n){let s=e[n];if(typeof s=="number"&&Number.isInteger(s)&&s>=0){let o=[];o[s]=r,r=o}else r=new Map([[s,r]])}return TE.createNode(r,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:i,sourceObjects:new Map})}var mg=i=>i==null||typeof i=="object"&&!!i[Symbol.iterator]().next().done,wc=class extends AE.NodeBase{constructor(e,t){super(e),Object.defineProperty(this,"schema",{value:t,configurable:!0,enumerable:!1,writable:!0})}clone(e){let t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(t.schema=e),t.items=t.items.map(r=>Jt.isNode(r)||Jt.isPair(r)?r.clone(e):r),this.range&&(t.range=this.range.slice()),t}addIn(e,t){if(mg(e))this.add(t);else{let[r,...n]=e,s=this.get(r,!0);if(Jt.isCollection(s))s.addIn(n,t);else if(s===void 0&&this.schema)this.set(r,_c(this.schema,n,t));else throw new Error(`Expected YAML collection at ${r}. Remaining path: ${n}`)}}deleteIn(e){let[t,...r]=e;if(r.length===0)return this.delete(t);let n=this.get(t,!0);if(Jt.isCollection(n))return n.deleteIn(r);throw new Error(`Expected YAML collection at ${t}. Remaining path: ${r}`)}getIn(e,t){let[r,...n]=e,s=this.get(r,!0);return n.length===0?!t&&Jt.isScalar(s)?s.value:s:Jt.isCollection(s)?s.getIn(n,t):void 0}hasAllNullValues(e){return this.items.every(t=>{if(!Jt.isPair(t))return!1;let r=t.value;return r==null||e&&Jt.isScalar(r)&&r.value==null&&!r.commentBefore&&!r.comment&&!r.tag})}hasIn(e){let[t,...r]=e;if(r.length===0)return this.has(t);let n=this.get(t,!0);return Jt.isCollection(n)?n.hasIn(r):!1}setIn(e,t){let[r,...n]=e;if(n.length===0)this.set(r,t);else{let s=this.get(r,!0);if(Jt.isCollection(s))s.setIn(n,t);else if(s===void 0&&this.schema)this.set(r,_c(this.schema,n,t));else throw new Error(`Expected YAML collection at ${r}. Remaining path: ${n}`)}}};so.Collection=wc;so.collectionFromPath=_c;so.isEmptyPath=mg});var bn=_(ao=>{"use strict";var IE=i=>i.replace(/^(?!$)(?: $)?/gm,"#");function xc(i,e){return/^\n+$/.test(i)?i.substring(1):e?i.replace(/^(?! *$)/gm,e):i}var NE=(i,e,t)=>i.endsWith(`
`)?xc(t,e):t.includes(`
`)?`
`+xc(t,e):(i.endsWith(" ")?"":" ")+t;ao.indentComment=xc;ao.lineComment=NE;ao.stringifyComment=IE});var vg=_(_n=>{"use strict";var LE="flow",Sc="block",lo="quoted";function BE(i,e,t="flow",{indentAtStart:r,lineWidth:n=80,minContentWidth:s=20,onFold:o,onOverflow:a}={}){if(!n||n<0)return i;n<s&&(s=0);let l=Math.max(1+s,1+n-e.length);if(i.length<=l)return i;let c=[],u={},f=n-e.length;typeof r=="number"&&(r>n-Math.max(2,s)?c.push(0):f=n-r);let d,m,g=!1,y=-1,b=-1,x=-1;t===Sc&&(y=gg(i,y,e.length),y!==-1&&(f=y+l));for(let k;k=i[y+=1];){if(t===lo&&k==="\\"){switch(b=y,i[y+1]){case"x":y+=3;break;case"u":y+=5;break;case"U":y+=9;break;default:y+=1}x=y}if(k===`
`)t===Sc&&(y=gg(i,y,e.length)),f=y+e.length+l,d=void 0;else{if(k===" "&&m&&m!==" "&&m!==`
`&&m!=="	"){let O=i[y+1];O&&O!==" "&&O!==`
`&&O!=="	"&&(d=y)}if(y>=f)if(d)c.push(d),f=d+l,d=void 0;else if(t===lo){for(;m===" "||m==="	";)m=k,k=i[y+=1],g=!0;let O=y>x+1?y-2:b-1;if(u[O])return i;c.push(O),u[O]=!0,f=O+l,d=void 0}else g=!0}m=k}if(g&&a&&a(),c.length===0)return i;o&&o();let E=i.slice(0,c[0]);for(let k=0;k<c.length;++k){let O=c[k],S=c[k+1]||i.length;O===0?E=`
${e}${i.slice(0,S)}`:(t===lo&&u[O]&&(E+=`${i[O]}\\`),E+=`
${e}${i.slice(O+1,S)}`)}return E}function gg(i,e,t){let r=e,n=e+1,s=i[n];for(;s===" "||s==="	";)if(e<n+t)s=i[++e];else{do s=i[++e];while(s&&s!==`
`);r=e,n=e+1,s=i[n]}return r}_n.FOLD_BLOCK=Sc;_n.FOLD_FLOW=LE;_n.FOLD_QUOTED=lo;_n.foldFlowLines=BE});var xn=_(yg=>{"use strict";var Zt=je(),wi=vg(),uo=(i,e)=>({indentAtStart:e?i.indent.length:i.indentAtStart,lineWidth:i.options.lineWidth,minContentWidth:i.options.minContentWidth}),fo=i=>/^(%|---|\.\.\.)/m.test(i);function RE(i,e,t){if(!e||e<0)return!1;let r=e-t,n=i.length;if(n<=r)return!1;for(let s=0,o=0;s<n;++s)if(i[s]===`
`){if(s-o>r)return!0;if(o=s+1,n-o<=r)return!1}return!0}function wn(i,e){let t=JSON.stringify(i);if(e.options.doubleQuotedAsJSON)return t;let{implicitKey:r}=e,n=e.options.doubleQuotedMinMultiLineLength,s=e.indent||(fo(i)?"  ":""),o="",a=0;for(let l=0,c=t[l];c;c=t[++l])if(c===" "&&t[l+1]==="\\"&&t[l+2]==="n"&&(o+=t.slice(a,l)+"\\ ",l+=1,a=l,c="\\"),c==="\\")switch(t[l+1]){case"u":{o+=t.slice(a,l);let u=t.substr(l+2,4);switch(u){case"0000":o+="\\0";break;case"0007":o+="\\a";break;case"000b":o+="\\v";break;case"001b":o+="\\e";break;case"0085":o+="\\N";break;case"00a0":o+="\\_";break;case"2028":o+="\\L";break;case"2029":o+="\\P";break;default:u.substr(0,2)==="00"?o+="\\x"+u.substr(2):o+=t.substr(l,6)}l+=5,a=l+1}break;case"n":if(r||t[l+2]==='"'||t.length<n)l+=1;else{for(o+=t.slice(a,l)+`

`;t[l+2]==="\\"&&t[l+3]==="n"&&t[l+4]!=='"';)o+=`
`,l+=2;o+=s,t[l+2]===" "&&(o+="\\"),l+=1,a=l+1}break;default:l+=1}return o=a?o+t.slice(a):t,r?o:wi.foldFlowLines(o,s,wi.FOLD_QUOTED,uo(e,!1))}function Ec(i,e){if(e.options.singleQuote===!1||e.implicitKey&&i.includes(`
`)||/[ \t]\n|\n[ \t]/.test(i))return wn(i,e);let t=e.indent||(fo(i)?"  ":""),r="'"+i.replace(/'/g,"''").replace(/\n+/g,`$&
${t}`)+"'";return e.implicitKey?r:wi.foldFlowLines(r,t,wi.FOLD_FLOW,uo(e,!1))}function Dr(i,e){let{singleQuote:t}=e.options,r;if(t===!1)r=wn;else{let n=i.includes('"'),s=i.includes("'");n&&!s?r=Ec:s&&!n?r=wn:r=t?Ec:wn}return r(i,e)}var Oc;try{Oc=new RegExp(`(^|(?<!
))
+(?!
|$)`,"g")}catch{Oc=/\n+(?!\n|$)/g}function co({comment:i,type:e,value:t},r,n,s){let{blockQuote:o,commentString:a,lineWidth:l}=r.options;if(!o||/\n[\t ]+$/.test(t)||/^\s*$/.test(t))return Dr(t,r);let c=r.indent||(r.forceBlockIndent||fo(t)?"  ":""),u=o==="literal"?!0:o==="folded"||e===Zt.Scalar.BLOCK_FOLDED?!1:e===Zt.Scalar.BLOCK_LITERAL?!0:!RE(t,l,c.length);if(!t)return u?`|
`:`>
`;let f,d;for(d=t.length;d>0;--d){let R=t[d-1];if(R!==`
`&&R!=="	"&&R!==" ")break}let m=t.substring(d),g=m.indexOf(`
`);g===-1?f="-":t===m||g!==m.length-1?(f="+",s&&s()):f="",m&&(t=t.slice(0,-m.length),m[m.length-1]===`
`&&(m=m.slice(0,-1)),m=m.replace(Oc,`$&${c}`));let y=!1,b,x=-1;for(b=0;b<t.length;++b){let R=t[b];if(R===" ")y=!0;else if(R===`
`)x=b;else break}let E=t.substring(0,x<b?x+1:b);E&&(t=t.substring(E.length),E=E.replace(/\n+/g,`$&${c}`));let O=(u?"|":">")+(y?c?"2":"1":"")+f;if(i&&(O+=" "+a(i.replace(/ ?[\r\n]+/g," ")),n&&n()),u)return t=t.replace(/\n+/g,`$&${c}`),`${O}
${c}${E}${t}${m}`;t=t.replace(/\n+/g,`
$&`).replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${c}`);let S=wi.foldFlowLines(`${E}${t}${m}`,c,wi.FOLD_BLOCK,uo(r,!0));return`${O}
${c}${S}`}function PE(i,e,t,r){let{type:n,value:s}=i,{actualString:o,implicitKey:a,indent:l,indentStep:c,inFlow:u}=e;if(a&&s.includes(`
`)||u&&/[[\]{},]/.test(s))return Dr(s,e);if(!s||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(s))return a||u||!s.includes(`
`)?Dr(s,e):co(i,e,t,r);if(!a&&!u&&n!==Zt.Scalar.PLAIN&&s.includes(`
`))return co(i,e,t,r);if(fo(s)){if(l==="")return e.forceBlockIndent=!0,co(i,e,t,r);if(a&&l===c)return Dr(s,e)}let f=s.replace(/\n+/g,`$&
${l}`);if(o){let d=y=>{var b;return y.default&&y.tag!=="tag:yaml.org,2002:str"&&((b=y.test)==null?void 0:b.test(f))},{compat:m,tags:g}=e.doc.schema;if(g.some(d)||m!=null&&m.some(d))return Dr(s,e)}return a?f:wi.foldFlowLines(f,l,wi.FOLD_FLOW,uo(e,!1))}function ME(i,e,t,r){let{implicitKey:n,inFlow:s}=e,o=typeof i.value=="string"?i:Object.assign({},i,{value:String(i.value)}),{type:a}=i;a!==Zt.Scalar.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(o.value)&&(a=Zt.Scalar.QUOTE_DOUBLE);let l=u=>{switch(u){case Zt.Scalar.BLOCK_FOLDED:case Zt.Scalar.BLOCK_LITERAL:return n||s?Dr(o.value,e):co(o,e,t,r);case Zt.Scalar.QUOTE_DOUBLE:return wn(o.value,e);case Zt.Scalar.QUOTE_SINGLE:return Ec(o.value,e);case Zt.Scalar.PLAIN:return PE(o,e,t,r);default:return null}},c=l(a);if(c===null){let{defaultKeyType:u,defaultStringType:f}=e.options,d=n&&u||f;if(c=l(d),c===null)throw new Error(`Unsupported default string type ${d}`)}return c}yg.stringifyString=ME});var Sn=_(kc=>{"use strict";var DE=to(),xi=Se(),FE=bn(),qE=xn();function jE(i,e){let t=Object.assign({blockQuote:!0,commentString:FE.stringifyComment,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},i.schema.toStringOptions,e),r;switch(t.collectionStyle){case"block":r=!1;break;case"flow":r=!0;break;default:r=null}return{anchors:new Set,doc:i,flowCollectionPadding:t.flowCollectionPadding?" ":"",indent:"",indentStep:typeof t.indent=="number"?" ".repeat(t.indent):"  ",inFlow:r,options:t}}function UE(i,e){var n,s,o,a;if(e.tag){let l=i.filter(c=>c.tag===e.tag);if(l.length>0)return(n=l.find(c=>c.format===e.format))!=null?n:l[0]}let t,r;if(xi.isScalar(e)){r=e.value;let l=i.filter(c=>{var u;return(u=c.identify)==null?void 0:u.call(c,r)});if(l.length>1){let c=l.filter(u=>u.test);c.length>0&&(l=c)}t=(s=l.find(c=>c.format===e.format))!=null?s:l.find(c=>!c.format)}else r=e,t=i.find(l=>l.nodeClass&&r instanceof l.nodeClass);if(!t){let l=(a=(o=r==null?void 0:r.constructor)==null?void 0:o.name)!=null?a:typeof r;throw new Error(`Tag not resolved for ${l} value`)}return t}function $E(i,e,{anchors:t,doc:r}){if(!r.directives)return"";let n=[],s=(xi.isScalar(i)||xi.isCollection(i))&&i.anchor;s&&DE.anchorIsValid(s)&&(t.add(s),n.push(`&${s}`));let o=i.tag?i.tag:e.default?null:e.tag;return o&&n.push(r.directives.tagString(o)),n.join(" ")}function VE(i,e,t,r){var l,c;if(xi.isPair(i))return i.toString(e,t,r);if(xi.isAlias(i)){if(e.doc.directives)return i.toString(e);if((l=e.resolvedAliases)!=null&&l.has(i))throw new TypeError("Cannot stringify circular structure without alias nodes");e.resolvedAliases?e.resolvedAliases.add(i):e.resolvedAliases=new Set([i]),i=i.resolve(e.doc)}let n,s=xi.isNode(i)?i:e.doc.createNode(i,{onTagObj:u=>n=u});n||(n=UE(e.doc.schema.tags,s));let o=$E(s,n,e);o.length>0&&(e.indentAtStart=((c=e.indentAtStart)!=null?c:0)+o.length+1);let a=typeof n.stringify=="function"?n.stringify(s,e,t,r):xi.isScalar(s)?qE.stringifyString(s,e,t,r):s.toString(e,t,r);return o?xi.isScalar(s)||a[0]==="{"||a[0]==="["?`${o} ${a}`:`${o}
${e.indent}${a}`:a}kc.createStringifyContext=jE;kc.stringify=VE});var xg=_(wg=>{"use strict";var li=Se(),bg=je(),_g=Sn(),En=bn();function HE({key:i,value:e},t,r,n){var T,A;let{allNullValues:s,doc:o,indent:a,indentStep:l,options:{commentString:c,indentSeq:u,simpleKeys:f}}=t,d=li.isNode(i)&&i.comment||null;if(f){if(d)throw new Error("With simple keys, key nodes cannot have comments");if(li.isCollection(i)||!li.isNode(i)&&typeof i=="object"){let C="With simple keys, collection cannot be used as a key value";throw new Error(C)}}let m=!f&&(!i||d&&e==null&&!t.inFlow||li.isCollection(i)||(li.isScalar(i)?i.type===bg.Scalar.BLOCK_FOLDED||i.type===bg.Scalar.BLOCK_LITERAL:typeof i=="object"));t=Object.assign({},t,{allNullValues:!1,implicitKey:!m&&(f||!s),indent:a+l});let g=!1,y=!1,b=_g.stringify(i,t,()=>g=!0,()=>y=!0);if(!m&&!t.inFlow&&b.length>1024){if(f)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");m=!0}if(t.inFlow){if(s||e==null)return g&&r&&r(),b===""?"?":m?`? ${b}`:b}else if(s&&!f||e==null&&m)return b=`? ${b}`,d&&!g?b+=En.lineComment(b,t.indent,c(d)):y&&n&&n(),b;g&&(d=null),m?(d&&(b+=En.lineComment(b,t.indent,c(d))),b=`? ${b}
${a}:`):(b=`${b}:`,d&&(b+=En.lineComment(b,t.indent,c(d))));let x,E,k;li.isNode(e)?(x=!!e.spaceBefore,E=e.commentBefore,k=e.comment):(x=!1,E=null,k=null,e&&typeof e=="object"&&(e=o.createNode(e))),t.implicitKey=!1,!m&&!d&&li.isScalar(e)&&(t.indentAtStart=b.length+1),y=!1,!u&&l.length>=2&&!t.inFlow&&!m&&li.isSeq(e)&&!e.flow&&!e.tag&&!e.anchor&&(t.indent=t.indent.substring(2));let O=!1,S=_g.stringify(e,t,()=>O=!0,()=>y=!0),R=" ";if(d||x||E){if(R=x?`
`:"",E){let C=c(E);R+=`
${En.indentComment(C,t.indent)}`}S===""&&!t.inFlow?R===`
`&&(R=`

`):R+=`
${t.indent}`}else if(!m&&li.isCollection(e)){let C=S[0],L=S.indexOf(`
`),P=L!==-1,U=(A=(T=t.inFlow)!=null?T:e.flow)!=null?A:e.items.length===0;if(P||!U){let F=!1;if(P&&(C==="&"||C==="!")){let H=S.indexOf(" ");C==="&"&&H!==-1&&H<L&&S[H+1]==="!"&&(H=S.indexOf(" ",H+1)),(H===-1||L<H)&&(F=!0)}F||(R=`
${t.indent}`)}}else(S===""||S[0]===`
`)&&(R="");return b+=R+S,t.inFlow?O&&r&&r():k&&!O?b+=En.lineComment(b,t.indent,c(k)):y&&n&&n(),b}wg.stringifyPair=HE});var Tc=_(Cc=>{"use strict";function GE(i,...e){i==="debug"&&console.log(...e)}function WE(i,e){(i==="debug"||i==="warn")&&(typeof process!="undefined"&&process.emitWarning?process.emitWarning(e):console.warn(e))}Cc.debug=GE;Cc.warn=WE});var go=_(mo=>{"use strict";var On=Se(),Sg=je(),ho="<<",po={identify:i=>i===ho||typeof i=="symbol"&&i.description===ho,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new Sg.Scalar(Symbol(ho)),{addToJSMap:Eg}),stringify:()=>ho},YE=(i,e)=>(po.identify(e)||On.isScalar(e)&&(!e.type||e.type===Sg.Scalar.PLAIN)&&po.identify(e.value))&&(i==null?void 0:i.doc.schema.tags.some(t=>t.tag===po.tag&&t.default));function Eg(i,e,t){if(t=i&&On.isAlias(t)?t.resolve(i.doc):t,On.isSeq(t))for(let r of t.items)Ac(i,e,r);else if(Array.isArray(t))for(let r of t)Ac(i,e,r);else Ac(i,e,t)}function Ac(i,e,t){let r=i&&On.isAlias(t)?t.resolve(i.doc):t;if(!On.isMap(r))throw new Error("Merge sources must be maps or map aliases");let n=r.toJSON(null,i,Map);for(let[s,o]of n)e instanceof Map?e.has(s)||e.set(s,o):e instanceof Set?e.add(s):Object.prototype.hasOwnProperty.call(e,s)||Object.defineProperty(e,s,{value:o,writable:!0,enumerable:!0,configurable:!0});return e}mo.addMergeToJSMap=Eg;mo.isMergeKey=YE;mo.merge=po});var Nc=_(Cg=>{"use strict";var KE=Tc(),Og=go(),zE=Sn(),kg=Se(),Ic=bi();function JE(i,e,{key:t,value:r}){if(kg.isNode(t)&&t.addToJSMap)t.addToJSMap(i,e,r);else if(Og.isMergeKey(i,t))Og.addMergeToJSMap(i,e,r);else{let n=Ic.toJS(t,"",i);if(e instanceof Map)e.set(n,Ic.toJS(r,n,i));else if(e instanceof Set)e.add(n);else{let s=ZE(t,n,i),o=Ic.toJS(r,s,i);s in e?Object.defineProperty(e,s,{value:o,writable:!0,enumerable:!0,configurable:!0}):e[s]=o}}return e}function ZE(i,e,t){if(e===null)return"";if(typeof e!="object")return String(e);if(kg.isNode(i)&&(t!=null&&t.doc)){let r=zE.createStringifyContext(t.doc,{});r.anchors=new Set;for(let s of t.anchors.keys())r.anchors.add(s.anchor);r.inFlow=!0,r.inStringifyKey=!0;let n=i.toString(r);if(!t.mapKeyWarned){let s=JSON.stringify(n);s.length>40&&(s=s.substring(0,36)+'..."'),KE.warn(t.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${s}. Set mapAsMap: true to use object keys.`),t.mapKeyWarned=!0}return n}return JSON.stringify(e)}Cg.addPairToJSMap=JE});var Si=_(Lc=>{"use strict";var Tg=yn(),QE=xg(),XE=Nc(),vo=Se();function eO(i,e,t){let r=Tg.createNode(i,void 0,t),n=Tg.createNode(e,void 0,t);return new yo(r,n)}var yo=class i{constructor(e,t=null){Object.defineProperty(this,vo.NODE_TYPE,{value:vo.PAIR}),this.key=e,this.value=t}clone(e){let{key:t,value:r}=this;return vo.isNode(t)&&(t=t.clone(e)),vo.isNode(r)&&(r=r.clone(e)),new i(t,r)}toJSON(e,t){let r=t!=null&&t.mapAsMap?new Map:{};return XE.addPairToJSMap(t,r,this)}toString(e,t,r){return e!=null&&e.doc?QE.stringifyPair(this,e,t,r):JSON.stringify(this)}};Lc.Pair=yo;Lc.createPair=eO});var Bc=_(Ig=>{"use strict";var Qi=Se(),Ag=Sn(),bo=bn();function tO(i,e,t){var s;return(((s=e.inFlow)!=null?s:i.flow)?rO:iO)(i,e,t)}function iO({comment:i,items:e},t,{blockItemPrefix:r,flowChars:n,itemIndent:s,onChompKeep:o,onComment:a}){let{indent:l,options:{commentString:c}}=t,u=Object.assign({},t,{indent:s,type:null}),f=!1,d=[];for(let g=0;g<e.length;++g){let y=e[g],b=null;if(Qi.isNode(y))!f&&y.spaceBefore&&d.push(""),_o(t,d,y.commentBefore,f),y.comment&&(b=y.comment);else if(Qi.isPair(y)){let E=Qi.isNode(y.key)?y.key:null;E&&(!f&&E.spaceBefore&&d.push(""),_o(t,d,E.commentBefore,f))}f=!1;let x=Ag.stringify(y,u,()=>b=null,()=>f=!0);b&&(x+=bo.lineComment(x,s,c(b))),f&&b&&(f=!1),d.push(r+x)}let m;if(d.length===0)m=n.start+n.end;else{m=d[0];for(let g=1;g<d.length;++g){let y=d[g];m+=y?`
${l}${y}`:`
`}}return i?(m+=`
`+bo.indentComment(c(i),l),a&&a()):f&&o&&o(),m}function rO({items:i},e,{flowChars:t,itemIndent:r}){let{indent:n,indentStep:s,flowCollectionPadding:o,options:{commentString:a}}=e;r+=s;let l=Object.assign({},e,{indent:r,inFlow:!0,type:null}),c=!1,u=0,f=[];for(let g=0;g<i.length;++g){let y=i[g],b=null;if(Qi.isNode(y))y.spaceBefore&&f.push(""),_o(e,f,y.commentBefore,!1),y.comment&&(b=y.comment);else if(Qi.isPair(y)){let E=Qi.isNode(y.key)?y.key:null;E&&(E.spaceBefore&&f.push(""),_o(e,f,E.commentBefore,!1),E.comment&&(c=!0));let k=Qi.isNode(y.value)?y.value:null;k?(k.comment&&(b=k.comment),k.commentBefore&&(c=!0)):y.value==null&&(E!=null&&E.comment)&&(b=E.comment)}b&&(c=!0);let x=Ag.stringify(y,l,()=>b=null);g<i.length-1&&(x+=","),b&&(x+=bo.lineComment(x,r,a(b))),!c&&(f.length>u||x.includes(`
`))&&(c=!0),f.push(x),u=f.length}let{start:d,end:m}=t;if(f.length===0)return d+m;if(!c){let g=f.reduce((y,b)=>y+b.length+2,2);c=e.options.lineWidth>0&&g>e.options.lineWidth}if(c){let g=d;for(let y of f)g+=y?`
${s}${n}${y}`:`
`;return`${g}
${n}${m}`}else return`${d}${o}${f.join(" ")}${o}${m}`}function _o({indent:i,options:{commentString:e}},t,r,n){if(r&&n&&(r=r.replace(/^\n+/,"")),r){let s=bo.indentComment(e(r),i);t.push(s.trimStart())}}Ig.stringifyCollection=tO});var Oi=_(Pc=>{"use strict";var nO=Bc(),sO=Nc(),oO=oo(),Ei=Se(),wo=Si(),aO=je();function kn(i,e){let t=Ei.isScalar(e)?e.value:e;for(let r of i)if(Ei.isPair(r)&&(r.key===e||r.key===t||Ei.isScalar(r.key)&&r.key.value===t))return r}var Rc=class extends oO.Collection{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(Ei.MAP,e),this.items=[]}static from(e,t,r){let{keepUndefined:n,replacer:s}=r,o=new this(e),a=(l,c)=>{if(typeof s=="function")c=s.call(t,l,c);else if(Array.isArray(s)&&!s.includes(l))return;(c!==void 0||n)&&o.items.push(wo.createPair(l,c,r))};if(t instanceof Map)for(let[l,c]of t)a(l,c);else if(t&&typeof t=="object")for(let l of Object.keys(t))a(l,t[l]);return typeof e.sortMapEntries=="function"&&o.items.sort(e.sortMapEntries),o}add(e,t){var o;let r;Ei.isPair(e)?r=e:!e||typeof e!="object"||!("key"in e)?r=new wo.Pair(e,e==null?void 0:e.value):r=new wo.Pair(e.key,e.value);let n=kn(this.items,r.key),s=(o=this.schema)==null?void 0:o.sortMapEntries;if(n){if(!t)throw new Error(`Key ${r.key} already set`);Ei.isScalar(n.value)&&aO.isScalarValue(r.value)?n.value.value=r.value:n.value=r.value}else if(s){let a=this.items.findIndex(l=>s(r,l)<0);a===-1?this.items.push(r):this.items.splice(a,0,r)}else this.items.push(r)}delete(e){let t=kn(this.items,e);return t?this.items.splice(this.items.indexOf(t),1).length>0:!1}get(e,t){var s;let r=kn(this.items,e),n=r==null?void 0:r.value;return(s=!t&&Ei.isScalar(n)?n.value:n)!=null?s:void 0}has(e){return!!kn(this.items,e)}set(e,t){this.add(new wo.Pair(e,t),!0)}toJSON(e,t,r){let n=r?new r:t!=null&&t.mapAsMap?new Map:{};t!=null&&t.onCreate&&t.onCreate(n);for(let s of this.items)sO.addPairToJSMap(t,n,s);return n}toString(e,t,r){if(!e)return JSON.stringify(this);for(let n of this.items)if(!Ei.isPair(n))throw new Error(`Map items must all be pairs; found ${JSON.stringify(n)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),nO.stringifyCollection(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:r,onComment:t})}};Pc.YAMLMap=Rc;Pc.findPair=kn});var Fr=_(Lg=>{"use strict";var lO=Se(),Ng=Oi(),cO={collection:"map",default:!0,nodeClass:Ng.YAMLMap,tag:"tag:yaml.org,2002:map",resolve(i,e){return lO.isMap(i)||e("Expected a mapping for this tag"),i},createNode:(i,e,t)=>Ng.YAMLMap.from(i,e,t)};Lg.map=cO});var ki=_(Bg=>{"use strict";var uO=yn(),fO=Bc(),hO=oo(),So=Se(),pO=je(),dO=bi(),Mc=class extends hO.Collection{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(So.SEQ,e),this.items=[]}add(e){this.items.push(e)}delete(e){let t=xo(e);return typeof t!="number"?!1:this.items.splice(t,1).length>0}get(e,t){let r=xo(e);if(typeof r!="number")return;let n=this.items[r];return!t&&So.isScalar(n)?n.value:n}has(e){let t=xo(e);return typeof t=="number"&&t<this.items.length}set(e,t){let r=xo(e);if(typeof r!="number")throw new Error(`Expected a valid index, not ${e}.`);let n=this.items[r];So.isScalar(n)&&pO.isScalarValue(t)?n.value=t:this.items[r]=t}toJSON(e,t){let r=[];t!=null&&t.onCreate&&t.onCreate(r);let n=0;for(let s of this.items)r.push(dO.toJS(s,String(n++),t));return r}toString(e,t,r){return e?fO.stringifyCollection(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:r,onComment:t}):JSON.stringify(this)}static from(e,t,r){let{replacer:n}=r,s=new this(e);if(t&&Symbol.iterator in Object(t)){let o=0;for(let a of t){if(typeof n=="function"){let l=t instanceof Set?a:String(o++);a=n.call(t,l,a)}s.items.push(uO.createNode(a,void 0,r))}}return s}};function xo(i){let e=So.isScalar(i)?i.value:i;return e&&typeof e=="string"&&(e=Number(e)),typeof e=="number"&&Number.isInteger(e)&&e>=0?e:null}Bg.YAMLSeq=Mc});var qr=_(Pg=>{"use strict";var mO=Se(),Rg=ki(),gO={collection:"seq",default:!0,nodeClass:Rg.YAMLSeq,tag:"tag:yaml.org,2002:seq",resolve(i,e){return mO.isSeq(i)||e("Expected a sequence for this tag"),i},createNode:(i,e,t)=>Rg.YAMLSeq.from(i,e,t)};Pg.seq=gO});var Cn=_(Mg=>{"use strict";var vO=xn(),yO={identify:i=>typeof i=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:i=>i,stringify(i,e,t,r){return e=Object.assign({actualString:!0},e),vO.stringifyString(i,e,t,r)}};Mg.string=yO});var Eo=_(qg=>{"use strict";var Dg=je(),Fg={identify:i=>i==null,createNode:()=>new Dg.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new Dg.Scalar(null),stringify:({source:i},e)=>typeof i=="string"&&Fg.test.test(i)?i:e.options.nullStr};qg.nullTag=Fg});var Dc=_(Ug=>{"use strict";var bO=je(),jg={identify:i=>typeof i=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:i=>new bO.Scalar(i[0]==="t"||i[0]==="T"),stringify({source:i,value:e},t){if(i&&jg.test.test(i)){let r=i[0]==="t"||i[0]==="T";if(e===r)return i}return e?t.options.trueStr:t.options.falseStr}};Ug.boolTag=jg});var jr=_($g=>{"use strict";function _O({format:i,minFractionDigits:e,tag:t,value:r}){if(typeof r=="bigint")return String(r);let n=typeof r=="number"?r:Number(r);if(!isFinite(n))return isNaN(n)?".nan":n<0?"-.inf":".inf";let s=JSON.stringify(r);if(!i&&e&&(!t||t==="tag:yaml.org,2002:float")&&/^\d/.test(s)){let o=s.indexOf(".");o<0&&(o=s.length,s+=".");let a=e-(s.length-o-1);for(;a-- >0;)s+="0"}return s}$g.stringifyNumber=_O});var qc=_(Oo=>{"use strict";var wO=je(),Fc=jr(),xO={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:i=>i.slice(-3).toLowerCase()==="nan"?NaN:i[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Fc.stringifyNumber},SO={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:i=>parseFloat(i),stringify(i){let e=Number(i.value);return isFinite(e)?e.toExponential():Fc.stringifyNumber(i)}},EO={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(i){let e=new wO.Scalar(parseFloat(i)),t=i.indexOf(".");return t!==-1&&i[i.length-1]==="0"&&(e.minFractionDigits=i.length-t-1),e},stringify:Fc.stringifyNumber};Oo.float=EO;Oo.floatExp=SO;Oo.floatNaN=xO});var Uc=_(Co=>{"use strict";var Vg=jr(),ko=i=>typeof i=="bigint"||Number.isInteger(i),jc=(i,e,t,{intAsBigInt:r})=>r?BigInt(i):parseInt(i.substring(e),t);function Hg(i,e,t){let{value:r}=i;return ko(r)&&r>=0?t+r.toString(e):Vg.stringifyNumber(i)}var OO={identify:i=>ko(i)&&i>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(i,e,t)=>jc(i,2,8,t),stringify:i=>Hg(i,8,"0o")},kO={identify:ko,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(i,e,t)=>jc(i,0,10,t),stringify:Vg.stringifyNumber},CO={identify:i=>ko(i)&&i>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(i,e,t)=>jc(i,2,16,t),stringify:i=>Hg(i,16,"0x")};Co.int=kO;Co.intHex=CO;Co.intOct=OO});var Wg=_(Gg=>{"use strict";var TO=Fr(),AO=Eo(),IO=qr(),NO=Cn(),LO=Dc(),$c=qc(),Vc=Uc(),BO=[TO.map,IO.seq,NO.string,AO.nullTag,LO.boolTag,Vc.intOct,Vc.int,Vc.intHex,$c.floatNaN,$c.floatExp,$c.float];Gg.schema=BO});var zg=_(Kg=>{"use strict";var RO=je(),PO=Fr(),MO=qr();function Yg(i){return typeof i=="bigint"||Number.isInteger(i)}var To=({value:i})=>JSON.stringify(i),DO=[{identify:i=>typeof i=="string",default:!0,tag:"tag:yaml.org,2002:str",resolve:i=>i,stringify:To},{identify:i=>i==null,createNode:()=>new RO.Scalar(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:To},{identify:i=>typeof i=="boolean",default:!0,tag:"tag:yaml.org,2002:bool",test:/^true|false$/,resolve:i=>i==="true",stringify:To},{identify:Yg,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(i,e,{intAsBigInt:t})=>t?BigInt(i):parseInt(i,10),stringify:({value:i})=>Yg(i)?i.toString():JSON.stringify(i)},{identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:i=>parseFloat(i),stringify:To}],FO={default:!0,tag:"",test:/^/,resolve(i,e){return e(`Unresolved plain scalar ${JSON.stringify(i)}`),i}},qO=[PO.map,MO.seq].concat(DO,FO);Kg.schema=qO});var Gc=_(Jg=>{"use strict";var Hc=je(),jO=xn(),UO={identify:i=>i instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(i,e){if(typeof Buffer=="function")return Buffer.from(i,"base64");if(typeof atob=="function"){let t=atob(i.replace(/[\n\r]/g,"")),r=new Uint8Array(t.length);for(let n=0;n<t.length;++n)r[n]=t.charCodeAt(n);return r}else return e("This environment does not support reading binary tags; either Buffer or atob is required"),i},stringify({comment:i,type:e,value:t},r,n,s){let o=t,a;if(typeof Buffer=="function")a=o instanceof Buffer?o.toString("base64"):Buffer.from(o.buffer).toString("base64");else if(typeof btoa=="function"){let l="";for(let c=0;c<o.length;++c)l+=String.fromCharCode(o[c]);a=btoa(l)}else throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(e||(e=Hc.Scalar.BLOCK_LITERAL),e!==Hc.Scalar.QUOTE_DOUBLE){let l=Math.max(r.options.lineWidth-r.indent.length,r.options.minContentWidth),c=Math.ceil(a.length/l),u=new Array(c);for(let f=0,d=0;f<c;++f,d+=l)u[f]=a.substr(d,l);a=u.join(e===Hc.Scalar.BLOCK_LITERAL?`
`:" ")}return jO.stringifyString({comment:i,type:e,value:a},r,n,s)}};Jg.binary=UO});var No=_(Io=>{"use strict";var Ao=Se(),Wc=Si(),$O=je(),VO=ki();function Zg(i,e){var t;if(Ao.isSeq(i))for(let r=0;r<i.items.length;++r){let n=i.items[r];if(!Ao.isPair(n)){if(Ao.isMap(n)){n.items.length>1&&e("Each pair must have its own sequence indicator");let s=n.items[0]||new Wc.Pair(new $O.Scalar(null));if(n.commentBefore&&(s.key.commentBefore=s.key.commentBefore?`${n.commentBefore}
${s.key.commentBefore}`:n.commentBefore),n.comment){let o=(t=s.value)!=null?t:s.key;o.comment=o.comment?`${n.comment}
${o.comment}`:n.comment}n=s}i.items[r]=Ao.isPair(n)?n:new Wc.Pair(n)}}else e("Expected a sequence for this tag");return i}function Qg(i,e,t){let{replacer:r}=t,n=new VO.YAMLSeq(i);n.tag="tag:yaml.org,2002:pairs";let s=0;if(e&&Symbol.iterator in Object(e))for(let o of e){typeof r=="function"&&(o=r.call(e,String(s++),o));let a,l;if(Array.isArray(o))if(o.length===2)a=o[0],l=o[1];else throw new TypeError(`Expected [key, value] tuple: ${o}`);else if(o&&o instanceof Object){let c=Object.keys(o);if(c.length===1)a=c[0],l=o[a];else throw new TypeError(`Expected tuple with one key, not ${c.length} keys`)}else a=o;n.items.push(Wc.createPair(a,l,t))}return n}var HO={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:Zg,createNode:Qg};Io.createPairs=Qg;Io.pairs=HO;Io.resolvePairs=Zg});var zc=_(Kc=>{"use strict";var Xg=Se(),Yc=bi(),Tn=Oi(),GO=ki(),e0=No(),Xi=class i extends GO.YAMLSeq{constructor(){super(),this.add=Tn.YAMLMap.prototype.add.bind(this),this.delete=Tn.YAMLMap.prototype.delete.bind(this),this.get=Tn.YAMLMap.prototype.get.bind(this),this.has=Tn.YAMLMap.prototype.has.bind(this),this.set=Tn.YAMLMap.prototype.set.bind(this),this.tag=i.tag}toJSON(e,t){if(!t)return super.toJSON(e);let r=new Map;t!=null&&t.onCreate&&t.onCreate(r);for(let n of this.items){let s,o;if(Xg.isPair(n)?(s=Yc.toJS(n.key,"",t),o=Yc.toJS(n.value,s,t)):s=Yc.toJS(n,"",t),r.has(s))throw new Error("Ordered maps must not include duplicate keys");r.set(s,o)}return r}static from(e,t,r){let n=e0.createPairs(e,t,r),s=new this;return s.items=n.items,s}};Xi.tag="tag:yaml.org,2002:omap";var WO={collection:"seq",identify:i=>i instanceof Map,nodeClass:Xi,default:!1,tag:"tag:yaml.org,2002:omap",resolve(i,e){let t=e0.resolvePairs(i,e),r=[];for(let{key:n}of t.items)Xg.isScalar(n)&&(r.includes(n.value)?e(`Ordered maps must not include duplicate keys: ${n.value}`):r.push(n.value));return Object.assign(new Xi,t)},createNode:(i,e,t)=>Xi.from(i,e,t)};Kc.YAMLOMap=Xi;Kc.omap=WO});var s0=_(Jc=>{"use strict";var t0=je();function i0({value:i,source:e},t){return e&&(i?r0:n0).test.test(e)?e:i?t.options.trueStr:t.options.falseStr}var r0={identify:i=>i===!0,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new t0.Scalar(!0),stringify:i0},n0={identify:i=>i===!1,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new t0.Scalar(!1),stringify:i0};Jc.falseTag=n0;Jc.trueTag=r0});var o0=_(Lo=>{"use strict";var YO=je(),Zc=jr(),KO={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:i=>i.slice(-3).toLowerCase()==="nan"?NaN:i[0]==="-"?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:Zc.stringifyNumber},zO={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:i=>parseFloat(i.replace(/_/g,"")),stringify(i){let e=Number(i.value);return isFinite(e)?e.toExponential():Zc.stringifyNumber(i)}},JO={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(i){let e=new YO.Scalar(parseFloat(i.replace(/_/g,""))),t=i.indexOf(".");if(t!==-1){let r=i.substring(t+1).replace(/_/g,"");r[r.length-1]==="0"&&(e.minFractionDigits=r.length)}return e},stringify:Zc.stringifyNumber};Lo.float=JO;Lo.floatExp=zO;Lo.floatNaN=KO});var l0=_(In=>{"use strict";var a0=jr(),An=i=>typeof i=="bigint"||Number.isInteger(i);function Bo(i,e,t,{intAsBigInt:r}){let n=i[0];if((n==="-"||n==="+")&&(e+=1),i=i.substring(e).replace(/_/g,""),r){switch(t){case 2:i=`0b${i}`;break;case 8:i=`0o${i}`;break;case 16:i=`0x${i}`;break}let o=BigInt(i);return n==="-"?BigInt(-1)*o:o}let s=parseInt(i,t);return n==="-"?-1*s:s}function Qc(i,e,t){let{value:r}=i;if(An(r)){let n=r.toString(e);return r<0?"-"+t+n.substr(1):t+n}return a0.stringifyNumber(i)}var ZO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(i,e,t)=>Bo(i,2,2,t),stringify:i=>Qc(i,2,"0b")},QO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(i,e,t)=>Bo(i,1,8,t),stringify:i=>Qc(i,8,"0")},XO={identify:An,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(i,e,t)=>Bo(i,0,10,t),stringify:a0.stringifyNumber},ek={identify:An,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(i,e,t)=>Bo(i,2,16,t),stringify:i=>Qc(i,16,"0x")};In.int=XO;In.intBin=ZO;In.intHex=ek;In.intOct=QO});var eu=_(Xc=>{"use strict";var Mo=Se(),Ro=Si(),Po=Oi(),er=class i extends Po.YAMLMap{constructor(e){super(e),this.tag=i.tag}add(e){let t;Mo.isPair(e)?t=e:e&&typeof e=="object"&&"key"in e&&"value"in e&&e.value===null?t=new Ro.Pair(e.key,null):t=new Ro.Pair(e,null),Po.findPair(this.items,t.key)||this.items.push(t)}get(e,t){let r=Po.findPair(this.items,e);return!t&&Mo.isPair(r)?Mo.isScalar(r.key)?r.key.value:r.key:r}set(e,t){if(typeof t!="boolean")throw new Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof t}`);let r=Po.findPair(this.items,e);r&&!t?this.items.splice(this.items.indexOf(r),1):!r&&t&&this.items.push(new Ro.Pair(e))}toJSON(e,t){return super.toJSON(e,t,Set)}toString(e,t,r){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),t,r);throw new Error("Set items must all have null values")}static from(e,t,r){let{replacer:n}=r,s=new this(e);if(t&&Symbol.iterator in Object(t))for(let o of t)typeof n=="function"&&(o=n.call(t,o,o)),s.items.push(Ro.createPair(o,null,r));return s}};er.tag="tag:yaml.org,2002:set";var tk={collection:"map",identify:i=>i instanceof Set,nodeClass:er,default:!1,tag:"tag:yaml.org,2002:set",createNode:(i,e,t)=>er.from(i,e,t),resolve(i,e){if(Mo.isMap(i)){if(i.hasAllNullValues(!0))return Object.assign(new er,i);e("Set items must all have null values")}else e("Expected a mapping for this tag");return i}};Xc.YAMLSet=er;Xc.set=tk});var iu=_(Do=>{"use strict";var ik=jr();function tu(i,e){let t=i[0],r=t==="-"||t==="+"?i.substring(1):i,n=o=>e?BigInt(o):Number(o),s=r.replace(/_/g,"").split(":").reduce((o,a)=>o*n(60)+n(a),n(0));return t==="-"?n(-1)*s:s}function c0(i){let{value:e}=i,t=o=>o;if(typeof e=="bigint")t=o=>BigInt(o);else if(isNaN(e)||!isFinite(e))return ik.stringifyNumber(i);let r="";e<0&&(r="-",e*=t(-1));let n=t(60),s=[e%n];return e<60?s.unshift(0):(e=(e-s[0])/n,s.unshift(e%n),e>=60&&(e=(e-s[0])/n,s.unshift(e))),r+s.map(o=>String(o).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}var rk={identify:i=>typeof i=="bigint"||Number.isInteger(i),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(i,e,{intAsBigInt:t})=>tu(i,t),stringify:c0},nk={identify:i=>typeof i=="number",default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:i=>tu(i,!1),stringify:c0},u0={identify:i=>i instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(i){let e=i.match(u0.test);if(!e)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");let[,t,r,n,s,o,a]=e.map(Number),l=e[7]?Number((e[7]+"00").substr(1,3)):0,c=Date.UTC(t,r-1,n,s||0,o||0,a||0,l),u=e[8];if(u&&u!=="Z"){let f=tu(u,!1);Math.abs(f)<30&&(f*=60),c-=6e4*f}return new Date(c)},stringify:({value:i})=>i.toISOString().replace(/((T00:00)?:00)?\.000Z$/,"")};Do.floatTime=nk;Do.intTime=rk;Do.timestamp=u0});var p0=_(h0=>{"use strict";var sk=Fr(),ok=Eo(),ak=qr(),lk=Cn(),ck=Gc(),f0=s0(),ru=o0(),Fo=l0(),uk=go(),fk=zc(),hk=No(),pk=eu(),nu=iu(),dk=[sk.map,ak.seq,lk.string,ok.nullTag,f0.trueTag,f0.falseTag,Fo.intBin,Fo.intOct,Fo.int,Fo.intHex,ru.floatNaN,ru.floatExp,ru.float,ck.binary,uk.merge,fk.omap,hk.pairs,pk.set,nu.intTime,nu.floatTime,nu.timestamp];h0.schema=dk});var S0=_(au=>{"use strict";var v0=Fr(),mk=Eo(),y0=qr(),gk=Cn(),vk=Dc(),su=qc(),ou=Uc(),yk=Wg(),bk=zg(),b0=Gc(),Nn=go(),_0=zc(),w0=No(),d0=p0(),x0=eu(),qo=iu(),m0=new Map([["core",yk.schema],["failsafe",[v0.map,y0.seq,gk.string]],["json",bk.schema],["yaml11",d0.schema],["yaml-1.1",d0.schema]]),g0={binary:b0.binary,bool:vk.boolTag,float:su.float,floatExp:su.floatExp,floatNaN:su.floatNaN,floatTime:qo.floatTime,int:ou.int,intHex:ou.intHex,intOct:ou.intOct,intTime:qo.intTime,map:v0.map,merge:Nn.merge,null:mk.nullTag,omap:_0.omap,pairs:w0.pairs,seq:y0.seq,set:x0.set,timestamp:qo.timestamp},_k={"tag:yaml.org,2002:binary":b0.binary,"tag:yaml.org,2002:merge":Nn.merge,"tag:yaml.org,2002:omap":_0.omap,"tag:yaml.org,2002:pairs":w0.pairs,"tag:yaml.org,2002:set":x0.set,"tag:yaml.org,2002:timestamp":qo.timestamp};function wk(i,e,t){let r=m0.get(e);if(r&&!i)return t&&!r.includes(Nn.merge)?r.concat(Nn.merge):r.slice();let n=r;if(!n)if(Array.isArray(i))n=[];else{let s=Array.from(m0.keys()).filter(o=>o!=="yaml11").map(o=>JSON.stringify(o)).join(", ");throw new Error(`Unknown schema "${e}"; use one of ${s} or define customTags array`)}if(Array.isArray(i))for(let s of i)n=n.concat(s);else typeof i=="function"&&(n=i(n.slice()));return t&&(n=n.concat(Nn.merge)),n.reduce((s,o)=>{let a=typeof o=="string"?g0[o]:o;if(!a){let l=JSON.stringify(o),c=Object.keys(g0).map(u=>JSON.stringify(u)).join(", ");throw new Error(`Unknown custom tag ${l}; use one of ${c}`)}return s.includes(a)||s.push(a),s},[])}au.coreKnownTags=_k;au.getTags=wk});var uu=_(E0=>{"use strict";var lu=Se(),xk=Fr(),Sk=qr(),Ek=Cn(),jo=S0(),Ok=(i,e)=>i.key<e.key?-1:i.key>e.key?1:0,cu=class i{constructor({compat:e,customTags:t,merge:r,resolveKnownTags:n,schema:s,sortMapEntries:o,toStringDefaults:a}){this.compat=Array.isArray(e)?jo.getTags(e,"compat"):e?jo.getTags(null,e):null,this.name=typeof s=="string"&&s||"core",this.knownTags=n?jo.coreKnownTags:{},this.tags=jo.getTags(t,this.name,r),this.toStringOptions=a!=null?a:null,Object.defineProperty(this,lu.MAP,{value:xk.map}),Object.defineProperty(this,lu.SCALAR,{value:Ek.string}),Object.defineProperty(this,lu.SEQ,{value:Sk.seq}),this.sortMapEntries=typeof o=="function"?o:o===!0?Ok:null}clone(){let e=Object.create(i.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}};E0.Schema=cu});var k0=_(O0=>{"use strict";var kk=Se(),fu=Sn(),Ln=bn();function Ck(i,e){var l;let t=[],r=e.directives===!0;if(e.directives!==!1&&i.directives){let c=i.directives.toString(i);c?(t.push(c),r=!0):i.directives.docStart&&(r=!0)}r&&t.push("---");let n=fu.createStringifyContext(i,e),{commentString:s}=n.options;if(i.commentBefore){t.length!==1&&t.unshift("");let c=s(i.commentBefore);t.unshift(Ln.indentComment(c,""))}let o=!1,a=null;if(i.contents){if(kk.isNode(i.contents)){if(i.contents.spaceBefore&&r&&t.push(""),i.contents.commentBefore){let f=s(i.contents.commentBefore);t.push(Ln.indentComment(f,""))}n.forceBlockIndent=!!i.comment,a=i.contents.comment}let c=a?void 0:()=>o=!0,u=fu.stringify(i.contents,n,()=>a=null,c);a&&(u+=Ln.lineComment(u,"",s(a))),(u[0]==="|"||u[0]===">")&&t[t.length-1]==="---"?t[t.length-1]=`--- ${u}`:t.push(u)}else t.push(fu.stringify(i.contents,n));if((l=i.directives)!=null&&l.docEnd)if(i.comment){let c=s(i.comment);c.includes(`
`)?(t.push("..."),t.push(Ln.indentComment(c,""))):t.push(`... ${c}`)}else t.push("...");else{let c=i.comment;c&&o&&(c=c.replace(/^\n+/,"")),c&&((!o||a)&&t[t.length-1]!==""&&t.push(""),t.push(Ln.indentComment(s(c),"")))}return t.join(`
`)+`
`}O0.stringifyDocument=Ck});var Bn=_(C0=>{"use strict";var Tk=vn(),Ur=oo(),Lt=Se(),Ak=Si(),Ik=bi(),Nk=uu(),Lk=k0(),hu=to(),Bk=gc(),Rk=yn(),pu=mc(),du=class i{constructor(e,t,r){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,Lt.NODE_TYPE,{value:Lt.DOC});let n=null;typeof t=="function"||Array.isArray(t)?n=t:r===void 0&&t&&(r=t,t=void 0);let s=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},r);this.options=s;let{version:o}=s;r!=null&&r._directives?(this.directives=r._directives.atDocument(),this.directives.yaml.explicit&&(o=this.directives.yaml.version)):this.directives=new pu.Directives({version:o}),this.setSchema(o,r),this.contents=e===void 0?null:this.createNode(e,n,r)}clone(){let e=Object.create(i.prototype,{[Lt.NODE_TYPE]:{value:Lt.DOC}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=Lt.isNode(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){$r(this.contents)&&this.contents.add(e)}addIn(e,t){$r(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){let r=hu.anchorNames(this);e.anchor=!t||r.has(t)?hu.findNewAnchor(t||"a",r):t}return new Tk.Alias(e.anchor)}createNode(e,t,r){let n;if(typeof t=="function")e=t.call({"":e},"",e),n=t;else if(Array.isArray(t)){let b=E=>typeof E=="number"||E instanceof String||E instanceof Number,x=t.filter(b).map(String);x.length>0&&(t=t.concat(x)),n=t}else r===void 0&&t&&(r=t,t=void 0);let{aliasDuplicateObjects:s,anchorPrefix:o,flow:a,keepUndefined:l,onTagObj:c,tag:u}=r!=null?r:{},{onAnchor:f,setAnchors:d,sourceObjects:m}=hu.createNodeAnchors(this,o||"a"),g={aliasDuplicateObjects:s!=null?s:!0,keepUndefined:l!=null?l:!1,onAnchor:f,onTagObj:c,replacer:n,schema:this.schema,sourceObjects:m},y=Rk.createNode(e,u,g);return a&&Lt.isCollection(y)&&(y.flow=!0),d(),y}createPair(e,t,r={}){let n=this.createNode(e,null,r),s=this.createNode(t,null,r);return new Ak.Pair(n,s)}delete(e){return $r(this.contents)?this.contents.delete(e):!1}deleteIn(e){return Ur.isEmptyPath(e)?this.contents==null?!1:(this.contents=null,!0):$r(this.contents)?this.contents.deleteIn(e):!1}get(e,t){return Lt.isCollection(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return Ur.isEmptyPath(e)?!t&&Lt.isScalar(this.contents)?this.contents.value:this.contents:Lt.isCollection(this.contents)?this.contents.getIn(e,t):void 0}has(e){return Lt.isCollection(this.contents)?this.contents.has(e):!1}hasIn(e){return Ur.isEmptyPath(e)?this.contents!==void 0:Lt.isCollection(this.contents)?this.contents.hasIn(e):!1}set(e,t){this.contents==null?this.contents=Ur.collectionFromPath(this.schema,[e],t):$r(this.contents)&&this.contents.set(e,t)}setIn(e,t){Ur.isEmptyPath(e)?this.contents=t:this.contents==null?this.contents=Ur.collectionFromPath(this.schema,Array.from(e),t):$r(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){typeof e=="number"&&(e=String(e));let r;switch(e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new pu.Directives({version:"1.1"}),r={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new pu.Directives({version:e}),r={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,r=null;break;default:{let n=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${n}`)}}if(t.schema instanceof Object)this.schema=t.schema;else if(r)this.schema=new Nk.Schema(Object.assign(r,t));else throw new Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:t,mapAsMap:r,maxAliasCount:n,onAnchor:s,reviver:o}={}){let a={anchors:new Map,doc:this,keep:!e,mapAsMap:r===!0,mapKeyWarned:!1,maxAliasCount:typeof n=="number"?n:100},l=Ik.toJS(this.contents,t!=null?t:"",a);if(typeof s=="function")for(let{count:c,res:u}of a.anchors.values())s(u,c);return typeof o=="function"?Bk.applyReviver(o,{"":l},"",l):l}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){let t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return Lk.stringifyDocument(this,e)}};function $r(i){if(Lt.isCollection(i))return!0;throw new Error("Expected a YAML collection as document contents")}C0.Document=du});var Mn=_(Pn=>{"use strict";var Rn=class extends Error{constructor(e,t,r,n){super(),this.name=e,this.code=r,this.message=n,this.pos=t}},mu=class extends Rn{constructor(e,t,r){super("YAMLParseError",e,t,r)}},gu=class extends Rn{constructor(e,t,r){super("YAMLWarning",e,t,r)}},Pk=(i,e)=>t=>{if(t.pos[0]===-1)return;t.linePos=t.pos.map(a=>e.linePos(a));let{line:r,col:n}=t.linePos[0];t.message+=` at line ${r}, column ${n}`;let s=n-1,o=i.substring(e.lineStarts[r-1],e.lineStarts[r]).replace(/[\n\r]+$/,"");if(s>=60&&o.length>80){let a=Math.min(s-39,o.length-79);o="\u2026"+o.substring(a),s-=a-1}if(o.length>80&&(o=o.substring(0,79)+"\u2026"),r>1&&/^ *$/.test(o.substring(0,s))){let a=i.substring(e.lineStarts[r-2],e.lineStarts[r-1]);a.length>80&&(a=a.substring(0,79)+`\u2026
`),o=a+o}if(/[^ ]/.test(o)){let a=1,l=t.linePos[1];l&&l.line===r&&l.col>n&&(a=Math.max(1,Math.min(l.col-n,80-s)));let c=" ".repeat(s)+"^".repeat(a);t.message+=`:

${o}
${c}
`}};Pn.YAMLError=Rn;Pn.YAMLParseError=mu;Pn.YAMLWarning=gu;Pn.prettifyError=Pk});var Dn=_(T0=>{"use strict";function Mk(i,{flow:e,indicator:t,next:r,offset:n,onError:s,parentIndent:o,startOnNewline:a}){let l=!1,c=a,u=a,f="",d="",m=!1,g=!1,y=null,b=null,x=null,E=null,k=null,O=null,S=null;for(let A of i)switch(g&&(A.type!=="space"&&A.type!=="newline"&&A.type!=="comma"&&s(A.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),g=!1),y&&(c&&A.type!=="comment"&&A.type!=="newline"&&s(y,"TAB_AS_INDENT","Tabs are not allowed as indentation"),y=null),A.type){case"space":!e&&(t!=="doc-start"||(r==null?void 0:r.type)!=="flow-collection")&&A.source.includes("	")&&(y=A),u=!0;break;case"comment":{u||s(A,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let C=A.source.substring(1)||" ";f?f+=d+C:f=C,d="",c=!1;break}case"newline":c?f?f+=A.source:l=!0:d+=A.source,c=!0,m=!0,(b||x)&&(E=A),u=!0;break;case"anchor":b&&s(A,"MULTIPLE_ANCHORS","A node can have at most one anchor"),A.source.endsWith(":")&&s(A.offset+A.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),b=A,S===null&&(S=A.offset),c=!1,u=!1,g=!0;break;case"tag":{x&&s(A,"MULTIPLE_TAGS","A node can have at most one tag"),x=A,S===null&&(S=A.offset),c=!1,u=!1,g=!0;break}case t:(b||x)&&s(A,"BAD_PROP_ORDER",`Anchors and tags must be after the ${A.source} indicator`),O&&s(A,"UNEXPECTED_TOKEN",`Unexpected ${A.source} in ${e!=null?e:"collection"}`),O=A,c=t==="seq-item-ind"||t==="explicit-key-ind",u=!1;break;case"comma":if(e){k&&s(A,"UNEXPECTED_TOKEN",`Unexpected , in ${e}`),k=A,c=!1,u=!1;break}default:s(A,"UNEXPECTED_TOKEN",`Unexpected ${A.type} token`),c=!1,u=!1}let R=i[i.length-1],T=R?R.offset+R.source.length:n;return g&&r&&r.type!=="space"&&r.type!=="newline"&&r.type!=="comma"&&(r.type!=="scalar"||r.source!=="")&&s(r.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),y&&(c&&y.indent<=o||(r==null?void 0:r.type)==="block-map"||(r==null?void 0:r.type)==="block-seq")&&s(y,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:k,found:O,spaceBefore:l,comment:f,hasNewline:m,anchor:b,tag:x,newlineAfterProp:E,end:T,start:S!=null?S:T}}T0.resolveProps=Mk});var Uo=_(A0=>{"use strict";function vu(i){if(!i)return null;switch(i.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(i.source.includes(`
`))return!0;if(i.end){for(let e of i.end)if(e.type==="newline")return!0}return!1;case"flow-collection":for(let e of i.items){for(let t of e.start)if(t.type==="newline")return!0;if(e.sep){for(let t of e.sep)if(t.type==="newline")return!0}if(vu(e.key)||vu(e.value))return!0}return!1;default:return!0}}A0.containsNewline=vu});var yu=_(I0=>{"use strict";var Dk=Uo();function Fk(i,e,t){if((e==null?void 0:e.type)==="flow-collection"){let r=e.end[0];r.indent===i&&(r.source==="]"||r.source==="}")&&Dk.containsNewline(e)&&t(r,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}I0.flowIndentCheck=Fk});var bu=_(L0=>{"use strict";var N0=Se();function qk(i,e,t){let{uniqueKeys:r}=i.options;if(r===!1)return!1;let n=typeof r=="function"?r:(s,o)=>s===o||N0.isScalar(s)&&N0.isScalar(o)&&s.value===o.value;return e.some(s=>n(s.key,t))}L0.mapIncludes=qk});var F0=_(D0=>{"use strict";var B0=Si(),jk=Oi(),R0=Dn(),Uk=Uo(),P0=yu(),$k=bu(),M0="All mapping items must start at the same column";function Vk({composeNode:i,composeEmptyNode:e},t,r,n,s){var u,f;let o=(u=s==null?void 0:s.nodeClass)!=null?u:jk.YAMLMap,a=new o(t.schema);t.atRoot&&(t.atRoot=!1);let l=r.offset,c=null;for(let d of r.items){let{start:m,key:g,sep:y,value:b}=d,x=R0.resolveProps(m,{indicator:"explicit-key-ind",next:g!=null?g:y==null?void 0:y[0],offset:l,onError:n,parentIndent:r.indent,startOnNewline:!0}),E=!x.found;if(E){if(g&&(g.type==="block-seq"?n(l,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in g&&g.indent!==r.indent&&n(l,"BAD_INDENT",M0)),!x.anchor&&!x.tag&&!y){c=x.end,x.comment&&(a.comment?a.comment+=`
`+x.comment:a.comment=x.comment);continue}(x.newlineAfterProp||Uk.containsNewline(g))&&n(g!=null?g:m[m.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else((f=x.found)==null?void 0:f.indent)!==r.indent&&n(l,"BAD_INDENT",M0);t.atKey=!0;let k=x.end,O=g?i(t,g,x,n):e(t,k,m,null,x,n);t.schema.compat&&P0.flowIndentCheck(r.indent,g,n),t.atKey=!1,$k.mapIncludes(t,a.items,O)&&n(k,"DUPLICATE_KEY","Map keys must be unique");let S=R0.resolveProps(y!=null?y:[],{indicator:"map-value-ind",next:b,offset:O.range[2],onError:n,parentIndent:r.indent,startOnNewline:!g||g.type==="block-scalar"});if(l=S.end,S.found){E&&((b==null?void 0:b.type)==="block-map"&&!S.hasNewline&&n(l,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),t.options.strict&&x.start<S.found.offset-1024&&n(O.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));let R=b?i(t,b,S,n):e(t,l,y,null,S,n);t.schema.compat&&P0.flowIndentCheck(r.indent,b,n),l=R.range[2];let T=new B0.Pair(O,R);t.options.keepSourceTokens&&(T.srcToken=d),a.items.push(T)}else{E&&n(O.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),S.comment&&(O.comment?O.comment+=`
`+S.comment:O.comment=S.comment);let R=new B0.Pair(O);t.options.keepSourceTokens&&(R.srcToken=d),a.items.push(R)}}return c&&c<l&&n(c,"IMPOSSIBLE","Map comment with trailing content"),a.range=[r.offset,l,c!=null?c:l],a}D0.resolveBlockMap=Vk});var j0=_(q0=>{"use strict";var Hk=ki(),Gk=Dn(),Wk=yu();function Yk({composeNode:i,composeEmptyNode:e},t,r,n,s){var u;let o=(u=s==null?void 0:s.nodeClass)!=null?u:Hk.YAMLSeq,a=new o(t.schema);t.atRoot&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let l=r.offset,c=null;for(let{start:f,value:d}of r.items){let m=Gk.resolveProps(f,{indicator:"seq-item-ind",next:d,offset:l,onError:n,parentIndent:r.indent,startOnNewline:!0});if(!m.found)if(m.anchor||m.tag||d)d&&d.type==="block-seq"?n(m.end,"BAD_INDENT","All sequence items must start at the same column"):n(l,"MISSING_CHAR","Sequence item without - indicator");else{c=m.end,m.comment&&(a.comment=m.comment);continue}let g=d?i(t,d,m,n):e(t,m.end,f,null,m,n);t.schema.compat&&Wk.flowIndentCheck(r.indent,d,n),l=g.range[2],a.items.push(g)}return a.range=[r.offset,l,c!=null?c:l],a}q0.resolveBlockSeq=Yk});var Vr=_(U0=>{"use strict";function Kk(i,e,t,r){let n="";if(i){let s=!1,o="";for(let a of i){let{source:l,type:c}=a;switch(c){case"space":s=!0;break;case"comment":{t&&!s&&r(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let u=l.substring(1)||" ";n?n+=o+u:n=u,o="";break}case"newline":n&&(o+=l),s=!0;break;default:r(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}e+=l.length}}return{comment:n,offset:e}}U0.resolveEnd=Kk});var G0=_(H0=>{"use strict";var zk=Se(),Jk=Si(),$0=Oi(),Zk=ki(),Qk=Vr(),V0=Dn(),Xk=Uo(),eC=bu(),_u="Block collections are not allowed within flow collections",wu=i=>i&&(i.type==="block-map"||i.type==="block-seq");function tC({composeNode:i,composeEmptyNode:e},t,r,n,s){var b,x;let o=r.start.source==="{",a=o?"flow map":"flow sequence",l=(b=s==null?void 0:s.nodeClass)!=null?b:o?$0.YAMLMap:Zk.YAMLSeq,c=new l(t.schema);c.flow=!0;let u=t.atRoot;u&&(t.atRoot=!1),t.atKey&&(t.atKey=!1);let f=r.offset+r.start.source.length;for(let E=0;E<r.items.length;++E){let k=r.items[E],{start:O,key:S,sep:R,value:T}=k,A=V0.resolveProps(O,{flow:a,indicator:"explicit-key-ind",next:S!=null?S:R==null?void 0:R[0],offset:f,onError:n,parentIndent:r.indent,startOnNewline:!1});if(!A.found){if(!A.anchor&&!A.tag&&!R&&!T){E===0&&A.comma?n(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):E<r.items.length-1&&n(A.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),A.comment&&(c.comment?c.comment+=`
`+A.comment:c.comment=A.comment),f=A.end;continue}!o&&t.options.strict&&Xk.containsNewline(S)&&n(S,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(E===0)A.comma&&n(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(A.comma||n(A.start,"MISSING_CHAR",`Missing , between ${a} items`),A.comment){let C="";e:for(let L of O)switch(L.type){case"comma":case"space":break;case"comment":C=L.source.substring(1);break e;default:break e}if(C){let L=c.items[c.items.length-1];zk.isPair(L)&&(L=(x=L.value)!=null?x:L.key),L.comment?L.comment+=`
`+C:L.comment=C,A.comment=A.comment.substring(C.length+1)}}if(!o&&!R&&!A.found){let C=T?i(t,T,A,n):e(t,A.end,R,null,A,n);c.items.push(C),f=C.range[2],wu(T)&&n(C.range,"BLOCK_IN_FLOW",_u)}else{t.atKey=!0;let C=A.end,L=S?i(t,S,A,n):e(t,C,O,null,A,n);wu(S)&&n(L.range,"BLOCK_IN_FLOW",_u),t.atKey=!1;let P=V0.resolveProps(R!=null?R:[],{flow:a,indicator:"map-value-ind",next:T,offset:L.range[2],onError:n,parentIndent:r.indent,startOnNewline:!1});if(P.found){if(!o&&!A.found&&t.options.strict){if(R)for(let H of R){if(H===P.found)break;if(H.type==="newline"){n(H,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}A.start<P.found.offset-1024&&n(P.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else T&&("source"in T&&T.source&&T.source[0]===":"?n(T,"MISSING_CHAR",`Missing space after : in ${a}`):n(P.start,"MISSING_CHAR",`Missing , or : between ${a} items`));let U=T?i(t,T,P,n):P.found?e(t,P.end,R,null,P,n):null;U?wu(T)&&n(U.range,"BLOCK_IN_FLOW",_u):P.comment&&(L.comment?L.comment+=`
`+P.comment:L.comment=P.comment);let F=new Jk.Pair(L,U);if(t.options.keepSourceTokens&&(F.srcToken=k),o){let H=c;eC.mapIncludes(t,H.items,L)&&n(C,"DUPLICATE_KEY","Map keys must be unique"),H.items.push(F)}else{let H=new $0.YAMLMap(t.schema);H.flow=!0,H.items.push(F);let j=(U!=null?U:L).range;H.range=[L.range[0],j[1],j[2]],c.items.push(H)}f=U?U.range[2]:P.end}}let d=o?"}":"]",[m,...g]=r.end,y=f;if(m&&m.source===d)y=m.offset+m.source.length;else{let E=a[0].toUpperCase()+a.substring(1),k=u?`${E} must end with a ${d}`:`${E} in block collection must be sufficiently indented and end with a ${d}`;n(f,u?"MISSING_CHAR":"BAD_INDENT",k),m&&m.source.length!==1&&g.unshift(m)}if(g.length>0){let E=Qk.resolveEnd(g,y,t.options.strict,n);E.comment&&(c.comment?c.comment+=`
`+E.comment:c.comment=E.comment),c.range=[r.offset,y,E.offset]}else c.range=[r.offset,y,y];return c}H0.resolveFlowCollection=tC});var Y0=_(W0=>{"use strict";var iC=Se(),rC=je(),nC=Oi(),sC=ki(),oC=F0(),aC=j0(),lC=G0();function xu(i,e,t,r,n,s){let o=t.type==="block-map"?oC.resolveBlockMap(i,e,t,r,s):t.type==="block-seq"?aC.resolveBlockSeq(i,e,t,r,s):lC.resolveFlowCollection(i,e,t,r,s),a=o.constructor;return n==="!"||n===a.tagName?(o.tag=a.tagName,o):(n&&(o.tag=n),o)}function cC(i,e,t,r,n){var d,m;let s=r.tag,o=s?e.directives.tagName(s.source,g=>n(s,"TAG_RESOLVE_FAILED",g)):null;if(t.type==="block-seq"){let{anchor:g,newlineAfterProp:y}=r,b=g&&s?g.offset>s.offset?g:s:g!=null?g:s;b&&(!y||y.offset<b.offset)&&n(b,"MISSING_CHAR","Missing newline after block sequence props")}let a=t.type==="block-map"?"map":t.type==="block-seq"?"seq":t.start.source==="{"?"map":"seq";if(!s||!o||o==="!"||o===nC.YAMLMap.tagName&&a==="map"||o===sC.YAMLSeq.tagName&&a==="seq")return xu(i,e,t,n,o);let l=e.schema.tags.find(g=>g.tag===o&&g.collection===a);if(!l){let g=e.schema.knownTags[o];if(g&&g.collection===a)e.schema.tags.push(Object.assign({},g,{default:!1})),l=g;else return g!=null&&g.collection?n(s,"BAD_COLLECTION_TYPE",`${g.tag} used for ${a} collection, but expects ${g.collection}`,!0):n(s,"TAG_RESOLVE_FAILED",`Unresolved tag: ${o}`,!0),xu(i,e,t,n,o)}let c=xu(i,e,t,n,o,l),u=(m=(d=l.resolve)==null?void 0:d.call(l,c,g=>n(s,"TAG_RESOLVE_FAILED",g),e.options))!=null?m:c,f=iC.isNode(u)?u:new rC.Scalar(u);return f.range=c.range,f.tag=o,l!=null&&l.format&&(f.format=l.format),f}W0.composeCollection=cC});var Eu=_(K0=>{"use strict";var Su=je();function uC(i,e,t){let r=e.offset,n=fC(e,i.options.strict,t);if(!n)return{value:"",type:null,comment:"",range:[r,r,r]};let s=n.mode===">"?Su.Scalar.BLOCK_FOLDED:Su.Scalar.BLOCK_LITERAL,o=e.source?hC(e.source):[],a=o.length;for(let y=o.length-1;y>=0;--y){let b=o[y][1];if(b===""||b==="\r")a=y;else break}if(a===0){let y=n.chomp==="+"&&o.length>0?`
`.repeat(Math.max(1,o.length-1)):"",b=r+n.length;return e.source&&(b+=e.source.length),{value:y,type:s,comment:n.comment,range:[r,b,b]}}let l=e.indent+n.indent,c=e.offset+n.length,u=0;for(let y=0;y<a;++y){let[b,x]=o[y];if(x===""||x==="\r")n.indent===0&&b.length>l&&(l=b.length);else{b.length<l&&t(c+b.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),n.indent===0&&(l=b.length),u=y,l===0&&!i.atRoot&&t(c,"BAD_INDENT","Block scalar values in collections must be indented");break}c+=b.length+x.length+1}for(let y=o.length-1;y>=a;--y)o[y][0].length>l&&(a=y+1);let f="",d="",m=!1;for(let y=0;y<u;++y)f+=o[y][0].slice(l)+`
`;for(let y=u;y<a;++y){let[b,x]=o[y];c+=b.length+x.length+1;let E=x[x.length-1]==="\r";if(E&&(x=x.slice(0,-1)),x&&b.length<l){let O=`Block scalar lines must not be less indented than their ${n.indent?"explicit indentation indicator":"first line"}`;t(c-x.length-(E?2:1),"BAD_INDENT",O),b=""}s===Su.Scalar.BLOCK_LITERAL?(f+=d+b.slice(l)+x,d=`
`):b.length>l||x[0]==="	"?(d===" "?d=`
`:!m&&d===`
`&&(d=`

`),f+=d+b.slice(l)+x,d=`
`,m=!0):x===""?d===`
`?f+=`
`:d=`
`:(f+=d+x,d=" ",m=!1)}switch(n.chomp){case"-":break;case"+":for(let y=a;y<o.length;++y)f+=`
`+o[y][0].slice(l);f[f.length-1]!==`
`&&(f+=`
`);break;default:f+=`
`}let g=r+n.length+e.source.length;return{value:f,type:s,comment:n.comment,range:[r,g,g]}}function fC({offset:i,props:e},t,r){if(e[0].type!=="block-scalar-header")return r(e[0],"IMPOSSIBLE","Block scalar header not found"),null;let{source:n}=e[0],s=n[0],o=0,a="",l=-1;for(let d=1;d<n.length;++d){let m=n[d];if(!a&&(m==="-"||m==="+"))a=m;else{let g=Number(m);!o&&g?o=g:l===-1&&(l=i+d)}}l!==-1&&r(l,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${n}`);let c=!1,u="",f=n.length;for(let d=1;d<e.length;++d){let m=e[d];switch(m.type){case"space":c=!0;case"newline":f+=m.source.length;break;case"comment":t&&!c&&r(m,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),f+=m.source.length,u=m.source.substring(1);break;case"error":r(m,"UNEXPECTED_TOKEN",m.message),f+=m.source.length;break;default:{let g=`Unexpected token in block scalar header: ${m.type}`;r(m,"UNEXPECTED_TOKEN",g);let y=m.source;y&&typeof y=="string"&&(f+=y.length)}}}return{mode:s,indent:o,chomp:a,comment:u,length:f}}function hC(i){let e=i.split(/\n( *)/),t=e[0],r=t.match(/^( *)/),s=[r!=null&&r[1]?[r[1],t.slice(r[1].length)]:["",t]];for(let o=1;o<e.length;o+=2)s.push([e[o],e[o+1]]);return s}K0.resolveBlockScalar=uC});var ku=_(J0=>{"use strict";var Ou=je(),pC=Vr();function dC(i,e,t){let{offset:r,type:n,source:s,end:o}=i,a,l,c=(d,m,g)=>t(r+d,m,g);switch(n){case"scalar":a=Ou.Scalar.PLAIN,l=mC(s,c);break;case"single-quoted-scalar":a=Ou.Scalar.QUOTE_SINGLE,l=gC(s,c);break;case"double-quoted-scalar":a=Ou.Scalar.QUOTE_DOUBLE,l=vC(s,c);break;default:return t(i,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${n}`),{value:"",type:null,comment:"",range:[r,r+s.length,r+s.length]}}let u=r+s.length,f=pC.resolveEnd(o,u,e,t);return{value:l,type:a,comment:f.comment,range:[r,u,f.offset]}}function mC(i,e){let t="";switch(i[0]){case"	":t="a tab character";break;case",":t="flow indicator character ,";break;case"%":t="directive indicator character %";break;case"|":case">":{t=`block scalar indicator ${i[0]}`;break}case"@":case"`":{t=`reserved character ${i[0]}`;break}}return t&&e(0,"BAD_SCALAR_START",`Plain value cannot start with ${t}`),z0(i)}function gC(i,e){return(i[i.length-1]!=="'"||i.length===1)&&e(i.length,"MISSING_CHAR","Missing closing 'quote"),z0(i.slice(1,-1)).replace(/''/g,"'")}function z0(i){var l;let e,t;try{e=new RegExp(`(.*?)(?<![ 	])[ 	]*\r?
`,"sy"),t=new RegExp(`[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?
`,"sy")}catch{e=/(.*?)[ \t]*\r?\n/sy,t=/[ \t]*(.*?)[ \t]*\r?\n/sy}let r=e.exec(i);if(!r)return i;let n=r[1],s=" ",o=e.lastIndex;for(t.lastIndex=o;r=t.exec(i);)r[1]===""?s===`
`?n+=s:s=`
`:(n+=s+r[1],s=" "),o=t.lastIndex;let a=/[ \t]*(.*)/sy;return a.lastIndex=o,r=a.exec(i),n+s+((l=r==null?void 0:r[1])!=null?l:"")}function vC(i,e){let t="";for(let r=1;r<i.length-1;++r){let n=i[r];if(!(n==="\r"&&i[r+1]===`
`))if(n===`
`){let{fold:s,offset:o}=yC(i,r);t+=s,r=o}else if(n==="\\"){let s=i[++r],o=bC[s];if(o)t+=o;else if(s===`
`)for(s=i[r+1];s===" "||s==="	";)s=i[++r+1];else if(s==="\r"&&i[r+1]===`
`)for(s=i[++r+1];s===" "||s==="	";)s=i[++r+1];else if(s==="x"||s==="u"||s==="U"){let a={x:2,u:4,U:8}[s];t+=_C(i,r+1,a,e),r+=a}else{let a=i.substr(r-1,2);e(r-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),t+=a}}else if(n===" "||n==="	"){let s=r,o=i[r+1];for(;o===" "||o==="	";)o=i[++r+1];o!==`
`&&!(o==="\r"&&i[r+2]===`
`)&&(t+=r>s?i.slice(s,r+1):n)}else t+=n}return(i[i.length-1]!=='"'||i.length===1)&&e(i.length,"MISSING_CHAR",'Missing closing "quote'),t}function yC(i,e){let t="",r=i[e+1];for(;(r===" "||r==="	"||r===`
`||r==="\r")&&!(r==="\r"&&i[e+2]!==`
`);)r===`
`&&(t+=`
`),e+=1,r=i[e+1];return t||(t=" "),{fold:t,offset:e}}var bC={0:"\0",a:"\x07",b:"\b",e:"\x1B",f:"\f",n:`
`,r:"\r",t:"	",v:"\v",N:"\x85",_:"\xA0",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function _C(i,e,t,r){let n=i.substr(e,t),o=n.length===t&&/^[0-9a-fA-F]+$/.test(n)?parseInt(n,16):NaN;if(isNaN(o)){let a=i.substr(e-2,t+2);return r(e-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${a}`),a}return String.fromCodePoint(o)}J0.resolveFlowScalar=dC});var X0=_(Q0=>{"use strict";var tr=Se(),Z0=je(),wC=Eu(),xC=ku();function SC(i,e,t,r){let{value:n,type:s,comment:o,range:a}=e.type==="block-scalar"?wC.resolveBlockScalar(i,e,r):xC.resolveFlowScalar(e,i.options.strict,r),l=t?i.directives.tagName(t.source,f=>r(t,"TAG_RESOLVE_FAILED",f)):null,c;i.options.stringKeys&&i.atKey?c=i.schema[tr.SCALAR]:l?c=EC(i.schema,n,l,t,r):e.type==="scalar"?c=OC(i,n,e,r):c=i.schema[tr.SCALAR];let u;try{let f=c.resolve(n,d=>r(t!=null?t:e,"TAG_RESOLVE_FAILED",d),i.options);u=tr.isScalar(f)?f:new Z0.Scalar(f)}catch(f){let d=f instanceof Error?f.message:String(f);r(t!=null?t:e,"TAG_RESOLVE_FAILED",d),u=new Z0.Scalar(n)}return u.range=a,u.source=n,s&&(u.type=s),l&&(u.tag=l),c.format&&(u.format=c.format),o&&(u.comment=o),u}function EC(i,e,t,r,n){var a;if(t==="!")return i[tr.SCALAR];let s=[];for(let l of i.tags)if(!l.collection&&l.tag===t)if(l.default&&l.test)s.push(l);else return l;for(let l of s)if((a=l.test)!=null&&a.test(e))return l;let o=i.knownTags[t];return o&&!o.collection?(i.tags.push(Object.assign({},o,{default:!1,test:void 0})),o):(n(r,"TAG_RESOLVE_FAILED",`Unresolved tag: ${t}`,t!=="tag:yaml.org,2002:str"),i[tr.SCALAR])}function OC({atKey:i,directives:e,schema:t},r,n,s){var a;let o=t.tags.find(l=>{var c;return(l.default===!0||i&&l.default==="key")&&((c=l.test)==null?void 0:c.test(r))})||t[tr.SCALAR];if(t.compat){let l=(a=t.compat.find(c=>{var u;return c.default&&((u=c.test)==null?void 0:u.test(r))}))!=null?a:t[tr.SCALAR];if(o.tag!==l.tag){let c=e.tagString(o.tag),u=e.tagString(l.tag),f=`Value may be parsed as either ${c} or ${u}`;s(n,"TAG_RESOLVE_FAILED",f,!0)}}return o}Q0.composeScalar=SC});var tv=_(ev=>{"use strict";function kC(i,e,t){if(e){t===null&&(t=e.length);for(let r=t-1;r>=0;--r){let n=e[r];switch(n.type){case"space":case"comment":case"newline":i-=n.source.length;continue}for(n=e[++r];(n==null?void 0:n.type)==="space";)i+=n.source.length,n=e[++r];break}}return i}ev.emptyScalarPosition=kC});var nv=_(Tu=>{"use strict";var CC=vn(),TC=Se(),AC=Y0(),iv=X0(),IC=Vr(),NC=tv(),LC={composeNode:rv,composeEmptyNode:Cu};function rv(i,e,t,r){let n=i.atKey,{spaceBefore:s,comment:o,anchor:a,tag:l}=t,c,u=!0;switch(e.type){case"alias":c=BC(i,e,r),(a||l)&&r(e,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=iv.composeScalar(i,e,l,r),a&&(c.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=AC.composeCollection(LC,i,e,t,r),a&&(c.anchor=a.source.substring(1));break;default:{let f=e.type==="error"?e.message:`Unsupported token (type: ${e.type})`;r(e,"UNEXPECTED_TOKEN",f),c=Cu(i,e.offset,void 0,null,t,r),u=!1}}return a&&c.anchor===""&&r(a,"BAD_ALIAS","Anchor cannot be an empty string"),n&&i.options.stringKeys&&(!TC.isScalar(c)||typeof c.value!="string"||c.tag&&c.tag!=="tag:yaml.org,2002:str")&&r(l!=null?l:e,"NON_STRING_KEY","With stringKeys, all keys must be strings"),s&&(c.spaceBefore=!0),o&&(e.type==="scalar"&&e.source===""?c.comment=o:c.commentBefore=o),i.options.keepSourceTokens&&u&&(c.srcToken=e),c}function Cu(i,e,t,r,{spaceBefore:n,comment:s,anchor:o,tag:a,end:l},c){let u={type:"scalar",offset:NC.emptyScalarPosition(e,t,r),indent:-1,source:""},f=iv.composeScalar(i,u,a,c);return o&&(f.anchor=o.source.substring(1),f.anchor===""&&c(o,"BAD_ALIAS","Anchor cannot be an empty string")),n&&(f.spaceBefore=!0),s&&(f.comment=s,f.range[2]=l),f}function BC({options:i},{offset:e,source:t,end:r},n){let s=new CC.Alias(t.substring(1));s.source===""&&n(e,"BAD_ALIAS","Alias cannot be an empty string"),s.source.endsWith(":")&&n(e+t.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);let o=e+t.length,a=IC.resolveEnd(r,o,i.strict,n);return s.range=[e,o,a.offset],a.comment&&(s.comment=a.comment),s}Tu.composeEmptyNode=Cu;Tu.composeNode=rv});var av=_(ov=>{"use strict";var RC=Bn(),sv=nv(),PC=Vr(),MC=Dn();function DC(i,e,{offset:t,start:r,value:n,end:s},o){let a=Object.assign({_directives:e},i),l=new RC.Document(void 0,a),c={atKey:!1,atRoot:!0,directives:l.directives,options:l.options,schema:l.schema},u=MC.resolveProps(r,{indicator:"doc-start",next:n!=null?n:s==null?void 0:s[0],offset:t,onError:o,parentIndent:0,startOnNewline:!0});u.found&&(l.directives.docStart=!0,n&&(n.type==="block-map"||n.type==="block-seq")&&!u.hasNewline&&o(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),l.contents=n?sv.composeNode(c,n,u,o):sv.composeEmptyNode(c,u.end,r,null,u,o);let f=l.contents.range[2],d=PC.resolveEnd(s,f,!1,o);return d.comment&&(l.comment=d.comment),l.range=[t,f,d.offset],l}ov.composeDoc=DC});var Iu=_(uv=>{"use strict";var FC=mc(),qC=Bn(),Fn=Mn(),lv=Se(),jC=av(),UC=Vr();function qn(i){if(typeof i=="number")return[i,i+1];if(Array.isArray(i))return i.length===2?i:[i[0],i[1]];let{offset:e,source:t}=i;return[e,e+(typeof t=="string"?t.length:1)]}function cv(i){var n;let e="",t=!1,r=!1;for(let s=0;s<i.length;++s){let o=i[s];switch(o[0]){case"#":e+=(e===""?"":r?`

`:`
`)+(o.substring(1)||" "),t=!0,r=!1;break;case"%":((n=i[s+1])==null?void 0:n[0])!=="#"&&(s+=1),t=!1;break;default:t||(r=!0),t=!1}}return{comment:e,afterEmptyLine:r}}var Au=class{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(t,r,n,s)=>{let o=qn(t);s?this.warnings.push(new Fn.YAMLWarning(o,r,n)):this.errors.push(new Fn.YAMLParseError(o,r,n))},this.directives=new FC.Directives({version:e.version||"1.2"}),this.options=e}decorate(e,t){let{comment:r,afterEmptyLine:n}=cv(this.prelude);if(r){let s=e.contents;if(t)e.comment=e.comment?`${e.comment}
${r}`:r;else if(n||e.directives.docStart||!s)e.commentBefore=r;else if(lv.isCollection(s)&&!s.flow&&s.items.length>0){let o=s.items[0];lv.isPair(o)&&(o=o.key);let a=o.commentBefore;o.commentBefore=a?`${r}
${a}`:r}else{let o=s.commentBefore;s.commentBefore=o?`${r}
${o}`:r}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:cv(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,r=-1){for(let n of e)yield*this.next(n);yield*this.end(t,r)}*next(e){switch(process.env.LOG_STREAM&&console.dir(e,{depth:null}),e.type){case"directive":this.directives.add(e.source,(t,r,n)=>{let s=qn(e);s[0]+=t,this.onError(s,"BAD_DIRECTIVE",r,n)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{let t=jC.composeDoc(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{let t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,r=new Fn.YAMLParseError(qn(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(r):this.doc.errors.push(r);break}case"doc-end":{if(!this.doc){let r="Unexpected doc-end without preceding document";this.errors.push(new Fn.YAMLParseError(qn(e),"UNEXPECTED_TOKEN",r));break}this.doc.directives.docEnd=!0;let t=UC.resolveEnd(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){let r=this.doc.comment;this.doc.comment=r?`${r}
${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new Fn.YAMLParseError(qn(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){let r=Object.assign({_directives:this.directives},this.options),n=new qC.Document(void 0,r);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),n.range=[0,t,t],this.decorate(n,!1),yield n}}};uv.Composer=Au});var pv=_($o=>{"use strict";var $C=Eu(),VC=ku(),HC=Mn(),fv=xn();function GC(i,e=!0,t){if(i){let r=(n,s,o)=>{let a=typeof n=="number"?n:Array.isArray(n)?n[0]:n.offset;if(t)t(a,s,o);else throw new HC.YAMLParseError([a,a+1],s,o)};switch(i.type){case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return VC.resolveFlowScalar(i,e,r);case"block-scalar":return $C.resolveBlockScalar({options:{strict:e}},i,r)}}return null}function WC(i,e){var c;let{implicitKey:t=!1,indent:r,inFlow:n=!1,offset:s=-1,type:o="PLAIN"}=e,a=fv.stringifyString({type:o,value:i},{implicitKey:t,indent:r>0?" ".repeat(r):"",inFlow:n,options:{blockQuote:!0,lineWidth:-1}}),l=(c=e.end)!=null?c:[{type:"newline",offset:-1,indent:r,source:`
`}];switch(a[0]){case"|":case">":{let u=a.indexOf(`
`),f=a.substring(0,u),d=a.substring(u+1)+`
`,m=[{type:"block-scalar-header",offset:s,indent:r,source:f}];return hv(m,l)||m.push({type:"newline",offset:-1,indent:r,source:`
`}),{type:"block-scalar",offset:s,indent:r,props:m,source:d}}case'"':return{type:"double-quoted-scalar",offset:s,indent:r,source:a,end:l};case"'":return{type:"single-quoted-scalar",offset:s,indent:r,source:a,end:l};default:return{type:"scalar",offset:s,indent:r,source:a,end:l}}}function YC(i,e,t={}){let{afterKey:r=!1,implicitKey:n=!1,inFlow:s=!1,type:o}=t,a="indent"in i?i.indent:null;if(r&&typeof a=="number"&&(a+=2),!o)switch(i.type){case"single-quoted-scalar":o="QUOTE_SINGLE";break;case"double-quoted-scalar":o="QUOTE_DOUBLE";break;case"block-scalar":{let c=i.props[0];if(c.type!=="block-scalar-header")throw new Error("Invalid block scalar header");o=c.source[0]===">"?"BLOCK_FOLDED":"BLOCK_LITERAL";break}default:o="PLAIN"}let l=fv.stringifyString({type:o,value:e},{implicitKey:n||a===null,indent:a!==null&&a>0?" ".repeat(a):"",inFlow:s,options:{blockQuote:!0,lineWidth:-1}});switch(l[0]){case"|":case">":KC(i,l);break;case'"':Nu(i,l,"double-quoted-scalar");break;case"'":Nu(i,l,"single-quoted-scalar");break;default:Nu(i,l,"scalar")}}function KC(i,e){let t=e.indexOf(`
`),r=e.substring(0,t),n=e.substring(t+1)+`
`;if(i.type==="block-scalar"){let s=i.props[0];if(s.type!=="block-scalar-header")throw new Error("Invalid block scalar header");s.source=r,i.source=n}else{let{offset:s}=i,o="indent"in i?i.indent:-1,a=[{type:"block-scalar-header",offset:s,indent:o,source:r}];hv(a,"end"in i?i.end:void 0)||a.push({type:"newline",offset:-1,indent:o,source:`
`});for(let l of Object.keys(i))l!=="type"&&l!=="offset"&&delete i[l];Object.assign(i,{type:"block-scalar",indent:o,props:a,source:n})}}function hv(i,e){if(e)for(let t of e)switch(t.type){case"space":case"comment":i.push(t);break;case"newline":return i.push(t),!0}return!1}function Nu(i,e,t){switch(i.type){case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":i.type=t,i.source=e;break;case"block-scalar":{let r=i.props.slice(1),n=e.length;i.props[0].type==="block-scalar-header"&&(n-=i.props[0].source.length);for(let s of r)s.offset+=n;delete i.props,Object.assign(i,{type:t,source:e,end:r});break}case"block-map":case"block-seq":{let n={type:"newline",offset:i.offset+e.length,indent:i.indent,source:`
`};delete i.items,Object.assign(i,{type:t,source:e,end:[n]});break}default:{let r="indent"in i?i.indent:-1,n="end"in i&&Array.isArray(i.end)?i.end.filter(s=>s.type==="space"||s.type==="comment"||s.type==="newline"):[];for(let s of Object.keys(i))s!=="type"&&s!=="offset"&&delete i[s];Object.assign(i,{type:t,indent:r,source:e,end:n})}}}$o.createScalarToken=WC;$o.resolveAsScalar=GC;$o.setScalarValue=YC});var mv=_(dv=>{"use strict";var zC=i=>"type"in i?Ho(i):Vo(i);function Ho(i){switch(i.type){case"block-scalar":{let e="";for(let t of i.props)e+=Ho(t);return e+i.source}case"block-map":case"block-seq":{let e="";for(let t of i.items)e+=Vo(t);return e}case"flow-collection":{let e=i.start.source;for(let t of i.items)e+=Vo(t);for(let t of i.end)e+=t.source;return e}case"document":{let e=Vo(i);if(i.end)for(let t of i.end)e+=t.source;return e}default:{let e=i.source;if("end"in i&&i.end)for(let t of i.end)e+=t.source;return e}}}function Vo({start:i,key:e,sep:t,value:r}){let n="";for(let s of i)n+=s.source;if(e&&(n+=Ho(e)),t)for(let s of t)n+=s.source;return r&&(n+=Ho(r)),n}dv.stringify=zC});var bv=_(yv=>{"use strict";var Lu=Symbol("break visit"),JC=Symbol("skip children"),gv=Symbol("remove item");function ir(i,e){"type"in i&&i.type==="document"&&(i={start:i.start,value:i.value}),vv(Object.freeze([]),i,e)}ir.BREAK=Lu;ir.SKIP=JC;ir.REMOVE=gv;ir.itemAtPath=(i,e)=>{let t=i;for(let[r,n]of e){let s=t==null?void 0:t[r];if(s&&"items"in s)t=s.items[n];else return}return t};ir.parentCollection=(i,e)=>{let t=ir.itemAtPath(i,e.slice(0,-1)),r=e[e.length-1][0],n=t==null?void 0:t[r];if(n&&"items"in n)return n;throw new Error("Parent collection not found")};function vv(i,e,t){let r=t(e,i);if(typeof r=="symbol")return r;for(let n of["key","value"]){let s=e[n];if(s&&"items"in s){for(let o=0;o<s.items.length;++o){let a=vv(Object.freeze(i.concat([[n,o]])),s.items[o],t);if(typeof a=="number")o=a-1;else{if(a===Lu)return Lu;a===gv&&(s.items.splice(o,1),o-=1)}}typeof r=="function"&&n==="key"&&(r=r(e,i))}}return typeof r=="function"?r(e,i):r}yv.visit=ir});var Go=_(vt=>{"use strict";var Bu=pv(),ZC=mv(),QC=bv(),Ru="\uFEFF",Pu="",Mu="",Du="",XC=i=>!!i&&"items"in i,eT=i=>!!i&&(i.type==="scalar"||i.type==="single-quoted-scalar"||i.type==="double-quoted-scalar"||i.type==="block-scalar");function tT(i){switch(i){case Ru:return"<BOM>";case Pu:return"<DOC>";case Mu:return"<FLOW_END>";case Du:return"<SCALAR>";default:return JSON.stringify(i)}}function iT(i){switch(i){case Ru:return"byte-order-mark";case Pu:return"doc-mode";case Mu:return"flow-error-end";case Du:return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case`
`:case`\r
`:return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(i[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}vt.createScalarToken=Bu.createScalarToken;vt.resolveAsScalar=Bu.resolveAsScalar;vt.setScalarValue=Bu.setScalarValue;vt.stringify=ZC.stringify;vt.visit=QC.visit;vt.BOM=Ru;vt.DOCUMENT=Pu;vt.FLOW_END=Mu;vt.SCALAR=Du;vt.isCollection=XC;vt.isScalar=eT;vt.prettyToken=tT;vt.tokenType=iT});var ju=_(wv=>{"use strict";var jn=Go();function Ut(i){switch(i){case void 0:case" ":case`
`:case"\r":case"	":return!0;default:return!1}}var _v=new Set("0123456789ABCDEFabcdef"),rT=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),Wo=new Set(",[]{}"),nT=new Set(` ,[]{}
\r	`),Fu=i=>!i||nT.has(i),qu=class{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,t=!1){var n;if(e){if(typeof e!="string")throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!t;let r=(n=this.next)!=null?n:"stream";for(;r&&(t||this.hasChars(1));)r=yield*this.parseNext(r)}atLineEnd(){let e=this.pos,t=this.buffer[e];for(;t===" "||t==="	";)t=this.buffer[++e];return!t||t==="#"||t===`
`?!0:t==="\r"?this.buffer[e+1]===`
`:!1}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let t=this.buffer[e];if(this.indentNext>0){let r=0;for(;t===" ";)t=this.buffer[++r+e];if(t==="\r"){let n=this.buffer[r+e+1];if(n===`
`||!n&&!this.atEnd)return e+r+1}return t===`
`||r>=this.indentNext||!t&&!this.atEnd?e+r:-1}if(t==="-"||t==="."){let r=this.buffer.substr(e,3);if((r==="---"||r==="...")&&Ut(this.buffer[e+3]))return-1}return e}getLine(){let e=this.lineEndPos;return(typeof e!="number"||e!==-1&&e<this.pos)&&(e=this.buffer.indexOf(`
`,this.pos),this.lineEndPos=e),e===-1?this.atEnd?this.buffer.substring(this.pos):null:(this.buffer[e-1]==="\r"&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(e===null)return this.setNext("stream");if(e[0]===jn.BOM&&(yield*this.pushCount(1),e=e.substring(1)),e[0]==="%"){let t=e.length,r=e.indexOf("#");for(;r!==-1;){let s=e[r-1];if(s===" "||s==="	"){t=r-1;break}else r=e.indexOf("#",r+1)}for(;;){let s=e[t-1];if(s===" "||s==="	")t-=1;else break}let n=(yield*this.pushCount(t))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-n),this.pushNewline(),"stream"}if(this.atLineEnd()){let t=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-t),yield*this.pushNewline(),"stream"}return yield jn.DOCUMENT,yield*this.parseLineStart()}*parseLineStart(){let e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if(e==="-"||e==="."){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");let t=this.peek(3);if((t==="---"||t==="...")&&Ut(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,t==="---"?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!Ut(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){let[e,t]=this.peek(2);if(!t&&!this.atEnd)return this.setNext("block-start");if((e==="-"||e==="?"||e===":")&&Ut(t)){let r=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=r,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);let e=this.getLine();if(e===null)return this.setNext("doc");let t=yield*this.pushIndicators();switch(e[t]){case"#":yield*this.pushCount(e.length-t);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(Fu),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return t+=yield*this.parseBlockScalarHeader(),t+=yield*this.pushSpaces(!0),yield*this.pushCount(e.length-t),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,t,r=-1;do e=yield*this.pushNewline(),e>0?(t=yield*this.pushSpaces(!1),this.indentValue=r=t):t=0,t+=yield*this.pushSpaces(!0);while(e+t>0);let n=this.getLine();if(n===null)return this.setNext("flow");if((r!==-1&&r<this.indentNext&&n[0]!=="#"||r===0&&(n.startsWith("---")||n.startsWith("..."))&&Ut(n[3]))&&!(r===this.indentNext-1&&this.flowLevel===1&&(n[0]==="]"||n[0]==="}")))return this.flowLevel=0,yield jn.FLOW_END,yield*this.parseLineStart();let s=0;for(;n[s]===",";)s+=yield*this.pushCount(1),s+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(s+=yield*this.pushIndicators(),n[s]){case void 0:return"flow";case"#":return yield*this.pushCount(n.length-s),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(Fu),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{let o=this.charAt(1);if(this.flowKey||Ut(o)||o===",")return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){let e=this.charAt(0),t=this.buffer.indexOf(e,this.pos+1);if(e==="'")for(;t!==-1&&this.buffer[t+1]==="'";)t=this.buffer.indexOf("'",t+2);else for(;t!==-1;){let s=0;for(;this.buffer[t-1-s]==="\\";)s+=1;if(s%2===0)break;t=this.buffer.indexOf('"',t+1)}let r=this.buffer.substring(0,t),n=r.indexOf(`
`,this.pos);if(n!==-1){for(;n!==-1;){let s=this.continueScalar(n+1);if(s===-1)break;n=r.indexOf(`
`,s)}n!==-1&&(t=n-(r[n-1]==="\r"?2:1))}if(t===-1){if(!this.atEnd)return this.setNext("quoted-scalar");t=this.buffer.length}return yield*this.pushToIndex(t+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){let t=this.buffer[++e];if(t==="+")this.blockScalarKeep=!0;else if(t>"0"&&t<="9")this.blockScalarIndent=Number(t)-1;else if(t!=="-")break}return yield*this.pushUntil(t=>Ut(t)||t==="#")}*parseBlockScalar(){let e=this.pos-1,t=0,r;e:for(let s=this.pos;r=this.buffer[s];++s)switch(r){case" ":t+=1;break;case`
`:e=s,t=0;break;case"\r":{let o=this.buffer[s+1];if(!o&&!this.atEnd)return this.setNext("block-scalar");if(o===`
`)break}default:break e}if(!r&&!this.atEnd)return this.setNext("block-scalar");if(t>=this.indentNext){this.blockScalarIndent===-1?this.indentNext=t:this.indentNext=this.blockScalarIndent+(this.indentNext===0?1:this.indentNext);do{let s=this.continueScalar(e+1);if(s===-1)break;e=this.buffer.indexOf(`
`,s)}while(e!==-1);if(e===-1){if(!this.atEnd)return this.setNext("block-scalar");e=this.buffer.length}}let n=e+1;for(r=this.buffer[n];r===" ";)r=this.buffer[++n];if(r==="	"){for(;r==="	"||r===" "||r==="\r"||r===`
`;)r=this.buffer[++n];e=n-1}else if(!this.blockScalarKeep)do{let s=e-1,o=this.buffer[s];o==="\r"&&(o=this.buffer[--s]);let a=s;for(;o===" ";)o=this.buffer[--s];if(o===`
`&&s>=this.pos&&s+1+t>a)e=s;else break}while(!0);return yield jn.SCALAR,yield*this.pushToIndex(e+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){let e=this.flowLevel>0,t=this.pos-1,r=this.pos-1,n;for(;n=this.buffer[++r];)if(n===":"){let s=this.buffer[r+1];if(Ut(s)||e&&Wo.has(s))break;t=r}else if(Ut(n)){let s=this.buffer[r+1];if(n==="\r"&&(s===`
`?(r+=1,n=`
`,s=this.buffer[r+1]):t=r),s==="#"||e&&Wo.has(s))break;if(n===`
`){let o=this.continueScalar(r+1);if(o===-1)break;r=Math.max(r,o-2)}}else{if(e&&Wo.has(n))break;t=r}return!n&&!this.atEnd?this.setNext("plain-scalar"):(yield jn.SCALAR,yield*this.pushToIndex(t+1,!0),e?"flow":"doc")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,t){let r=this.buffer.slice(this.pos,e);return r?(yield r,this.pos+=r.length,r.length):(t&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(Fu))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{let e=this.flowLevel>0,t=this.charAt(1);if(Ut(t)||e&&Wo.has(t))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if(this.charAt(1)==="<"){let e=this.pos+2,t=this.buffer[e];for(;!Ut(t)&&t!==">";)t=this.buffer[++e];return yield*this.pushToIndex(t===">"?e+1:e,!1)}else{let e=this.pos+1,t=this.buffer[e];for(;t;)if(rT.has(t))t=this.buffer[++e];else if(t==="%"&&_v.has(this.buffer[e+1])&&_v.has(this.buffer[e+2]))t=this.buffer[e+=3];else break;return yield*this.pushToIndex(e,!1)}}*pushNewline(){let e=this.buffer[this.pos];return e===`
`?yield*this.pushCount(1):e==="\r"&&this.charAt(1)===`
`?yield*this.pushCount(2):0}*pushSpaces(e){let t=this.pos-1,r;do r=this.buffer[++t];while(r===" "||e&&r==="	");let n=t-this.pos;return n>0&&(yield this.buffer.substr(this.pos,n),this.pos=t),n}*pushUntil(e){let t=this.pos,r=this.buffer[t];for(;!e(r);)r=this.buffer[++t];return yield*this.pushToIndex(t,!1)}};wv.Lexer=qu});var $u=_(xv=>{"use strict";var Uu=class{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,r=this.lineStarts.length;for(;t<r;){let s=t+r>>1;this.lineStarts[s]<e?t=s+1:r=s}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(t===0)return{line:0,col:e};let n=this.lineStarts[t-1];return{line:t,col:e-n+1}}}};xv.LineCounter=Uu});var Hu=_(Cv=>{"use strict";var Sv=Go(),sT=ju();function rr(i,e){for(let t=0;t<i.length;++t)if(i[t].type===e)return!0;return!1}function Ev(i){for(let e=0;e<i.length;++e)switch(i[e].type){case"space":case"comment":case"newline":break;default:return e}return-1}function kv(i){switch(i==null?void 0:i.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Yo(i){var e;switch(i.type){case"document":return i.start;case"block-map":{let t=i.items[i.items.length-1];return(e=t.sep)!=null?e:t.start}case"block-seq":return i.items[i.items.length-1].start;default:return[]}}function Hr(i){var t;if(i.length===0)return[];let e=i.length;e:for(;--e>=0;)switch(i[e].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;((t=i[++e])==null?void 0:t.type)==="space";);return i.splice(e,i.length)}function Ov(i){if(i.start.type==="flow-seq-start")for(let e of i.items)e.sep&&!e.value&&!rr(e.start,"explicit-key-ind")&&!rr(e.sep,"map-value-ind")&&(e.key&&(e.value=e.key),delete e.key,kv(e.value)?e.value.end?Array.prototype.push.apply(e.value.end,e.sep):e.value.end=e.sep:Array.prototype.push.apply(e.start,e.sep),delete e.sep)}var Vu=class{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new sT.Lexer,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&this.offset===0&&this.onNewLine(0);for(let r of this.lexer.lex(e,t))yield*this.next(r);t||(yield*this.end())}*next(e){if(this.source=e,process.env.LOG_TOKENS&&console.log("|",Sv.prettyToken(e)),this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=e.length;return}let t=Sv.tokenType(e);if(t)if(t==="scalar")this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&e[0]===" "&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{let r=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:r,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){let e=this.peek(1);if(this.type==="doc-end"&&(!e||e.type!=="doc-end")){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}peek(e){return this.stack[this.stack.length-e]}*pop(e){let t=e!=null?e:this.stack.pop();if(!t)yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"};else if(this.stack.length===0)yield t;else{let r=this.peek(1);switch(t.type==="block-scalar"?t.indent="indent"in r?r.indent:0:t.type==="flow-collection"&&r.type==="document"&&(t.indent=0),t.type==="flow-collection"&&Ov(t),r.type){case"document":r.value=t;break;case"block-scalar":r.props.push(t);break;case"block-map":{let n=r.items[r.items.length-1];if(n.value){r.items.push({start:[],key:t,sep:[]}),this.onKeyLine=!0;return}else if(n.sep)n.value=t;else{Object.assign(n,{key:t,sep:[]}),this.onKeyLine=!n.explicitKey;return}break}case"block-seq":{let n=r.items[r.items.length-1];n.value?r.items.push({start:[],value:t}):n.value=t;break}case"flow-collection":{let n=r.items[r.items.length-1];!n||n.value?r.items.push({start:[],key:t,sep:[]}):n.sep?n.value=t:Object.assign(n,{key:t,sep:[]});return}default:yield*this.pop(),yield*this.pop(t)}if((r.type==="document"||r.type==="block-map"||r.type==="block-seq")&&(t.type==="block-map"||t.type==="block-seq")){let n=t.items[t.items.length-1];n&&!n.sep&&!n.value&&n.start.length>0&&Ev(n.start)===-1&&(t.indent===0||n.start.every(s=>s.type!=="comment"||s.indent<t.indent))&&(r.type==="document"?r.end=n.start:r.items.push({start:n.start}),t.items.splice(-1,1))}}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{let e={type:"document",offset:this.offset,start:[]};this.type==="doc-start"&&e.start.push(this.sourceToken),this.stack.push(e);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":{Ev(e.start)!==-1?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken);return}case"anchor":case"tag":case"space":case"comment":case"newline":e.start.push(this.sourceToken);return}let t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if(this.type==="map-value-ind"){let t=Yo(this.peek(2)),r=Hr(t),n;e.end?(n=e.end,n.push(this.sourceToken),delete e.end):n=[this.sourceToken];let s={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:r,key:e,sep:n}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=s}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":e.props.push(this.sourceToken);return;case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){var r;let t=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,t.value){let n="end"in t.value?t.value.end:void 0,s=Array.isArray(n)?n[n.length-1]:void 0;(s==null?void 0:s.type)==="comment"?n==null||n.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else if(t.sep)t.sep.push(this.sourceToken);else{if(this.atIndentedComment(t.start,e.indent)){let n=e.items[e.items.length-2],s=(r=n==null?void 0:n.value)==null?void 0:r.end;if(Array.isArray(s)){Array.prototype.push.apply(s,t.start),s.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return}if(this.indent>=e.indent){let n=!this.onKeyLine&&this.indent===e.indent,s=n&&(t.sep||t.explicitKey)&&this.type!=="seq-item-ind",o=[];if(s&&t.sep&&!t.value){let a=[];for(let l=0;l<t.sep.length;++l){let c=t.sep[l];switch(c.type){case"newline":a.push(l);break;case"space":break;case"comment":c.indent>e.indent&&(a.length=0);break;default:a.length=0}}a.length>=2&&(o=t.sep.splice(a[1]))}switch(this.type){case"anchor":case"tag":s||t.value?(o.push(this.sourceToken),e.items.push({start:o}),this.onKeyLine=!0):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"explicit-key-ind":!t.sep&&!t.explicitKey?(t.start.push(this.sourceToken),t.explicitKey=!0):s||t.value?(o.push(this.sourceToken),e.items.push({start:o,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}),this.onKeyLine=!0;return;case"map-value-ind":if(t.explicitKey)if(t.sep)if(t.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(rr(t.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:null,sep:[this.sourceToken]}]});else if(kv(t.key)&&!rr(t.sep,"newline")){let a=Hr(t.start),l=t.key,c=t.sep;c.push(this.sourceToken),delete t.key,delete t.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:a,key:l,sep:c}]})}else o.length>0?t.sep=t.sep.concat(o,this.sourceToken):t.sep.push(this.sourceToken);else if(rr(t.start,"newline"))Object.assign(t,{key:null,sep:[this.sourceToken]});else{let a=Hr(t.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:a,key:null,sep:[this.sourceToken]}]})}else t.sep?t.value||s?e.items.push({start:o,key:null,sep:[this.sourceToken]}):rr(t.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let a=this.flowScalar(this.type);s||t.value?(e.items.push({start:o,key:a,sep:[]}),this.onKeyLine=!0):t.sep?this.stack.push(a):(Object.assign(t,{key:a,sep:[]}),this.onKeyLine=!0);return}default:{let a=this.startBlockValue(e);if(a){n&&a.type!=="block-seq"&&e.items.push({start:o}),this.stack.push(a);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(e){var r;let t=e.items[e.items.length-1];switch(this.type){case"newline":if(t.value){let n="end"in t.value?t.value.end:void 0,s=Array.isArray(n)?n[n.length-1]:void 0;(s==null?void 0:s.type)==="comment"?n==null||n.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(t.start,e.indent)){let n=e.items[e.items.length-2],s=(r=n==null?void 0:n.value)==null?void 0:r.end;if(Array.isArray(s)){Array.prototype.push.apply(s,t.start),s.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return;case"anchor":case"tag":if(t.value||this.indent<=e.indent)break;t.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==e.indent)break;t.value||rr(t.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return}if(this.indent>e.indent){let n=this.startBlockValue(e);if(n){this.stack.push(n);return}}yield*this.pop(),yield*this.step()}*flowCollection(e){let t=e.items[e.items.length-1];if(this.type==="flow-error-end"){let r;do yield*this.pop(),r=this.peek(1);while(r&&r.type==="flow-collection")}else if(e.end.length===0){switch(this.type){case"comma":case"explicit-key-ind":!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return;case"map-value-ind":!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let n=this.flowScalar(this.type);!t||t.value?e.items.push({start:[],key:n,sep:[]}):t.sep?this.stack.push(n):Object.assign(t,{key:n,sep:[]});return}case"flow-map-end":case"flow-seq-end":e.end.push(this.sourceToken);return}let r=this.startBlockValue(e);r?this.stack.push(r):(yield*this.pop(),yield*this.step())}else{let r=this.peek(2);if(r.type==="block-map"&&(this.type==="map-value-ind"&&r.indent===e.indent||this.type==="newline"&&!r.items[r.items.length-1].sep))yield*this.pop(),yield*this.step();else if(this.type==="map-value-ind"&&r.type!=="flow-collection"){let n=Yo(r),s=Hr(n);Ov(e);let o=e.end.splice(1,e.end.length);o.push(this.sourceToken);let a={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:s,key:e,sep:o}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=a}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let t=this.source.indexOf(`
`)+1;for(;t!==0;)this.onNewLine(this.offset+t),t=this.source.indexOf(`
`,t)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;let t=Yo(e),r=Hr(t);return r.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;let t=Yo(e),r=Hr(t);return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return this.type!=="comment"||this.indent<=t?!1:e.every(r=>r.type==="newline"||r.type==="space")}*documentEnd(e){this.type!=="doc-mode"&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;case"space":case"comment":default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],this.type==="newline"&&(yield*this.pop())}}};Cv.Parser=Vu});var Lv=_($n=>{"use strict";var Tv=Iu(),oT=Bn(),Un=Mn(),aT=Tc(),lT=Se(),cT=$u(),Av=Hu();function Iv(i){let e=i.prettyErrors!==!1;return{lineCounter:i.lineCounter||e&&new cT.LineCounter||null,prettyErrors:e}}function uT(i,e={}){let{lineCounter:t,prettyErrors:r}=Iv(e),n=new Av.Parser(t==null?void 0:t.addNewLine),s=new Tv.Composer(e),o=Array.from(s.compose(n.parse(i)));if(r&&t)for(let a of o)a.errors.forEach(Un.prettifyError(i,t)),a.warnings.forEach(Un.prettifyError(i,t));return o.length>0?o:Object.assign([],{empty:!0},s.streamInfo())}function Nv(i,e={}){let{lineCounter:t,prettyErrors:r}=Iv(e),n=new Av.Parser(t==null?void 0:t.addNewLine),s=new Tv.Composer(e),o=null;for(let a of s.compose(n.parse(i),!0,i.length))if(!o)o=a;else if(o.options.logLevel!=="silent"){o.errors.push(new Un.YAMLParseError(a.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}return r&&t&&(o.errors.forEach(Un.prettifyError(i,t)),o.warnings.forEach(Un.prettifyError(i,t))),o}function fT(i,e,t){let r;typeof e=="function"?r=e:t===void 0&&e&&typeof e=="object"&&(t=e);let n=Nv(i,t);if(!n)return null;if(n.warnings.forEach(s=>aT.warn(n.options.logLevel,s)),n.errors.length>0){if(n.options.logLevel!=="silent")throw n.errors[0];n.errors=[]}return n.toJS(Object.assign({reviver:r},t))}function hT(i,e,t){var n;let r=null;if(typeof e=="function"||Array.isArray(e)?r=e:t===void 0&&e&&(t=e),typeof t=="string"&&(t=t.length),typeof t=="number"){let s=Math.round(t);t=s<1?void 0:s>8?{indent:8}:{indent:s}}if(i===void 0){let{keepUndefined:s}=(n=t!=null?t:e)!=null?n:{};if(!s)return}return lT.isDocument(i)&&!r?i.toString(t):new oT.Document(i,r,t).toString(t)}$n.parse=fT;$n.parseAllDocuments=uT;$n.parseDocument=Nv;$n.stringify=hT});var Rv=_(Ce=>{"use strict";var pT=Iu(),dT=Bn(),mT=uu(),Gu=Mn(),gT=vn(),Ci=Se(),vT=Si(),yT=je(),bT=Oi(),_T=ki(),wT=Go(),xT=ju(),ST=$u(),ET=Hu(),Ko=Lv(),Bv=pn();Ce.Composer=pT.Composer;Ce.Document=dT.Document;Ce.Schema=mT.Schema;Ce.YAMLError=Gu.YAMLError;Ce.YAMLParseError=Gu.YAMLParseError;Ce.YAMLWarning=Gu.YAMLWarning;Ce.Alias=gT.Alias;Ce.isAlias=Ci.isAlias;Ce.isCollection=Ci.isCollection;Ce.isDocument=Ci.isDocument;Ce.isMap=Ci.isMap;Ce.isNode=Ci.isNode;Ce.isPair=Ci.isPair;Ce.isScalar=Ci.isScalar;Ce.isSeq=Ci.isSeq;Ce.Pair=vT.Pair;Ce.Scalar=yT.Scalar;Ce.YAMLMap=bT.YAMLMap;Ce.YAMLSeq=_T.YAMLSeq;Ce.CST=wT;Ce.Lexer=xT.Lexer;Ce.LineCounter=ST.LineCounter;Ce.Parser=ET.Parser;Ce.parse=Ko.parse;Ce.parseAllDocuments=Ko.parseAllDocuments;Ce.parseDocument=Ko.parseDocument;Ce.stringify=Ko.stringify;Ce.visit=Bv.visit;Ce.visitAsync=Bv.visitAsync});var Fv=_((kL,Dv)=>{"use strict";var{Duplex:OT}=require("stream");function Pv(i){i.emit("close")}function kT(){!this.destroyed&&this._writableState.finished&&this.destroy()}function Mv(i){this.removeListener("error",Mv),this.destroy(),this.listenerCount("error")===0&&this.emit("error",i)}function CT(i,e){let t=!0,r=new OT({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return i.on("message",function(s,o){let a=!o&&r._readableState.objectMode?s.toString():s;r.push(a)||i.pause()}),i.once("error",function(s){r.destroyed||(t=!1,r.destroy(s))}),i.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(n,s){if(i.readyState===i.CLOSED){s(n),process.nextTick(Pv,r);return}let o=!1;i.once("error",function(l){o=!0,s(l)}),i.once("close",function(){o||s(n),process.nextTick(Pv,r)}),t&&i.terminate()},r._final=function(n){if(i.readyState===i.CONNECTING){i.once("open",function(){r._final(n)});return}i._socket!==null&&(i._socket._writableState.finished?(n(),r._readableState.endEmitted&&r.destroy()):(i._socket.once("finish",function(){n()}),i.close()))},r._read=function(){i.isPaused&&i.resume()},r._write=function(n,s,o){if(i.readyState===i.CONNECTING){i.once("open",function(){r._write(n,s,o)});return}i.send(n,o)},r.on("end",kT),r.on("error",Mv),r}Dv.exports=CT});var Ti=_((CL,qv)=>{"use strict";qv.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var Vn=_((TL,zo)=>{"use strict";var{EMPTY_BUFFER:TT}=Ti(),Wu=Buffer[Symbol.species];function AT(i,e){if(i.length===0)return TT;if(i.length===1)return i[0];let t=Buffer.allocUnsafe(e),r=0;for(let n=0;n<i.length;n++){let s=i[n];t.set(s,r),r+=s.length}return r<e?new Wu(t.buffer,t.byteOffset,r):t}function jv(i,e,t,r,n){for(let s=0;s<n;s++)t[r+s]=i[s]^e[s&3]}function Uv(i,e){for(let t=0;t<i.length;t++)i[t]^=e[t&3]}function IT(i){return i.length===i.buffer.byteLength?i.buffer:i.buffer.slice(i.byteOffset,i.byteOffset+i.length)}function Yu(i){if(Yu.readOnly=!0,Buffer.isBuffer(i))return i;let e;return i instanceof ArrayBuffer?e=new Wu(i):ArrayBuffer.isView(i)?e=new Wu(i.buffer,i.byteOffset,i.byteLength):(e=Buffer.from(i),Yu.readOnly=!1),e}zo.exports={concat:AT,mask:jv,toArrayBuffer:IT,toBuffer:Yu,unmask:Uv};if(!process.env.WS_NO_BUFFER_UTIL)try{let i=require("bufferutil");zo.exports.mask=function(e,t,r,n,s){s<48?jv(e,t,r,n,s):i.mask(e,t,r,n,s)},zo.exports.unmask=function(e,t){e.length<32?Uv(e,t):i.unmask(e,t)}}catch{}});var Hv=_((AL,Vv)=>{"use strict";var $v=Symbol("kDone"),Ku=Symbol("kRun"),zu=class{constructor(e){this[$v]=()=>{this.pending--,this[Ku]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Ku]()}[Ku](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[$v])}}};Vv.exports=zu});var Wn=_((IL,Kv)=>{"use strict";var Hn=require("zlib"),Gv=Vn(),NT=Hv(),{kStatusCode:Wv}=Ti(),LT=Buffer[Symbol.species],BT=Buffer.from([0,0,255,255]),Qo=Symbol("permessage-deflate"),ci=Symbol("total-length"),Gn=Symbol("callback"),Ai=Symbol("buffers"),Zo=Symbol("error"),Jo,Ju=class{constructor(e,t,r){if(this._maxPayload=r|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,!Jo){let n=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;Jo=new NT(n)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[Gn];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(n=>!(t.serverNoContextTakeover===!1&&n.server_no_context_takeover||n.server_max_window_bits&&(t.serverMaxWindowBits===!1||typeof t.serverMaxWindowBits=="number"&&t.serverMaxWindowBits>n.server_max_window_bits)||typeof t.clientMaxWindowBits=="number"&&!n.client_max_window_bits));if(!r)throw new Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),typeof t.serverMaxWindowBits=="number"&&(r.server_max_window_bits=t.serverMaxWindowBits),typeof t.clientMaxWindowBits=="number"?r.client_max_window_bits=t.clientMaxWindowBits:(r.client_max_window_bits===!0||t.clientMaxWindowBits===!1)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(this._options.clientNoContextTakeover===!1&&t.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!t.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(t.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return t}normalizeParams(e){return e.forEach(t=>{Object.keys(t).forEach(r=>{let n=t[r];if(n.length>1)throw new Error(`Parameter "${r}" must have only a single value`);if(n=n[0],r==="client_max_window_bits"){if(n!==!0){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else if(r==="server_max_window_bits"){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(r==="client_no_context_takeover"||r==="server_no_context_takeover"){if(n!==!0)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else throw new Error(`Unknown parameter "${r}"`);t[r]=n})}),e}decompress(e,t,r){Jo.add(n=>{this._decompress(e,t,(s,o)=>{n(),r(s,o)})})}compress(e,t,r){Jo.add(n=>{this._compress(e,t,(s,o)=>{n(),r(s,o)})})}_decompress(e,t,r){let n=this._isServer?"client":"server";if(!this._inflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Hn.Z_DEFAULT_WINDOWBITS:this.params[s];this._inflate=Hn.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[Qo]=this,this._inflate[ci]=0,this._inflate[Ai]=[],this._inflate.on("error",PT),this._inflate.on("data",Yv)}this._inflate[Gn]=r,this._inflate.write(e),t&&this._inflate.write(BT),this._inflate.flush(()=>{let s=this._inflate[Zo];if(s){this._inflate.close(),this._inflate=null,r(s);return}let o=Gv.concat(this._inflate[Ai],this._inflate[ci]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[ci]=0,this._inflate[Ai]=[],t&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,o)})}_compress(e,t,r){let n=this._isServer?"server":"client";if(!this._deflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Hn.Z_DEFAULT_WINDOWBITS:this.params[s];this._deflate=Hn.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[ci]=0,this._deflate[Ai]=[],this._deflate.on("data",RT)}this._deflate[Gn]=r,this._deflate.write(e),this._deflate.flush(Hn.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let s=Gv.concat(this._deflate[Ai],this._deflate[ci]);t&&(s=new LT(s.buffer,s.byteOffset,s.length-4)),this._deflate[Gn]=null,this._deflate[ci]=0,this._deflate[Ai]=[],t&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,s)})}};Kv.exports=Ju;function RT(i){this[Ai].push(i),this[ci]+=i.length}function Yv(i){if(this[ci]+=i.length,this[Qo]._maxPayload<1||this[ci]<=this[Qo]._maxPayload){this[Ai].push(i);return}this[Zo]=new RangeError("Max payload size exceeded"),this[Zo].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[Zo][Wv]=1009,this.removeListener("data",Yv),this.reset()}function PT(i){this[Qo]._inflate=null,i[Wv]=1007,this[Gn](i)}});var Yn=_((NL,Xo)=>{"use strict";var{isUtf8:zv}=require("buffer"),MT=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function DT(i){return i>=1e3&&i<=1014&&i!==1004&&i!==1005&&i!==1006||i>=3e3&&i<=4999}function Zu(i){let e=i.length,t=0;for(;t<e;)if((i[t]&128)===0)t++;else if((i[t]&224)===192){if(t+1===e||(i[t+1]&192)!==128||(i[t]&254)===192)return!1;t+=2}else if((i[t]&240)===224){if(t+2>=e||(i[t+1]&192)!==128||(i[t+2]&192)!==128||i[t]===224&&(i[t+1]&224)===128||i[t]===237&&(i[t+1]&224)===160)return!1;t+=3}else if((i[t]&248)===240){if(t+3>=e||(i[t+1]&192)!==128||(i[t+2]&192)!==128||(i[t+3]&192)!==128||i[t]===240&&(i[t+1]&240)===128||i[t]===244&&i[t+1]>143||i[t]>244)return!1;t+=4}else return!1;return!0}Xo.exports={isValidStatusCode:DT,isValidUTF8:Zu,tokenChars:MT};if(zv)Xo.exports.isValidUTF8=function(i){return i.length<24?Zu(i):zv(i)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let i=require("utf-8-validate");Xo.exports.isValidUTF8=function(e){return e.length<32?Zu(e):i(e)}}catch{}});var rf=_((LL,iy)=>{"use strict";var{Writable:FT}=require("stream"),Jv=Wn(),{BINARY_TYPES:qT,EMPTY_BUFFER:Zv,kStatusCode:jT,kWebSocket:UT}=Ti(),{concat:Qu,toArrayBuffer:$T,unmask:VT}=Vn(),{isValidStatusCode:HT,isValidUTF8:Qv}=Yn(),ea=Buffer[Symbol.species],Bt=0,Xv=1,ey=2,ty=3,Xu=4,ef=5,ta=6,tf=class extends FT{constructor(e={}){super(),this._allowSynchronousEvents=e.allowSynchronousEvents!==void 0?e.allowSynchronousEvents:!0,this._binaryType=e.binaryType||qT[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[UT]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=Bt}_write(e,t,r){if(this._opcode===8&&this._state==Bt)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let r=this._buffers[0];return this._buffers[0]=new ea(r.buffer,r.byteOffset+e,r.length-e),new ea(r.buffer,r.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=t.length-e;e>=r.length?t.set(this._buffers.shift(),n):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=new ea(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case Bt:this.getInfo(e);break;case Xv:this.getPayloadLength16(e);break;case ey:this.getPayloadLength64(e);break;case ty:this.getMask();break;case Xu:this.getData(e);break;case ef:case ta:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((t[0]&48)!==0){let n=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");e(n);return}let r=(t[0]&64)===64;if(r&&!this._extensions[Jv.extensionName]){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(this._fin=(t[0]&128)===128,this._opcode=t[0]&15,this._payloadLength=t[1]&127,this._opcode===0){if(r){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(!this._fragmented){let n=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let n=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}this._compressed=r}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let n=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");e(n);return}if(r){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let n=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");e(n);return}}else{let n=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(t[1]&128)===128,this._isServer){if(!this._masked){let n=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");e(n);return}}else if(this._masked){let n=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");e(n);return}this._payloadLength===126?this._state=Xv:this._payloadLength===127?this._state=ey:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>Math.pow(2,21)-1){let n=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");e(n);return}this._payloadLength=r*Math.pow(2,32)+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){let t=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");e(t);return}this._masked?this._state=ty:this._state=Xu}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Xu}getData(e){let t=Zv;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0&&VT(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=ef,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[Jv.extensionName].decompress(e,this._fin,(n,s)=>{if(n)return t(n);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let o=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");t(o);return}this._fragments.push(s)}this.dataMessage(t),this._state===Bt&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=Bt;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let n;this._binaryType==="nodebuffer"?n=Qu(r,t):this._binaryType==="arraybuffer"?n=$T(Qu(r,t)):n=r,this._allowSynchronousEvents?(this.emit("message",n,!0),this._state=Bt):(this._state=ta,setImmediate(()=>{this.emit("message",n,!0),this._state=Bt,this.startLoop(e)}))}else{let n=Qu(r,t);if(!this._skipUTF8Validation&&!Qv(n)){let s=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");e(s);return}this._state===ef||this._allowSynchronousEvents?(this.emit("message",n,!1),this._state=Bt):(this._state=ta,setImmediate(()=>{this.emit("message",n,!1),this._state=Bt,this.startLoop(e)}))}}controlMessage(e,t){if(this._opcode===8){if(e.length===0)this._loop=!1,this.emit("conclude",1005,Zv),this.end();else{let r=e.readUInt16BE(0);if(!HT(r)){let s=this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");t(s);return}let n=new ea(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!Qv(n)){let s=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");t(s);return}this._loop=!1,this.emit("conclude",r,n),this.end()}this._state=Bt;return}this._allowSynchronousEvents?(this.emit(this._opcode===9?"ping":"pong",e),this._state=Bt):(this._state=ta,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",e),this._state=Bt,this.startLoop(t)}))}createError(e,t,r,n,s){this._loop=!1,this._errored=!0;let o=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(o,this.createError),o.code=s,o[jT]=n,o}};iy.exports=tf});var sf=_((RL,sy)=>{"use strict";var{Duplex:BL}=require("stream"),{randomFillSync:GT}=require("crypto"),ry=Wn(),{EMPTY_BUFFER:WT}=Ti(),{isValidStatusCode:YT}=Yn(),{mask:ny,toBuffer:Gr}=Vn(),$t=Symbol("kByteLength"),KT=Buffer.alloc(4),ia=8*1024,nr,Wr=ia,nf=class i{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let r,n=!1,s=2,o=!1;t.mask&&(r=t.maskBuffer||KT,t.generateMask?t.generateMask(r):(Wr===ia&&(nr===void 0&&(nr=Buffer.alloc(ia)),GT(nr,0,ia),Wr=0),r[0]=nr[Wr++],r[1]=nr[Wr++],r[2]=nr[Wr++],r[3]=nr[Wr++]),o=(r[0]|r[1]|r[2]|r[3])===0,s=6);let a;typeof e=="string"?(!t.mask||o)&&t[$t]!==void 0?a=t[$t]:(e=Buffer.from(e),a=e.length):(a=e.length,n=t.mask&&t.readOnly&&!o);let l=a;a>=65536?(s+=8,l=127):a>125&&(s+=2,l=126);let c=Buffer.allocUnsafe(n?a+s:s);return c[0]=t.fin?t.opcode|128:t.opcode,t.rsv1&&(c[0]|=64),c[1]=l,l===126?c.writeUInt16BE(a,2):l===127&&(c[2]=c[3]=0,c.writeUIntBE(a,4,6)),t.mask?(c[1]|=128,c[s-4]=r[0],c[s-3]=r[1],c[s-2]=r[2],c[s-1]=r[3],o?[c,e]:n?(ny(e,r,c,s,a),[c]):(ny(e,r,e,0,a),[c,e])):[c,e]}close(e,t,r,n){let s;if(e===void 0)s=WT;else{if(typeof e!="number"||!YT(e))throw new TypeError("First argument must be a valid error code number");if(t===void 0||!t.length)s=Buffer.allocUnsafe(2),s.writeUInt16BE(e,0);else{let a=Buffer.byteLength(t);if(a>123)throw new RangeError("The message must not be greater than 123 bytes");s=Buffer.allocUnsafe(2+a),s.writeUInt16BE(e,0),typeof t=="string"?s.write(t,2):s.set(t,2)}}let o={[$t]:s.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,o,n]):this.sendFrame(i.frame(s,o),n)}ping(e,t,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=Gr(e),n=e.length,s=Gr.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[$t]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(i.frame(e,o),r)}pong(e,t,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=Gr(e),n=e.length,s=Gr.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[$t]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(i.frame(e,o),r)}send(e,t,r){let n=this._extensions[ry.extensionName],s=t.binary?2:1,o=t.compress,a,l;if(typeof e=="string"?(a=Buffer.byteLength(e),l=!1):(e=Gr(e),a=e.length,l=Gr.readOnly),this._firstFragment?(this._firstFragment=!1,o&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=a>=n._threshold),this._compress=o):(o=!1,s=0),t.fin&&(this._firstFragment=!0),n){let c={[$t]:a,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:l,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,c,r]):this.dispatch(e,this._compress,c,r)}else this.sendFrame(i.frame(e,{[$t]:a,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:l,rsv1:!1}),r)}dispatch(e,t,r,n){if(!t){this.sendFrame(i.frame(e,r),n);return}let s=this._extensions[ry.extensionName];this._bufferedBytes+=r[$t],this._deflating=!0,s.compress(e,r.fin,(o,a)=>{if(this._socket.destroyed){let l=new Error("The socket was closed while data was being compressed");typeof n=="function"&&n(l);for(let c=0;c<this._queue.length;c++){let u=this._queue[c],f=u[u.length-1];typeof f=="function"&&f(l)}return}this._bufferedBytes-=r[$t],this._deflating=!1,r.readOnly=!1,this.sendFrame(i.frame(a,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][$t],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][$t],this._queue.push(e)}sendFrame(e,t){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}};sy.exports=nf});var dy=_((PL,py)=>{"use strict";var{kForOnEventAttribute:Kn,kListener:of}=Ti(),oy=Symbol("kCode"),ay=Symbol("kData"),ly=Symbol("kError"),cy=Symbol("kMessage"),uy=Symbol("kReason"),Yr=Symbol("kTarget"),fy=Symbol("kType"),hy=Symbol("kWasClean"),ui=class{constructor(e){this[Yr]=null,this[fy]=e}get target(){return this[Yr]}get type(){return this[fy]}};Object.defineProperty(ui.prototype,"target",{enumerable:!0});Object.defineProperty(ui.prototype,"type",{enumerable:!0});var sr=class extends ui{constructor(e,t={}){super(e),this[oy]=t.code===void 0?0:t.code,this[uy]=t.reason===void 0?"":t.reason,this[hy]=t.wasClean===void 0?!1:t.wasClean}get code(){return this[oy]}get reason(){return this[uy]}get wasClean(){return this[hy]}};Object.defineProperty(sr.prototype,"code",{enumerable:!0});Object.defineProperty(sr.prototype,"reason",{enumerable:!0});Object.defineProperty(sr.prototype,"wasClean",{enumerable:!0});var Kr=class extends ui{constructor(e,t={}){super(e),this[ly]=t.error===void 0?null:t.error,this[cy]=t.message===void 0?"":t.message}get error(){return this[ly]}get message(){return this[cy]}};Object.defineProperty(Kr.prototype,"error",{enumerable:!0});Object.defineProperty(Kr.prototype,"message",{enumerable:!0});var zn=class extends ui{constructor(e,t={}){super(e),this[ay]=t.data===void 0?null:t.data}get data(){return this[ay]}};Object.defineProperty(zn.prototype,"data",{enumerable:!0});var zT={addEventListener(i,e,t={}){for(let n of this.listeners(i))if(!t[Kn]&&n[of]===e&&!n[Kn])return;let r;if(i==="message")r=function(s,o){let a=new zn("message",{data:o?s:s.toString()});a[Yr]=this,ra(e,this,a)};else if(i==="close")r=function(s,o){let a=new sr("close",{code:s,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});a[Yr]=this,ra(e,this,a)};else if(i==="error")r=function(s){let o=new Kr("error",{error:s,message:s.message});o[Yr]=this,ra(e,this,o)};else if(i==="open")r=function(){let s=new ui("open");s[Yr]=this,ra(e,this,s)};else return;r[Kn]=!!t[Kn],r[of]=e,t.once?this.once(i,r):this.on(i,r)},removeEventListener(i,e){for(let t of this.listeners(i))if(t[of]===e&&!t[Kn]){this.removeListener(i,t);break}}};py.exports={CloseEvent:sr,ErrorEvent:Kr,Event:ui,EventTarget:zT,MessageEvent:zn};function ra(i,e,t){typeof i=="object"&&i.handleEvent?i.handleEvent.call(i,t):i.call(e,t)}});var af=_((ML,my)=>{"use strict";var{tokenChars:Jn}=Yn();function Qt(i,e,t){i[e]===void 0?i[e]=[t]:i[e].push(t)}function JT(i){let e=Object.create(null),t=Object.create(null),r=!1,n=!1,s=!1,o,a,l=-1,c=-1,u=-1,f=0;for(;f<i.length;f++)if(c=i.charCodeAt(f),o===void 0)if(u===-1&&Jn[c]===1)l===-1&&(l=f);else if(f!==0&&(c===32||c===9))u===-1&&l!==-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let m=i.slice(l,u);c===44?(Qt(e,m,t),t=Object.create(null)):o=m,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);else if(a===void 0)if(u===-1&&Jn[c]===1)l===-1&&(l=f);else if(c===32||c===9)u===-1&&l!==-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f),Qt(t,i.slice(l,u),!0),c===44&&(Qt(e,o,t),t=Object.create(null),o=void 0),l=u=-1}else if(c===61&&l!==-1&&u===-1)a=i.slice(l,f),l=u=-1;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(n){if(Jn[c]!==1)throw new SyntaxError(`Unexpected character at index ${f}`);l===-1?l=f:r||(r=!0),n=!1}else if(s)if(Jn[c]===1)l===-1&&(l=f);else if(c===34&&l!==-1)s=!1,u=f;else if(c===92)n=!0;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(c===34&&i.charCodeAt(f-1)===61)s=!0;else if(u===-1&&Jn[c]===1)l===-1&&(l=f);else if(l!==-1&&(c===32||c===9))u===-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let m=i.slice(l,u);r&&(m=m.replace(/\\/g,""),r=!1),Qt(t,a,m),c===44&&(Qt(e,o,t),t=Object.create(null),o=void 0),a=void 0,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);if(l===-1||s||c===32||c===9)throw new SyntaxError("Unexpected end of input");u===-1&&(u=f);let d=i.slice(l,u);return o===void 0?Qt(e,d,t):(a===void 0?Qt(t,d,!0):r?Qt(t,a,d.replace(/\\/g,"")):Qt(t,a,d),Qt(e,o,t)),e}function ZT(i){return Object.keys(i).map(e=>{let t=i[e];return Array.isArray(t)||(t=[t]),t.map(r=>[e].concat(Object.keys(r).map(n=>{let s=r[n];return Array.isArray(s)||(s=[s]),s.map(o=>o===!0?n:`${n}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}my.exports={format:ZT,parse:JT}});var hf=_((qL,ky)=>{"use strict";var QT=require("events"),XT=require("https"),eA=require("http"),yy=require("net"),tA=require("tls"),{randomBytes:iA,createHash:rA}=require("crypto"),{Duplex:DL,Readable:FL}=require("stream"),{URL:lf}=require("url"),Ii=Wn(),nA=rf(),sA=sf(),{BINARY_TYPES:gy,EMPTY_BUFFER:na,GUID:oA,kForOnEventAttribute:cf,kListener:aA,kStatusCode:lA,kWebSocket:st,NOOP:by}=Ti(),{EventTarget:{addEventListener:cA,removeEventListener:uA}}=dy(),{format:fA,parse:hA}=af(),{toBuffer:pA}=Vn(),dA=30*1e3,_y=Symbol("kAborted"),uf=[8,13],fi=["CONNECTING","OPEN","CLOSING","CLOSED"],mA=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,qe=class i extends QT{constructor(e,t,r){super(),this._binaryType=gy[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=na,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=i.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,t===void 0?t=[]:Array.isArray(t)||(typeof t=="object"&&t!==null?(r=t,t=[]):t=[t]),wy(this,e,t,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){gy.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let n=new nA({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new sA(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[st]=this,e[st]=this,n.on("conclude",yA),n.on("drain",bA),n.on("error",_A),n.on("message",wA),n.on("ping",xA),n.on("pong",SA),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",Sy),e.on("data",oa),e.on("end",Ey),e.on("error",Oy),this._readyState=i.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=i.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[Ii.extensionName]&&this._extensions[Ii.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=i.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==i.CLOSED){if(this.readyState===i.CONNECTING){Et(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===i.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=i.CLOSING,this._sender.close(e,t,!this._isServer,r=>{r||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),dA)}}pause(){this.readyState===i.CONNECTING||this.readyState===i.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===i.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=t=void 0):typeof t=="function"&&(r=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==i.OPEN){ff(this,e,r);return}t===void 0&&(t=!this._isServer),this._sender.ping(e||na,t,r)}pong(e,t,r){if(this.readyState===i.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=t=void 0):typeof t=="function"&&(r=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==i.OPEN){ff(this,e,r);return}t===void 0&&(t=!this._isServer),this._sender.pong(e||na,t,r)}resume(){this.readyState===i.CONNECTING||this.readyState===i.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===i.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof t=="function"&&(r=t,t={}),typeof e=="number"&&(e=e.toString()),this.readyState!==i.OPEN){ff(this,e,r);return}let n={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[Ii.extensionName]||(n.compress=!1),this._sender.send(e||na,n,r)}terminate(){if(this.readyState!==i.CLOSED){if(this.readyState===i.CONNECTING){Et(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=i.CLOSING,this._socket.destroy())}}};Object.defineProperty(qe,"CONNECTING",{enumerable:!0,value:fi.indexOf("CONNECTING")});Object.defineProperty(qe.prototype,"CONNECTING",{enumerable:!0,value:fi.indexOf("CONNECTING")});Object.defineProperty(qe,"OPEN",{enumerable:!0,value:fi.indexOf("OPEN")});Object.defineProperty(qe.prototype,"OPEN",{enumerable:!0,value:fi.indexOf("OPEN")});Object.defineProperty(qe,"CLOSING",{enumerable:!0,value:fi.indexOf("CLOSING")});Object.defineProperty(qe.prototype,"CLOSING",{enumerable:!0,value:fi.indexOf("CLOSING")});Object.defineProperty(qe,"CLOSED",{enumerable:!0,value:fi.indexOf("CLOSED")});Object.defineProperty(qe.prototype,"CLOSED",{enumerable:!0,value:fi.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(i=>{Object.defineProperty(qe.prototype,i,{enumerable:!0})});["open","error","close","message"].forEach(i=>{Object.defineProperty(qe.prototype,`on${i}`,{enumerable:!0,get(){for(let e of this.listeners(i))if(e[cf])return e[aA];return null},set(e){for(let t of this.listeners(i))if(t[cf]){this.removeListener(i,t);break}typeof e=="function"&&this.addEventListener(i,e,{[cf]:!0})}})});qe.prototype.addEventListener=cA;qe.prototype.removeEventListener=uA;ky.exports=qe;function wy(i,e,t,r){let n={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:uf[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...r,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(i._autoPong=n.autoPong,!uf.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${uf.join(", ")})`);let s;if(e instanceof lf)s=e;else try{s=new lf(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}s.protocol==="http:"?s.protocol="ws:":s.protocol==="https:"&&(s.protocol="wss:"),i._url=s.href;let o=s.protocol==="wss:",a=s.protocol==="ws+unix:",l;if(s.protocol!=="ws:"&&!o&&!a?l=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`:a&&!s.pathname?l="The URL's pathname is empty":s.hash&&(l="The URL contains a fragment identifier"),l){let y=new SyntaxError(l);if(i._redirects===0)throw y;sa(i,y);return}let c=o?443:80,u=iA(16).toString("base64"),f=o?XT.request:eA.request,d=new Set,m;if(n.createConnection=n.createConnection||(o?vA:gA),n.defaultPort=n.defaultPort||c,n.port=s.port||c,n.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,n.headers={...n.headers,"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":u,Connection:"Upgrade",Upgrade:"websocket"},n.path=s.pathname+s.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(m=new Ii(n.perMessageDeflate!==!0?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=fA({[Ii.extensionName]:m.offer()})),t.length){for(let y of t){if(typeof y!="string"||!mA.test(y)||d.has(y))throw new SyntaxError("An invalid or duplicated subprotocol was specified");d.add(y)}n.headers["Sec-WebSocket-Protocol"]=t.join(",")}if(n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(s.username||s.password)&&(n.auth=`${s.username}:${s.password}`),a){let y=n.path.split(":");n.socketPath=y[0],n.path=y[1]}let g;if(n.followRedirects){if(i._redirects===0){i._originalIpc=a,i._originalSecure=o,i._originalHostOrSocketPath=a?n.socketPath:s.host;let y=r&&r.headers;if(r={...r,headers:{}},y)for(let[b,x]of Object.entries(y))r.headers[b.toLowerCase()]=x}else if(i.listenerCount("redirect")===0){let y=a?i._originalIpc?n.socketPath===i._originalHostOrSocketPath:!1:i._originalIpc?!1:s.host===i._originalHostOrSocketPath;(!y||i._originalSecure&&!o)&&(delete n.headers.authorization,delete n.headers.cookie,y||delete n.headers.host,n.auth=void 0)}n.auth&&!r.headers.authorization&&(r.headers.authorization="Basic "+Buffer.from(n.auth).toString("base64")),g=i._req=f(n),i._redirects&&i.emit("redirect",i.url,g)}else g=i._req=f(n);n.timeout&&g.on("timeout",()=>{Et(i,g,"Opening handshake has timed out")}),g.on("error",y=>{g===null||g[_y]||(g=i._req=null,sa(i,y))}),g.on("response",y=>{let b=y.headers.location,x=y.statusCode;if(b&&n.followRedirects&&x>=300&&x<400){if(++i._redirects>n.maxRedirects){Et(i,g,"Maximum redirects exceeded");return}g.abort();let E;try{E=new lf(b,e)}catch{let O=new SyntaxError(`Invalid URL: ${b}`);sa(i,O);return}wy(i,E,t,r)}else i.emit("unexpected-response",g,y)||Et(i,g,`Unexpected server response: ${y.statusCode}`)}),g.on("upgrade",(y,b,x)=>{if(i.emit("upgrade",y),i.readyState!==qe.CONNECTING)return;g=i._req=null;let E=y.headers.upgrade;if(E===void 0||E.toLowerCase()!=="websocket"){Et(i,b,"Invalid Upgrade header");return}let k=rA("sha1").update(u+oA).digest("base64");if(y.headers["sec-websocket-accept"]!==k){Et(i,b,"Invalid Sec-WebSocket-Accept header");return}let O=y.headers["sec-websocket-protocol"],S;if(O!==void 0?d.size?d.has(O)||(S="Server sent an invalid subprotocol"):S="Server sent a subprotocol but none was requested":d.size&&(S="Server sent no subprotocol"),S){Et(i,b,S);return}O&&(i._protocol=O);let R=y.headers["sec-websocket-extensions"];if(R!==void 0){if(!m){Et(i,b,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let T;try{T=hA(R)}catch{Et(i,b,"Invalid Sec-WebSocket-Extensions header");return}let A=Object.keys(T);if(A.length!==1||A[0]!==Ii.extensionName){Et(i,b,"Server indicated an extension that was not requested");return}try{m.accept(T[Ii.extensionName])}catch{Et(i,b,"Invalid Sec-WebSocket-Extensions header");return}i._extensions[Ii.extensionName]=m}i.setSocket(b,x,{allowSynchronousEvents:n.allowSynchronousEvents,generateMask:n.generateMask,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation})}),n.finishRequest?n.finishRequest(g,i):g.end()}function sa(i,e){i._readyState=qe.CLOSING,i.emit("error",e),i.emitClose()}function gA(i){return i.path=i.socketPath,yy.connect(i)}function vA(i){return i.path=void 0,!i.servername&&i.servername!==""&&(i.servername=yy.isIP(i.host)?"":i.host),tA.connect(i)}function Et(i,e,t){i._readyState=qe.CLOSING;let r=new Error(t);Error.captureStackTrace(r,Et),e.setHeader?(e[_y]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(sa,i,r)):(e.destroy(r),e.once("error",i.emit.bind(i,"error")),e.once("close",i.emitClose.bind(i)))}function ff(i,e,t){if(e){let r=pA(e).length;i._socket?i._sender._bufferedBytes+=r:i._bufferedAmount+=r}if(t){let r=new Error(`WebSocket is not open: readyState ${i.readyState} (${fi[i.readyState]})`);process.nextTick(t,r)}}function yA(i,e){let t=this[st];t._closeFrameReceived=!0,t._closeMessage=e,t._closeCode=i,t._socket[st]!==void 0&&(t._socket.removeListener("data",oa),process.nextTick(xy,t._socket),i===1005?t.close():t.close(i,e))}function bA(){let i=this[st];i.isPaused||i._socket.resume()}function _A(i){let e=this[st];e._socket[st]!==void 0&&(e._socket.removeListener("data",oa),process.nextTick(xy,e._socket),e.close(i[lA])),e.emit("error",i)}function vy(){this[st].emitClose()}function wA(i,e){this[st].emit("message",i,e)}function xA(i){let e=this[st];e._autoPong&&e.pong(i,!this._isServer,by),e.emit("ping",i)}function SA(i){this[st].emit("pong",i)}function xy(i){i.resume()}function Sy(){let i=this[st];this.removeListener("close",Sy),this.removeListener("data",oa),this.removeListener("end",Ey),i._readyState=qe.CLOSING;let e;!this._readableState.endEmitted&&!i._closeFrameReceived&&!i._receiver._writableState.errorEmitted&&(e=i._socket.read())!==null&&i._receiver.write(e),i._receiver.end(),this[st]=void 0,clearTimeout(i._closeTimer),i._receiver._writableState.finished||i._receiver._writableState.errorEmitted?i.emitClose():(i._receiver.on("error",vy),i._receiver.on("finish",vy))}function oa(i){this[st]._receiver.write(i)||this.pause()}function Ey(){let i=this[st];i._readyState=qe.CLOSING,i._receiver.end(),this.end()}function Oy(){let i=this[st];this.removeListener("error",Oy),this.on("error",by),i&&(i._readyState=qe.CLOSING,this.destroy())}});var Ty=_((jL,Cy)=>{"use strict";var{tokenChars:EA}=Yn();function OA(i){let e=new Set,t=-1,r=-1,n=0;for(n;n<i.length;n++){let o=i.charCodeAt(n);if(r===-1&&EA[o]===1)t===-1&&(t=n);else if(n!==0&&(o===32||o===9))r===-1&&t!==-1&&(r=n);else if(o===44){if(t===-1)throw new SyntaxError(`Unexpected character at index ${n}`);r===-1&&(r=n);let a=i.slice(t,r);if(e.has(a))throw new SyntaxError(`The "${a}" subprotocol is duplicated`);e.add(a),t=r=-1}else throw new SyntaxError(`Unexpected character at index ${n}`)}if(t===-1||r!==-1)throw new SyntaxError("Unexpected end of input");let s=i.slice(t,n);if(e.has(s))throw new SyntaxError(`The "${s}" subprotocol is duplicated`);return e.add(s),e}Cy.exports={parse:OA}});var Py=_(($L,Ry)=>{"use strict";var kA=require("events"),aa=require("http"),{Duplex:UL}=require("stream"),{createHash:CA}=require("crypto"),Ay=af(),or=Wn(),TA=Ty(),AA=hf(),{GUID:IA,kWebSocket:NA}=Ti(),LA=/^[+/0-9A-Za-z]{22}==$/,Iy=0,Ny=1,By=2,pf=class extends kA{constructor(e,t){if(super(),e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:AA,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=aa.createServer((r,n)=>{let s=aa.STATUS_CODES[426];n.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),n.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let r=this.emit.bind(this,"connection");this._removeListeners=BA(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(n,s,o)=>{this.handleUpgrade(n,s,o,r)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Iy}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===By){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(Zn,this);return}if(e&&this.once("close",e),this._state!==Ny)if(this._state=Ny,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(Zn,this):process.nextTick(Zn,this);else{let t=this._server;this._removeListeners(),this._removeListeners=this._server=null,t.close(()=>{Zn(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((t!==-1?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,n){t.on("error",Ly);let s=e.headers["sec-websocket-key"],o=e.headers.upgrade,a=+e.headers["sec-websocket-version"];if(e.method!=="GET"){ar(this,e,t,405,"Invalid HTTP method");return}if(o===void 0||o.toLowerCase()!=="websocket"){ar(this,e,t,400,"Invalid Upgrade header");return}if(s===void 0||!LA.test(s)){ar(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(a!==8&&a!==13){ar(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){Qn(t,400);return}let l=e.headers["sec-websocket-protocol"],c=new Set;if(l!==void 0)try{c=TA.parse(l)}catch{ar(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&u!==void 0){let d=new or(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let m=Ay.parse(u);m[or.extensionName]&&(d.accept(m[or.extensionName]),f[or.extensionName]=d)}catch{ar(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let d={origin:e.headers[`${a===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(d,(m,g,y,b)=>{if(!m)return Qn(t,g||401,y,b);this.completeUpgrade(f,s,c,e,t,r,n)});return}if(!this.options.verifyClient(d))return Qn(t,401)}this.completeUpgrade(f,s,c,e,t,r,n)}completeUpgrade(e,t,r,n,s,o,a){if(!s.readable||!s.writable)return s.destroy();if(s[NA])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Iy)return Qn(s,503);let c=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${CA("sha1").update(t+IA).digest("base64")}`],u=new this.options.WebSocket(null,void 0,this.options);if(r.size){let f=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;f&&(c.push(`Sec-WebSocket-Protocol: ${f}`),u._protocol=f)}if(e[or.extensionName]){let f=e[or.extensionName].params,d=Ay.format({[or.extensionName]:[f]});c.push(`Sec-WebSocket-Extensions: ${d}`),u._extensions=e}this.emit("headers",c,n),s.write(c.concat(`\r
`).join(`\r
`)),s.removeListener("error",Ly),u.setSocket(s,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(u),u.on("close",()=>{this.clients.delete(u),this._shouldEmitClose&&!this.clients.size&&process.nextTick(Zn,this)})),a(u,n)}};Ry.exports=pf;function BA(i,e){for(let t of Object.keys(e))i.on(t,e[t]);return function(){for(let r of Object.keys(e))i.removeListener(r,e[r])}}function Zn(i){i._state=By,i.emit("close")}function Ly(){this.destroy()}function Qn(i,e,t,r){t=t||aa.STATUS_CODES[e],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(t),...r},i.once("finish",i.destroy),i.end(`HTTP/1.1 ${e} ${aa.STATUS_CODES[e]}\r
`+Object.keys(r).map(n=>`${n}: ${r[n]}`).join(`\r
`)+`\r
\r
`+t)}function ar(i,e,t,r,n){if(i.listenerCount("wsClientError")){let s=new Error(n);Error.captureStackTrace(s,ar),i.emit("wsClientError",s,t,e)}else Qn(t,r,n)}});var qy=_((HL,Fy)=>{var Ni=require("constants"),PA=process.cwd,la=null,MA=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return la||(la=PA.call(process)),la};try{process.cwd()}catch{}typeof process.chdir=="function"&&(vf=process.chdir,process.chdir=function(i){la=null,vf.call(process,i)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,vf));var vf;Fy.exports=DA;function DA(i){Ni.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(i),i.lutimes||t(i),i.chown=s(i.chown),i.fchown=s(i.fchown),i.lchown=s(i.lchown),i.chmod=r(i.chmod),i.fchmod=r(i.fchmod),i.lchmod=r(i.lchmod),i.chownSync=o(i.chownSync),i.fchownSync=o(i.fchownSync),i.lchownSync=o(i.lchownSync),i.chmodSync=n(i.chmodSync),i.fchmodSync=n(i.fchmodSync),i.lchmodSync=n(i.lchmodSync),i.stat=a(i.stat),i.fstat=a(i.fstat),i.lstat=a(i.lstat),i.statSync=l(i.statSync),i.fstatSync=l(i.fstatSync),i.lstatSync=l(i.lstatSync),i.chmod&&!i.lchmod&&(i.lchmod=function(u,f,d){d&&process.nextTick(d)},i.lchmodSync=function(){}),i.chown&&!i.lchown&&(i.lchown=function(u,f,d,m){m&&process.nextTick(m)},i.lchownSync=function(){}),MA==="win32"&&(i.rename=typeof i.rename!="function"?i.rename:function(u){function f(d,m,g){var y=Date.now(),b=0;u(d,m,function x(E){if(E&&(E.code==="EACCES"||E.code==="EPERM")&&Date.now()-y<6e4){setTimeout(function(){i.stat(m,function(k,O){k&&k.code==="ENOENT"?u(d,m,x):g(E)})},b),b<100&&(b+=10);return}g&&g(E)})}return Object.setPrototypeOf&&Object.setPrototypeOf(f,u),f}(i.rename)),i.read=typeof i.read!="function"?i.read:function(u){function f(d,m,g,y,b,x){var E;if(x&&typeof x=="function"){var k=0;E=function(O,S,R){if(O&&O.code==="EAGAIN"&&k<10)return k++,u.call(i,d,m,g,y,b,E);x.apply(this,arguments)}}return u.call(i,d,m,g,y,b,E)}return Object.setPrototypeOf&&Object.setPrototypeOf(f,u),f}(i.read),i.readSync=typeof i.readSync!="function"?i.readSync:function(u){return function(f,d,m,g,y){for(var b=0;;)try{return u.call(i,f,d,m,g,y)}catch(x){if(x.code==="EAGAIN"&&b<10){b++;continue}throw x}}}(i.readSync);function e(u){u.lchmod=function(f,d,m){u.open(f,Ni.O_WRONLY|Ni.O_SYMLINK,d,function(g,y){if(g){m&&m(g);return}u.fchmod(y,d,function(b){u.close(y,function(x){m&&m(b||x)})})})},u.lchmodSync=function(f,d){var m=u.openSync(f,Ni.O_WRONLY|Ni.O_SYMLINK,d),g=!0,y;try{y=u.fchmodSync(m,d),g=!1}finally{if(g)try{u.closeSync(m)}catch{}else u.closeSync(m)}return y}}function t(u){Ni.hasOwnProperty("O_SYMLINK")&&u.futimes?(u.lutimes=function(f,d,m,g){u.open(f,Ni.O_SYMLINK,function(y,b){if(y){g&&g(y);return}u.futimes(b,d,m,function(x){u.close(b,function(E){g&&g(x||E)})})})},u.lutimesSync=function(f,d,m){var g=u.openSync(f,Ni.O_SYMLINK),y,b=!0;try{y=u.futimesSync(g,d,m),b=!1}finally{if(b)try{u.closeSync(g)}catch{}else u.closeSync(g)}return y}):u.futimes&&(u.lutimes=function(f,d,m,g){g&&process.nextTick(g)},u.lutimesSync=function(){})}function r(u){return u&&function(f,d,m){return u.call(i,f,d,function(g){c(g)&&(g=null),m&&m.apply(this,arguments)})}}function n(u){return u&&function(f,d){try{return u.call(i,f,d)}catch(m){if(!c(m))throw m}}}function s(u){return u&&function(f,d,m,g){return u.call(i,f,d,m,function(y){c(y)&&(y=null),g&&g.apply(this,arguments)})}}function o(u){return u&&function(f,d,m){try{return u.call(i,f,d,m)}catch(g){if(!c(g))throw g}}}function a(u){return u&&function(f,d,m){typeof d=="function"&&(m=d,d=null);function g(y,b){b&&(b.uid<0&&(b.uid+=4294967296),b.gid<0&&(b.gid+=4294967296)),m&&m.apply(this,arguments)}return d?u.call(i,f,d,g):u.call(i,f,g)}}function l(u){return u&&function(f,d){var m=d?u.call(i,f,d):u.call(i,f);return m&&(m.uid<0&&(m.uid+=4294967296),m.gid<0&&(m.gid+=4294967296)),m}}function c(u){if(!u||u.code==="ENOSYS")return!0;var f=!process.getuid||process.getuid()!==0;return!!(f&&(u.code==="EINVAL"||u.code==="EPERM"))}}});var $y=_((GL,Uy)=>{var jy=require("stream").Stream;Uy.exports=FA;function FA(i){return{ReadStream:e,WriteStream:t};function e(r,n){if(!(this instanceof e))return new e(r,n);jy.call(this);var s=this;this.path=r,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,n=n||{};for(var o=Object.keys(n),a=0,l=o.length;a<l;a++){var c=o[a];this[c]=n[c]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){s._read()});return}i.open(this.path,this.flags,this.mode,function(u,f){if(u){s.emit("error",u),s.readable=!1;return}s.fd=f,s.emit("open",f),s._read()})}function t(r,n){if(!(this instanceof t))return new t(r,n);jy.call(this),this.path=r,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,n=n||{};for(var s=Object.keys(n),o=0,a=s.length;o<a;o++){var l=s[o];this[l]=n[l]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=i.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}});var Hy=_((WL,Vy)=>{"use strict";Vy.exports=jA;var qA=Object.getPrototypeOf||function(i){return i.__proto__};function jA(i){if(i===null||typeof i!="object")return i;if(i instanceof Object)var e={__proto__:qA(i)};else var e=Object.create(null);return Object.getOwnPropertyNames(i).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}),e}});var Ky=_((YL,_f)=>{var De=require("fs"),UA=qy(),$A=$y(),VA=Hy(),ca=require("util"),it,fa;typeof Symbol=="function"&&typeof Symbol.for=="function"?(it=Symbol.for("graceful-fs.queue"),fa=Symbol.for("graceful-fs.previous")):(it="___graceful-fs.queue",fa="___graceful-fs.previous");function HA(){}function Yy(i,e){Object.defineProperty(i,it,{get:function(){return e}})}var lr=HA;ca.debuglog?lr=ca.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(lr=function(){var i=ca.format.apply(ca,arguments);i="GFS4: "+i.split(/\n/).join(`
GFS4: `),console.error(i)});De[it]||(Gy=global[it]||[],Yy(De,Gy),De.close=function(i){function e(t,r){return i.call(De,t,function(n){n||Wy(),typeof r=="function"&&r.apply(this,arguments)})}return Object.defineProperty(e,fa,{value:i}),e}(De.close),De.closeSync=function(i){function e(t){i.apply(De,arguments),Wy()}return Object.defineProperty(e,fa,{value:i}),e}(De.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){lr(De[it]),require("assert").equal(De[it].length,0)}));var Gy;global[it]||Yy(global,De[it]);_f.exports=yf(VA(De));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!De.__patched&&(_f.exports=yf(De),De.__patched=!0);function yf(i){UA(i),i.gracefulify=yf,i.createReadStream=S,i.createWriteStream=R;var e=i.readFile;i.readFile=t;function t(C,L,P){return typeof L=="function"&&(P=L,L=null),U(C,L,P);function U(F,H,j,V){return e(F,H,function(Y){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?zr([U,[F,H,j],Y,V||Date.now(),Date.now()]):typeof j=="function"&&j.apply(this,arguments)})}}var r=i.writeFile;i.writeFile=n;function n(C,L,P,U){return typeof P=="function"&&(U=P,P=null),F(C,L,P,U);function F(H,j,V,Y,Q){return r(H,j,V,function(W){W&&(W.code==="EMFILE"||W.code==="ENFILE")?zr([F,[H,j,V,Y],W,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var s=i.appendFile;s&&(i.appendFile=o);function o(C,L,P,U){return typeof P=="function"&&(U=P,P=null),F(C,L,P,U);function F(H,j,V,Y,Q){return s(H,j,V,function(W){W&&(W.code==="EMFILE"||W.code==="ENFILE")?zr([F,[H,j,V,Y],W,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var a=i.copyFile;a&&(i.copyFile=l);function l(C,L,P,U){return typeof P=="function"&&(U=P,P=0),F(C,L,P,U);function F(H,j,V,Y,Q){return a(H,j,V,function(W){W&&(W.code==="EMFILE"||W.code==="ENFILE")?zr([F,[H,j,V,Y],W,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var c=i.readdir;i.readdir=f;var u=/^v[0-5]\./;function f(C,L,P){typeof L=="function"&&(P=L,L=null);var U=u.test(process.version)?function(j,V,Y,Q){return c(j,F(j,V,Y,Q))}:function(j,V,Y,Q){return c(j,V,F(j,V,Y,Q))};return U(C,L,P);function F(H,j,V,Y){return function(Q,W){Q&&(Q.code==="EMFILE"||Q.code==="ENFILE")?zr([U,[H,j,V],Q,Y||Date.now(),Date.now()]):(W&&W.sort&&W.sort(),typeof V=="function"&&V.call(this,Q,W))}}}if(process.version.substr(0,4)==="v0.8"){var d=$A(i);x=d.ReadStream,k=d.WriteStream}var m=i.ReadStream;m&&(x.prototype=Object.create(m.prototype),x.prototype.open=E);var g=i.WriteStream;g&&(k.prototype=Object.create(g.prototype),k.prototype.open=O),Object.defineProperty(i,"ReadStream",{get:function(){return x},set:function(C){x=C},enumerable:!0,configurable:!0}),Object.defineProperty(i,"WriteStream",{get:function(){return k},set:function(C){k=C},enumerable:!0,configurable:!0});var y=x;Object.defineProperty(i,"FileReadStream",{get:function(){return y},set:function(C){y=C},enumerable:!0,configurable:!0});var b=k;Object.defineProperty(i,"FileWriteStream",{get:function(){return b},set:function(C){b=C},enumerable:!0,configurable:!0});function x(C,L){return this instanceof x?(m.apply(this,arguments),this):x.apply(Object.create(x.prototype),arguments)}function E(){var C=this;A(C.path,C.flags,C.mode,function(L,P){L?(C.autoClose&&C.destroy(),C.emit("error",L)):(C.fd=P,C.emit("open",P),C.read())})}function k(C,L){return this instanceof k?(g.apply(this,arguments),this):k.apply(Object.create(k.prototype),arguments)}function O(){var C=this;A(C.path,C.flags,C.mode,function(L,P){L?(C.destroy(),C.emit("error",L)):(C.fd=P,C.emit("open",P))})}function S(C,L){return new i.ReadStream(C,L)}function R(C,L){return new i.WriteStream(C,L)}var T=i.open;i.open=A;function A(C,L,P,U){return typeof P=="function"&&(U=P,P=null),F(C,L,P,U);function F(H,j,V,Y,Q){return T(H,j,V,function(W,de){W&&(W.code==="EMFILE"||W.code==="ENFILE")?zr([F,[H,j,V,Y],W,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}return i}function zr(i){lr("ENQUEUE",i[0].name,i[1]),De[it].push(i),bf()}var ua;function Wy(){for(var i=Date.now(),e=0;e<De[it].length;++e)De[it][e].length>2&&(De[it][e][3]=i,De[it][e][4]=i);bf()}function bf(){if(clearTimeout(ua),ua=void 0,De[it].length!==0){var i=De[it].shift(),e=i[0],t=i[1],r=i[2],n=i[3],s=i[4];if(n===void 0)lr("RETRY",e.name,t),e.apply(null,t);else if(Date.now()-n>=6e4){lr("TIMEOUT",e.name,t);var o=t.pop();typeof o=="function"&&o.call(null,r)}else{var a=Date.now()-s,l=Math.max(s-n,1),c=Math.min(l*1.2,100);a>=c?(lr("RETRY",e.name,t),e.apply(null,t.concat([n]))):De[it].push(i)}ua===void 0&&(ua=setTimeout(bf,0))}}});var Jy=_((KL,zy)=>{function Rt(i,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(i)),this._timeouts=i,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}zy.exports=Rt;Rt.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};Rt.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timeouts=[],this._cachedTimeouts=null};Rt.prototype.retry=function(i){if(this._timeout&&clearTimeout(this._timeout),!i)return!1;var e=new Date().getTime();if(i&&e-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(i);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),t=this._timeouts.shift();else return!1;var r=this,n=setTimeout(function(){r._attempts++,r._operationTimeoutCb&&(r._timeout=setTimeout(function(){r._operationTimeoutCb(r._attempts)},r._operationTimeout),r._options.unref&&r._timeout.unref()),r._fn(r._attempts)},t);return this._options.unref&&n.unref(),!0};Rt.prototype.attempt=function(i,e){this._fn=i,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};Rt.prototype.try=function(i){console.log("Using RetryOperation.try() is deprecated"),this.attempt(i)};Rt.prototype.start=function(i){console.log("Using RetryOperation.start() is deprecated"),this.attempt(i)};Rt.prototype.start=Rt.prototype.try;Rt.prototype.errors=function(){return this._errors};Rt.prototype.attempts=function(){return this._attempts};Rt.prototype.mainError=function(){if(this._errors.length===0)return null;for(var i={},e=null,t=0,r=0;r<this._errors.length;r++){var n=this._errors[r],s=n.message,o=(i[s]||0)+1;i[s]=o,o>=t&&(e=n,t=o)}return e}});var Zy=_(cr=>{var GA=Jy();cr.operation=function(i){var e=cr.timeouts(i);return new GA(e,{forever:i&&i.forever,unref:i&&i.unref,maxRetryTime:i&&i.maxRetryTime})};cr.timeouts=function(i){if(i instanceof Array)return[].concat(i);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in i)e[t]=i[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var r=[],n=0;n<e.retries;n++)r.push(this.createTimeout(n,e));return i&&i.forever&&!r.length&&r.push(this.createTimeout(n,e)),r.sort(function(s,o){return s-o}),r};cr.createTimeout=function(i,e){var t=e.randomize?Math.random()+1:1,r=Math.round(t*e.minTimeout*Math.pow(e.factor,i));return r=Math.min(r,e.maxTimeout),r};cr.wrap=function(i,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var r in i)typeof i[r]=="function"&&t.push(r)}for(var n=0;n<t.length;n++){var s=t[n],o=i[s];i[s]=function(l){var c=cr.operation(e),u=Array.prototype.slice.call(arguments,1),f=u.pop();u.push(function(d){c.retry(d)||(d&&(arguments[0]=c.mainError()),f.apply(this,arguments))}),c.attempt(function(){l.apply(i,u)})}.bind(i,o),i[s].options=e}}});var Xy=_((JL,Qy)=>{Qy.exports=Zy()});var eb=_((ZL,ha)=>{ha.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&ha.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&ha.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var sb=_((QL,Qr)=>{var Me=global.process,ur=function(i){return i&&typeof i=="object"&&typeof i.removeListener=="function"&&typeof i.emit=="function"&&typeof i.reallyExit=="function"&&typeof i.listeners=="function"&&typeof i.kill=="function"&&typeof i.pid=="number"&&typeof i.on=="function"};ur(Me)?(tb=require("assert"),Jr=eb(),ib=/^win/i.test(Me.platform),Xn=require("events"),typeof Xn!="function"&&(Xn=Xn.EventEmitter),Me.__signal_exit_emitter__?Qe=Me.__signal_exit_emitter__:(Qe=Me.__signal_exit_emitter__=new Xn,Qe.count=0,Qe.emitted={}),Qe.infinite||(Qe.setMaxListeners(1/0),Qe.infinite=!0),Qr.exports=function(i,e){if(!ur(global.process))return function(){};tb.equal(typeof i,"function","a callback must be provided for exit handler"),Zr===!1&&wf();var t="exit";e&&e.alwaysLast&&(t="afterexit");var r=function(){Qe.removeListener(t,i),Qe.listeners("exit").length===0&&Qe.listeners("afterexit").length===0&&pa()};return Qe.on(t,i),r},pa=function(){!Zr||!ur(global.process)||(Zr=!1,Jr.forEach(function(e){try{Me.removeListener(e,da[e])}catch{}}),Me.emit=ma,Me.reallyExit=xf,Qe.count-=1)},Qr.exports.unload=pa,fr=function(e,t,r){Qe.emitted[e]||(Qe.emitted[e]=!0,Qe.emit(e,t,r))},da={},Jr.forEach(function(i){da[i]=function(){if(ur(global.process)){var t=Me.listeners(i);t.length===Qe.count&&(pa(),fr("exit",null,i),fr("afterexit",null,i),ib&&i==="SIGHUP"&&(i="SIGINT"),Me.kill(Me.pid,i))}}}),Qr.exports.signals=function(){return Jr},Zr=!1,wf=function(){Zr||!ur(global.process)||(Zr=!0,Qe.count+=1,Jr=Jr.filter(function(e){try{return Me.on(e,da[e]),!0}catch{return!1}}),Me.emit=nb,Me.reallyExit=rb)},Qr.exports.load=wf,xf=Me.reallyExit,rb=function(e){ur(global.process)&&(Me.exitCode=e||0,fr("exit",Me.exitCode,null),fr("afterexit",Me.exitCode,null),xf.call(Me,Me.exitCode))},ma=Me.emit,nb=function(e,t){if(e==="exit"&&ur(global.process)){t!==void 0&&(Me.exitCode=t);var r=ma.apply(this,arguments);return fr("exit",Me.exitCode,null),fr("afterexit",Me.exitCode,null),r}else return ma.apply(this,arguments)}):Qr.exports=function(){return function(){}};var tb,Jr,ib,Xn,Qe,pa,fr,da,Zr,wf,xf,rb,ma,nb});var pb=_((XL,hb)=>{"use strict";var WA=require("path"),cb=Ky(),YA=Xy(),KA=sb(),Li={},ob=Symbol();function zA(i,e,t){let r=e[ob];if(r)return e.stat(i,(s,o)=>{if(s)return t(s);t(null,o.mtime,r)});let n=new Date(Math.ceil(Date.now()/1e3)*1e3+5);e.utimes(i,n,n,s=>{if(s)return t(s);e.stat(i,(o,a)=>{if(o)return t(o);let l=a.mtime.getTime()%1e3===0?"s":"ms";Object.defineProperty(e,ob,{value:l}),t(null,a.mtime,l)})})}function JA(i){let e=Date.now();return i==="s"&&(e=Math.ceil(e/1e3)*1e3),new Date(e)}function va(i,e){return e.lockfilePath||`${i}.lock`}function ub(i,e,t){if(!e.realpath)return t(null,WA.resolve(i));e.fs.realpath(i,t)}function Ef(i,e,t){let r=va(i,e);e.fs.mkdir(r,n=>{if(!n)return zA(r,e.fs,(s,o,a)=>{if(s)return e.fs.rmdir(r,()=>{}),t(s);t(null,o,a)});if(n.code!=="EEXIST")return t(n);if(e.stale<=0)return t(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:i}));e.fs.stat(r,(s,o)=>{if(s)return s.code==="ENOENT"?Ef(i,{...e,stale:0},t):t(s);if(!ZA(o,e))return t(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:i}));fb(i,e,a=>{if(a)return t(a);Ef(i,{...e,stale:0},t)})})})}function ZA(i,e){return i.mtime.getTime()<Date.now()-e.stale}function fb(i,e,t){e.fs.rmdir(va(i,e),r=>{if(r&&r.code!=="ENOENT")return t(r);t()})}function ga(i,e){let t=Li[i];t.updateTimeout||(t.updateDelay=t.updateDelay||e.update,t.updateTimeout=setTimeout(()=>{t.updateTimeout=null,e.fs.stat(t.lockfilePath,(r,n)=>{let s=t.lastUpdate+e.stale<Date.now();if(r)return r.code==="ENOENT"||s?Sf(i,t,Object.assign(r,{code:"ECOMPROMISED"})):(t.updateDelay=1e3,ga(i,e));if(!(t.mtime.getTime()===n.mtime.getTime()))return Sf(i,t,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let a=JA(t.mtimePrecision);e.fs.utimes(t.lockfilePath,a,a,l=>{let c=t.lastUpdate+e.stale<Date.now();if(!t.released){if(l)return l.code==="ENOENT"||c?Sf(i,t,Object.assign(l,{code:"ECOMPROMISED"})):(t.updateDelay=1e3,ga(i,e));t.mtime=a,t.lastUpdate=Date.now(),t.updateDelay=null,ga(i,e)}})})},t.updateDelay),t.updateTimeout.unref&&t.updateTimeout.unref())}function Sf(i,e,t){e.released=!0,e.updateTimeout&&clearTimeout(e.updateTimeout),Li[i]===e&&delete Li[i],e.options.onCompromised(t)}function QA(i,e,t){e={stale:1e4,update:null,realpath:!0,retries:0,fs:cb,onCompromised:r=>{throw r},...e},e.retries=e.retries||0,e.retries=typeof e.retries=="number"?{retries:e.retries}:e.retries,e.stale=Math.max(e.stale||0,2e3),e.update=e.update==null?e.stale/2:e.update||0,e.update=Math.max(Math.min(e.update,e.stale/2),1e3),ub(i,e,(r,n)=>{if(r)return t(r);let s=YA.operation(e.retries);s.attempt(()=>{Ef(n,e,(o,a,l)=>{if(s.retry(o))return;if(o)return t(s.mainError());let c=Li[n]={lockfilePath:va(n,e),mtime:a,mtimePrecision:l,options:e,lastUpdate:Date.now()};ga(n,e),t(null,u=>{if(c.released)return u&&u(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));XA(n,{...e,realpath:!1},u)})})})})}function XA(i,e,t){e={fs:cb,realpath:!0,...e},ub(i,e,(r,n)=>{if(r)return t(r);let s=Li[n];if(!s)return t(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));s.updateTimeout&&clearTimeout(s.updateTimeout),s.released=!0,delete Li[n],fb(n,e,t)})}function ab(i){return(...e)=>new Promise((t,r)=>{e.push((n,s)=>{n?r(n):t(s)}),i(...e)})}var lb=!1;function eI(){lb||(lb=!0,KA(()=>{for(let i in Li){let e=Li[i].options;try{e.fs.rmdirSync(va(i,e))}catch{}}}))}hb.exports.lock=async(i,e)=>{eI();let t=await ab(QA)(i,e);return ab(t)}});var vI={};Tf(vI,{HttpsProxyAgent:()=>Eb.HttpsProxyAgent,PNG:()=>Ob.PNG,SocksProxyAgent:()=>kb.SocksProxyAgent,colors:()=>tI,debug:()=>iI,diff:()=>rI,dotenv:()=>nI,getProxyForUrl:()=>Sb.getProxyForUrl,jpegjs:()=>sI,lockfile:()=>aI,mime:()=>lI,minimatch:()=>cI,open:()=>uI,program:()=>sm,progress:()=>fI,ws:()=>pI,wsReceiver:()=>mI,wsSender:()=>gI,wsServer:()=>dI,yaml:()=>hI});module.exports=Yb(vI);var db=$e(rh()),mb=$e(br());var Pa={};Tf(Pa,{Diff:()=>Tt,applyPatch:()=>Mh,applyPatches:()=>X_,canonicalize:()=>Es,convertChangesToDMP:()=>lw,convertChangesToXML:()=>cw,createPatch:()=>ew,createTwoFilesPatch:()=>Dh,diffArrays:()=>z_,diffChars:()=>N_,diffCss:()=>j_,diffJson:()=>K_,diffLines:()=>Aa,diffSentences:()=>q_,diffTrimmedLines:()=>F_,diffWords:()=>M_,diffWordsWithSpace:()=>Nh,formatPatch:()=>Cs,merge:()=>nw,parsePatch:()=>Ts,reversePatch:()=>Fh,structuredPatch:()=>ks});function Tt(){}Tt.prototype={diff:function(e,t){var r,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=n.callback;typeof n=="function"&&(s=n,n={});var o=this;function a(O){return O=o.postProcess(O,n),s?(setTimeout(function(){s(O)},0),!0):O}e=this.castInput(e,n),t=this.castInput(t,n),e=this.removeEmpty(this.tokenize(e,n)),t=this.removeEmpty(this.tokenize(t,n));var l=t.length,c=e.length,u=1,f=l+c;n.maxEditLength!=null&&(f=Math.min(f,n.maxEditLength));var d=(r=n.timeout)!==null&&r!==void 0?r:1/0,m=Date.now()+d,g=[{oldPos:-1,lastComponent:void 0}],y=this.extractCommon(g[0],t,e,0,n);if(g[0].oldPos+1>=c&&y+1>=l)return a(mh(o,g[0].lastComponent,t,e,o.useLongestToken));var b=-1/0,x=1/0;function E(){for(var O=Math.max(b,-u);O<=Math.min(x,u);O+=2){var S=void 0,R=g[O-1],T=g[O+1];R&&(g[O-1]=void 0);var A=!1;if(T){var C=T.oldPos-O;A=T&&0<=C&&C<l}var L=R&&R.oldPos+1<c;if(!A&&!L){g[O]=void 0;continue}if(!L||A&&R.oldPos<T.oldPos?S=o.addToPath(T,!0,!1,0,n):S=o.addToPath(R,!1,!0,1,n),y=o.extractCommon(S,t,e,O,n),S.oldPos+1>=c&&y+1>=l)return a(mh(o,S.lastComponent,t,e,o.useLongestToken));g[O]=S,S.oldPos+1>=c&&(x=Math.min(x,O-1)),y+1>=l&&(b=Math.max(b,O+1))}u++}if(s)(function O(){setTimeout(function(){if(u>f||Date.now()>m)return s();E()||O()},0)})();else for(;u<=f&&Date.now()<=m;){var k=E();if(k)return k}},addToPath:function(e,t,r,n,s){var o=e.lastComponent;return o&&!s.oneChangePerToken&&o.added===t&&o.removed===r?{oldPos:e.oldPos+n,lastComponent:{count:o.count+1,added:t,removed:r,previousComponent:o.previousComponent}}:{oldPos:e.oldPos+n,lastComponent:{count:1,added:t,removed:r,previousComponent:o}}},extractCommon:function(e,t,r,n,s){for(var o=t.length,a=r.length,l=e.oldPos,c=l-n,u=0;c+1<o&&l+1<a&&this.equals(r[l+1],t[c+1],s);)c++,l++,u++,s.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return u&&!s.oneChangePerToken&&(e.lastComponent={count:u,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=l,c},equals:function(e,t,r){return r.comparator?r.comparator(e,t):e===t||r.ignoreCase&&e.toLowerCase()===t.toLowerCase()},removeEmpty:function(e){for(var t=[],r=0;r<e.length;r++)e[r]&&t.push(e[r]);return t},castInput:function(e){return e},tokenize:function(e){return Array.from(e)},join:function(e){return e.join("")},postProcess:function(e){return e}};function mh(i,e,t,r,n){for(var s=[],o;e;)s.push(e),o=e.previousComponent,delete e.previousComponent,e=o;s.reverse();for(var a=0,l=s.length,c=0,u=0;a<l;a++){var f=s[a];if(f.removed)f.value=i.join(r.slice(u,u+f.count)),u+=f.count;else{if(!f.added&&n){var d=t.slice(c,c+f.count);d=d.map(function(m,g){var y=r[u+g];return y.length>m.length?y:m}),f.value=i.join(d)}else f.value=i.join(t.slice(c,c+f.count));c+=f.count,f.added||(u+=f.count)}}return s}var I_=new Tt;function N_(i,e,t){return I_.diff(i,e,t)}function gh(i,e){var t;for(t=0;t<i.length&&t<e.length;t++)if(i[t]!=e[t])return i.slice(0,t);return i.slice(0,t)}function vh(i,e){var t;if(!i||!e||i[i.length-1]!=e[e.length-1])return"";for(t=0;t<i.length&&t<e.length;t++)if(i[i.length-(t+1)]!=e[e.length-(t+1)])return i.slice(-t);return i.slice(-t)}function Ca(i,e,t){if(i.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(i)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return t+i.slice(e.length)}function Ta(i,e,t){if(!e)return i+t;if(i.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(i)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return i.slice(0,-e.length)+t}function en(i,e){return Ca(i,e,"")}function ws(i,e){return Ta(i,e,"")}function yh(i,e){return e.slice(0,L_(i,e))}function L_(i,e){var t=0;i.length>e.length&&(t=i.length-e.length);var r=e.length;i.length<e.length&&(r=i.length);var n=Array(r),s=0;n[0]=0;for(var o=1;o<r;o++){for(e[o]==e[s]?n[o]=n[s]:n[o]=s;s>0&&e[o]!=e[s];)s=n[s];e[o]==e[s]&&s++}s=0;for(var a=t;a<i.length;a++){for(;s>0&&i[a]!=e[s];)s=n[s];i[a]==e[s]&&s++}return s}function B_(i){return i.includes(`\r
`)&&!i.startsWith(`
`)&&!i.match(/[^\r]\n/)}function R_(i){return!i.includes(`\r
`)&&i.includes(`
`)}var Ss="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",P_=new RegExp("[".concat(Ss,"]+|\\s+|[^").concat(Ss,"]"),"ug"),tn=new Tt;tn.equals=function(i,e,t){return t.ignoreCase&&(i=i.toLowerCase(),e=e.toLowerCase()),i.trim()===e.trim()};tn.tokenize=function(i){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t;if(e.intlSegmenter){if(e.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');t=Array.from(e.intlSegmenter.segment(i),function(s){return s.segment})}else t=i.match(P_)||[];var r=[],n=null;return t.forEach(function(s){/\s/.test(s)?n==null?r.push(s):r.push(r.pop()+s):/\s/.test(n)?r[r.length-1]==n?r.push(r.pop()+s):r.push(n+s):r.push(s),n=s}),r};tn.join=function(i){return i.map(function(e,t){return t==0?e:e.replace(/^\s+/,"")}).join("")};tn.postProcess=function(i,e){if(!i||e.oneChangePerToken)return i;var t=null,r=null,n=null;return i.forEach(function(s){s.added?r=s:s.removed?n=s:((r||n)&&bh(t,n,r,s),t=s,r=null,n=null)}),(r||n)&&bh(t,n,r,null),i};function M_(i,e,t){return(t==null?void 0:t.ignoreWhitespace)!=null&&!t.ignoreWhitespace?Nh(i,e,t):tn.diff(i,e,t)}function bh(i,e,t,r){if(e&&t){var n=e.value.match(/^\s*/)[0],s=e.value.match(/\s*$/)[0],o=t.value.match(/^\s*/)[0],a=t.value.match(/\s*$/)[0];if(i){var l=gh(n,o);i.value=Ta(i.value,o,l),e.value=en(e.value,l),t.value=en(t.value,l)}if(r){var c=vh(s,a);r.value=Ca(r.value,a,c),e.value=ws(e.value,c),t.value=ws(t.value,c)}}else if(t)i&&(t.value=t.value.replace(/^\s*/,"")),r&&(r.value=r.value.replace(/^\s*/,""));else if(i&&r){var u=r.value.match(/^\s*/)[0],f=e.value.match(/^\s*/)[0],d=e.value.match(/\s*$/)[0],m=gh(u,f);e.value=en(e.value,m);var g=vh(en(u,m),d);e.value=ws(e.value,g),r.value=Ca(r.value,u,g),i.value=Ta(i.value,u,u.slice(0,u.length-g.length))}else if(r){var y=r.value.match(/^\s*/)[0],b=e.value.match(/\s*$/)[0],x=yh(b,y);e.value=ws(e.value,x)}else if(i){var E=i.value.match(/\s*$/)[0],k=e.value.match(/^\s*/)[0],O=yh(E,k);e.value=en(e.value,O)}}var Ih=new Tt;Ih.tokenize=function(i){var e=new RegExp("(\\r?\\n)|[".concat(Ss,"]+|[^\\S\\n\\r]+|[^").concat(Ss,"]"),"ug");return i.match(e)||[]};function Nh(i,e,t){return Ih.diff(i,e,t)}function D_(i,e){if(typeof i=="function")e.callback=i;else if(i)for(var t in i)i.hasOwnProperty(t)&&(e[t]=i[t]);return e}var rn=new Tt;rn.tokenize=function(i,e){e.stripTrailingCr&&(i=i.replace(/\r\n/g,`
`));var t=[],r=i.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var n=0;n<r.length;n++){var s=r[n];n%2&&!e.newlineIsToken?t[t.length-1]+=s:t.push(s)}return t};rn.equals=function(i,e,t){return t.ignoreWhitespace?((!t.newlineIsToken||!i.includes(`
`))&&(i=i.trim()),(!t.newlineIsToken||!e.includes(`
`))&&(e=e.trim())):t.ignoreNewlineAtEof&&!t.newlineIsToken&&(i.endsWith(`
`)&&(i=i.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),Tt.prototype.equals.call(this,i,e,t)};function Aa(i,e,t){return rn.diff(i,e,t)}function F_(i,e,t){var r=D_(t,{ignoreWhitespace:!0});return rn.diff(i,e,r)}var Lh=new Tt;Lh.tokenize=function(i){return i.split(/(\S.+?[.!?])(?=\s+|$)/)};function q_(i,e,t){return Lh.diff(i,e,t)}var Bh=new Tt;Bh.tokenize=function(i){return i.split(/([{}:;,]|\s+)/)};function j_(i,e,t){return Bh.diff(i,e,t)}function _h(i,e){var t=Object.keys(i);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);e&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(i,n).enumerable})),t.push.apply(t,r)}return t}function dt(i){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?_h(Object(t),!0).forEach(function(r){V_(i,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(t)):_h(Object(t)).forEach(function(r){Object.defineProperty(i,r,Object.getOwnPropertyDescriptor(t,r))})}return i}function U_(i,e){if(typeof i!="object"||!i)return i;var t=i[Symbol.toPrimitive];if(t!==void 0){var r=t.call(i,e||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(i)}function $_(i){var e=U_(i,"string");return typeof e=="symbol"?e:e+""}function Ia(i){"@babel/helpers - typeof";return Ia=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ia(i)}function V_(i,e,t){return e=$_(e),e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}function ii(i){return H_(i)||G_(i)||W_(i)||Y_()}function H_(i){if(Array.isArray(i))return Na(i)}function G_(i){if(typeof Symbol!="undefined"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function W_(i,e){if(i){if(typeof i=="string")return Na(i,e);var t=Object.prototype.toString.call(i).slice(8,-1);if(t==="Object"&&i.constructor&&(t=i.constructor.name),t==="Map"||t==="Set")return Array.from(i);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Na(i,e)}}function Na(i,e){(e==null||e>i.length)&&(e=i.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=i[t];return r}function Y_(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var _r=new Tt;_r.useLongestToken=!0;_r.tokenize=rn.tokenize;_r.castInput=function(i,e){var t=e.undefinedReplacement,r=e.stringifyReplacer,n=r===void 0?function(s,o){return typeof o=="undefined"?t:o}:r;return typeof i=="string"?i:JSON.stringify(Es(i,null,null,n),n,"  ")};_r.equals=function(i,e,t){return Tt.prototype.equals.call(_r,i.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),t)};function K_(i,e,t){return _r.diff(i,e,t)}function Es(i,e,t,r,n){e=e||[],t=t||[],r&&(i=r(n,i));var s;for(s=0;s<e.length;s+=1)if(e[s]===i)return t[s];var o;if(Object.prototype.toString.call(i)==="[object Array]"){for(e.push(i),o=new Array(i.length),t.push(o),s=0;s<i.length;s+=1)o[s]=Es(i[s],e,t,r,n);return e.pop(),t.pop(),o}if(i&&i.toJSON&&(i=i.toJSON()),Ia(i)==="object"&&i!==null){e.push(i),o={},t.push(o);var a=[],l;for(l in i)Object.prototype.hasOwnProperty.call(i,l)&&a.push(l);for(a.sort(),s=0;s<a.length;s+=1)l=a[s],o[l]=Es(i[l],e,t,r,l);e.pop(),t.pop()}else o=i;return o}var Os=new Tt;Os.tokenize=function(i){return i.slice()};Os.join=Os.removeEmpty=function(i){return i};function z_(i,e,t){return Os.diff(i,e,t)}function Rh(i){return Array.isArray(i)?i.map(Rh):dt(dt({},i),{},{hunks:i.hunks.map(function(e){return dt(dt({},e),{},{lines:e.lines.map(function(t,r){var n;return t.startsWith("\\")||t.endsWith("\r")||(n=e.lines[r+1])!==null&&n!==void 0&&n.startsWith("\\")?t:t+"\r"})})})})}function Ph(i){return Array.isArray(i)?i.map(Ph):dt(dt({},i),{},{hunks:i.hunks.map(function(e){return dt(dt({},e),{},{lines:e.lines.map(function(t){return t.endsWith("\r")?t.substring(0,t.length-1):t})})})})}function J_(i){return Array.isArray(i)||(i=[i]),!i.some(function(e){return e.hunks.some(function(t){return t.lines.some(function(r){return!r.startsWith("\\")&&r.endsWith("\r")})})})}function Z_(i){return Array.isArray(i)||(i=[i]),i.some(function(e){return e.hunks.some(function(t){return t.lines.some(function(r){return r.endsWith("\r")})})})&&i.every(function(e){return e.hunks.every(function(t){return t.lines.every(function(r,n){var s;return r.startsWith("\\")||r.endsWith("\r")||((s=t.lines[n+1])===null||s===void 0?void 0:s.startsWith("\\"))})})})}function Ts(i){var e=i.split(/\n/),t=[],r=0;function n(){var a={};for(t.push(a);r<e.length;){var l=e[r];if(/^(\-\-\-|\+\+\+|@@)\s/.test(l))break;var c=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(l);c&&(a.index=c[1]),r++}for(s(a),s(a),a.hunks=[];r<e.length;){var u=e[r];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(u))break;if(/^@@/.test(u))a.hunks.push(o());else{if(u)throw new Error("Unknown line "+(r+1)+" "+JSON.stringify(u));r++}}}function s(a){var l=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[r]);if(l){var c=l[1]==="---"?"old":"new",u=l[2].split("	",2),f=u[0].replace(/\\\\/g,"\\");/^".*"$/.test(f)&&(f=f.substr(1,f.length-2)),a[c+"FileName"]=f,a[c+"Header"]=(u[1]||"").trim(),r++}}function o(){var a=r,l=e[r++],c=l.split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),u={oldStart:+c[1],oldLines:typeof c[2]=="undefined"?1:+c[2],newStart:+c[3],newLines:typeof c[4]=="undefined"?1:+c[4],lines:[]};u.oldLines===0&&(u.oldStart+=1),u.newLines===0&&(u.newStart+=1);for(var f=0,d=0;r<e.length&&(d<u.oldLines||f<u.newLines||(m=e[r])!==null&&m!==void 0&&m.startsWith("\\"));r++){var m,g=e[r].length==0&&r!=e.length-1?" ":e[r][0];if(g==="+"||g==="-"||g===" "||g==="\\")u.lines.push(e[r]),g==="+"?f++:g==="-"?d++:g===" "&&(f++,d++);else throw new Error("Hunk at line ".concat(a+1," contained invalid line ").concat(e[r]))}if(!f&&u.newLines===1&&(u.newLines=0),!d&&u.oldLines===1&&(u.oldLines=0),f!==u.newLines)throw new Error("Added line count did not match for hunk at line "+(a+1));if(d!==u.oldLines)throw new Error("Removed line count did not match for hunk at line "+(a+1));return u}for(;r<e.length;)n();return t}function Q_(i,e,t){var r=!0,n=!1,s=!1,o=1;return function a(){if(r&&!s){if(n?o++:r=!1,i+o<=t)return i+o;s=!0}if(!n)return s||(r=!0),e<=i-o?i-o++:(n=!0,a())}}function Mh(i,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(typeof e=="string"&&(e=Ts(e)),Array.isArray(e)){if(e.length>1)throw new Error("applyPatch only works with a single input.");e=e[0]}(t.autoConvertLineEndings||t.autoConvertLineEndings==null)&&(B_(i)&&J_(e)?e=Rh(e):R_(i)&&Z_(e)&&(e=Ph(e)));var r=i.split(`
`),n=e.hunks,s=t.compareLine||function(P,U,F,H){return U===H},o=t.fuzzFactor||0,a=0;if(o<0||!Number.isInteger(o))throw new Error("fuzzFactor must be a non-negative integer");if(!n.length)return i;for(var l="",c=!1,u=!1,f=0;f<n[n.length-1].lines.length;f++){var d=n[n.length-1].lines[f];d[0]=="\\"&&(l[0]=="+"?c=!0:l[0]=="-"&&(u=!0)),l=d}if(c){if(u){if(!o&&r[r.length-1]=="")return!1}else if(r[r.length-1]=="")r.pop();else if(!o)return!1}else if(u){if(r[r.length-1]!="")r.push("");else if(!o)return!1}function m(P,U,F){for(var H=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,j=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,V=arguments.length>5&&arguments[5]!==void 0?arguments[5]:[],Y=arguments.length>6&&arguments[6]!==void 0?arguments[6]:0,Q=0,W=!1;H<P.length;H++){var de=P[H],ae=de.length>0?de[0]:" ",ne=de.length>0?de.substr(1):de;if(ae==="-")if(s(U+1,r[U],ae,ne))U++,Q=0;else return!F||r[U]==null?null:(V[Y]=r[U],m(P,U+1,F-1,H,!1,V,Y+1));if(ae==="+"){if(!j)return null;V[Y]=ne,Y++,Q=0,W=!0}if(ae===" ")if(Q++,V[Y]=r[U],s(U+1,r[U],ae,ne))Y++,j=!0,W=!1,U++;else return W||!F?null:r[U]&&(m(P,U+1,F-1,H+1,!1,V,Y+1)||m(P,U+1,F-1,H,!1,V,Y+1))||m(P,U,F-1,H+1,!1,V,Y)}return Y-=Q,U-=Q,V.length=Y,{patchedLines:V,oldLineLastI:U-1}}for(var g=[],y=0,b=0;b<n.length;b++){for(var x=n[b],E=void 0,k=r.length-x.oldLines+o,O=void 0,S=0;S<=o;S++){O=x.oldStart+y-1;for(var R=Q_(O,a,k);O!==void 0&&(E=m(x.lines,O,S),!E);O=R());if(E)break}if(!E)return!1;for(var T=a;T<O;T++)g.push(r[T]);for(var A=0;A<E.patchedLines.length;A++){var C=E.patchedLines[A];g.push(C)}a=E.oldLineLastI+1,y=O+1-x.oldStart}for(var L=a;L<r.length;L++)g.push(r[L]);return g.join(`
`)}function X_(i,e){typeof i=="string"&&(i=Ts(i));var t=0;function r(){var n=i[t++];if(!n)return e.complete();e.loadFile(n,function(s,o){if(s)return e.complete(s);var a=Mh(o,n,e);e.patched(n,a,function(l){if(l)return e.complete(l);r()})})}r()}function ks(i,e,t,r,n,s,o){if(o||(o={}),typeof o=="function"&&(o={callback:o}),typeof o.context=="undefined"&&(o.context=4),o.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(o.callback){var a=o,l=a.callback;Aa(t,r,dt(dt({},o),{},{callback:function(f){var d=c(f);l(d)}}))}else return c(Aa(t,r,o));function c(u){if(!u)return;u.push({value:"",lines:[]});function f(A){return A.map(function(C){return" "+C})}for(var d=[],m=0,g=0,y=[],b=1,x=1,E=function(){var C=u[k],L=C.lines||tw(C.value);if(C.lines=L,C.added||C.removed){var P;if(!m){var U=u[k-1];m=b,g=x,U&&(y=o.context>0?f(U.lines.slice(-o.context)):[],m-=y.length,g-=y.length)}(P=y).push.apply(P,ii(L.map(function(Y){return(C.added?"+":"-")+Y}))),C.added?x+=L.length:b+=L.length}else{if(m)if(L.length<=o.context*2&&k<u.length-2){var F;(F=y).push.apply(F,ii(f(L)))}else{var H,j=Math.min(L.length,o.context);(H=y).push.apply(H,ii(f(L.slice(0,j))));var V={oldStart:m,oldLines:b-m+j,newStart:g,newLines:x-g+j,lines:y};d.push(V),m=0,g=0,y=[]}b+=L.length,x+=L.length}},k=0;k<u.length;k++)E();for(var O=0,S=d;O<S.length;O++)for(var R=S[O],T=0;T<R.lines.length;T++)R.lines[T].endsWith(`
`)?R.lines[T]=R.lines[T].slice(0,-1):(R.lines.splice(T+1,0,"\\ No newline at end of file"),T++);return{oldFileName:i,newFileName:e,oldHeader:n,newHeader:s,hunks:d}}}function Cs(i){if(Array.isArray(i))return i.map(Cs).join(`
`);var e=[];i.oldFileName==i.newFileName&&e.push("Index: "+i.oldFileName),e.push("==================================================================="),e.push("--- "+i.oldFileName+(typeof i.oldHeader=="undefined"?"":"	"+i.oldHeader)),e.push("+++ "+i.newFileName+(typeof i.newHeader=="undefined"?"":"	"+i.newHeader));for(var t=0;t<i.hunks.length;t++){var r=i.hunks[t];r.oldLines===0&&(r.oldStart-=1),r.newLines===0&&(r.newStart-=1),e.push("@@ -"+r.oldStart+","+r.oldLines+" +"+r.newStart+","+r.newLines+" @@"),e.push.apply(e,r.lines)}return e.join(`
`)+`
`}function Dh(i,e,t,r,n,s,o){var a;if(typeof o=="function"&&(o={callback:o}),(a=o)!==null&&a!==void 0&&a.callback){var c=o,u=c.callback;ks(i,e,t,r,n,s,dt(dt({},o),{},{callback:function(d){d?u(Cs(d)):u()}}))}else{var l=ks(i,e,t,r,n,s,o);return l?Cs(l):void 0}}function ew(i,e,t,r,n,s){return Dh(i,i,e,t,r,n,s)}function tw(i){var e=i.endsWith(`
`),t=i.split(`
`).map(function(r){return r+`
`});return e?t.pop():t.push(t.pop().slice(0,-1)),t}function iw(i,e){return i.length!==e.length?!1:La(i,e)}function La(i,e){if(e.length>i.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==i[t])return!1;return!0}function rw(i){var e=Ba(i.lines),t=e.oldLines,r=e.newLines;t!==void 0?i.oldLines=t:delete i.oldLines,r!==void 0?i.newLines=r:delete i.newLines}function nw(i,e,t){i=wh(i,t),e=wh(e,t);var r={};(i.index||e.index)&&(r.index=i.index||e.index),(i.newFileName||e.newFileName)&&(xh(i)?xh(e)?(r.oldFileName=xs(r,i.oldFileName,e.oldFileName),r.newFileName=xs(r,i.newFileName,e.newFileName),r.oldHeader=xs(r,i.oldHeader,e.oldHeader),r.newHeader=xs(r,i.newHeader,e.newHeader)):(r.oldFileName=i.oldFileName,r.newFileName=i.newFileName,r.oldHeader=i.oldHeader,r.newHeader=i.newHeader):(r.oldFileName=e.oldFileName||i.oldFileName,r.newFileName=e.newFileName||i.newFileName,r.oldHeader=e.oldHeader||i.oldHeader,r.newHeader=e.newHeader||i.newHeader)),r.hunks=[];for(var n=0,s=0,o=0,a=0;n<i.hunks.length||s<e.hunks.length;){var l=i.hunks[n]||{oldStart:1/0},c=e.hunks[s]||{oldStart:1/0};if(Sh(l,c))r.hunks.push(Eh(l,o)),n++,a+=l.newLines-l.oldLines;else if(Sh(c,l))r.hunks.push(Eh(c,a)),s++,o+=c.newLines-c.oldLines;else{var u={oldStart:Math.min(l.oldStart,c.oldStart),oldLines:0,newStart:Math.min(l.newStart+o,c.oldStart+a),newLines:0,lines:[]};sw(u,l.oldStart,l.lines,c.oldStart,c.lines),s++,n++,r.hunks.push(u)}}return r}function wh(i,e){if(typeof i=="string"){if(/^@@/m.test(i)||/^Index:/m.test(i))return Ts(i)[0];if(!e)throw new Error("Must provide a base reference or pass in a patch");return ks(void 0,void 0,e,i)}return i}function xh(i){return i.newFileName&&i.newFileName!==i.oldFileName}function xs(i,e,t){return e===t?e:(i.conflict=!0,{mine:e,theirs:t})}function Sh(i,e){return i.oldStart<e.oldStart&&i.oldStart+i.oldLines<e.oldStart}function Eh(i,e){return{oldStart:i.oldStart,oldLines:i.oldLines,newStart:i.newStart+e,newLines:i.newLines,lines:i.lines}}function sw(i,e,t,r,n){var s={offset:e,lines:t,index:0},o={offset:r,lines:n,index:0};for(kh(i,s,o),kh(i,o,s);s.index<s.lines.length&&o.index<o.lines.length;){var a=s.lines[s.index],l=o.lines[o.index];if((a[0]==="-"||a[0]==="+")&&(l[0]==="-"||l[0]==="+"))ow(i,s,o);else if(a[0]==="+"&&l[0]===" "){var c;(c=i.lines).push.apply(c,ii(Vi(s)))}else if(l[0]==="+"&&a[0]===" "){var u;(u=i.lines).push.apply(u,ii(Vi(o)))}else a[0]==="-"&&l[0]===" "?Oh(i,s,o):l[0]==="-"&&a[0]===" "?Oh(i,o,s,!0):a===l?(i.lines.push(a),s.index++,o.index++):Ra(i,Vi(s),Vi(o))}Ch(i,s),Ch(i,o),rw(i)}function ow(i,e,t){var r=Vi(e),n=Vi(t);if(Th(r)&&Th(n)){if(La(r,n)&&Ah(t,r,r.length-n.length)){var s;(s=i.lines).push.apply(s,ii(r));return}else if(La(n,r)&&Ah(e,n,n.length-r.length)){var o;(o=i.lines).push.apply(o,ii(n));return}}else if(iw(r,n)){var a;(a=i.lines).push.apply(a,ii(r));return}Ra(i,r,n)}function Oh(i,e,t,r){var n=Vi(e),s=aw(t,n);if(s.merged){var o;(o=i.lines).push.apply(o,ii(s.merged))}else Ra(i,r?s:n,r?n:s)}function Ra(i,e,t){i.conflict=!0,i.lines.push({conflict:!0,mine:e,theirs:t})}function kh(i,e,t){for(;e.offset<t.offset&&e.index<e.lines.length;){var r=e.lines[e.index++];i.lines.push(r),e.offset++}}function Ch(i,e){for(;e.index<e.lines.length;){var t=e.lines[e.index++];i.lines.push(t)}}function Vi(i){for(var e=[],t=i.lines[i.index][0];i.index<i.lines.length;){var r=i.lines[i.index];if(t==="-"&&r[0]==="+"&&(t="+"),t===r[0])e.push(r),i.index++;else break}return e}function aw(i,e){for(var t=[],r=[],n=0,s=!1,o=!1;n<e.length&&i.index<i.lines.length;){var a=i.lines[i.index],l=e[n];if(l[0]==="+")break;if(s=s||a[0]!==" ",r.push(l),n++,a[0]==="+")for(o=!0;a[0]==="+";)t.push(a),a=i.lines[++i.index];l.substr(1)===a.substr(1)?(t.push(a),i.index++):o=!0}if((e[n]||"")[0]==="+"&&s&&(o=!0),o)return t;for(;n<e.length;)r.push(e[n++]);return{merged:r,changes:t}}function Th(i){return i.reduce(function(e,t){return e&&t[0]==="-"},!0)}function Ah(i,e,t){for(var r=0;r<t;r++){var n=e[e.length-t+r].substr(1);if(i.lines[i.index+r]!==" "+n)return!1}return i.index+=t,!0}function Ba(i){var e=0,t=0;return i.forEach(function(r){if(typeof r!="string"){var n=Ba(r.mine),s=Ba(r.theirs);e!==void 0&&(n.oldLines===s.oldLines?e+=n.oldLines:e=void 0),t!==void 0&&(n.newLines===s.newLines?t+=n.newLines:t=void 0)}else t!==void 0&&(r[0]==="+"||r[0]===" ")&&t++,e!==void 0&&(r[0]==="-"||r[0]===" ")&&e++}),{oldLines:e,newLines:t}}function Fh(i){return Array.isArray(i)?i.map(Fh).reverse():dt(dt({},i),{},{oldFileName:i.newFileName,oldHeader:i.newHeader,newFileName:i.oldFileName,newHeader:i.oldHeader,hunks:i.hunks.map(function(e){return{oldLines:e.newLines,oldStart:e.newStart,newLines:e.oldLines,newStart:e.oldStart,lines:e.lines.map(function(t){return t.startsWith("-")?"+".concat(t.slice(1)):t.startsWith("+")?"-".concat(t.slice(1)):t})}})})}function lw(i){for(var e=[],t,r,n=0;n<i.length;n++)t=i[n],t.added?r=1:t.removed?r=-1:r=0,e.push([r,t.value]);return e}function cw(i){for(var e=[],t=0;t<i.length;t++){var r=i[t];r.added?e.push("<ins>"):r.removed&&e.push("<del>"),e.push(uw(r.value)),r.added?e.push("</ins>"):r.removed&&e.push("</del>")}return e.join("")}function uw(i){var e=i;return e=e.replace(/&/g,"&amp;"),e=e.replace(/</g,"&lt;"),e=e.replace(/>/g,"&gt;"),e=e.replace(/"/g,"&quot;"),e}var gb=$e(Vh()),Sb=$e(Gh()),Eb=$e(ep()),vb=$e(ap()),yb=$e(mp()),bb=$e(Mp()),_b=$e(Jp()),Ob=$e(Gd());var nm=$e(rm(),1),{program:sm,createCommand:N2,createArgument:L2,createOption:B2,CommanderError:R2,InvalidArgumentError:P2,InvalidOptionArgumentError:M2,Command:D2,Argument:F2,Option:q2,Help:j2}=nm.default;var wb=$e(um()),kb=$e(Wm()),xb=$e(Rv());var RA=$e(Fv(),1),df=$e(rf(),1),mf=$e(sf(),1),My=$e(hf(),1),gf=$e(Py(),1);var Dy=My.default;var tI=db.default,iI=mb.default,rI=Pa,nI=gb.default,sI=vb.default,oI=pb(),aI=oI,lI=yb.default,cI=bb.default,uI=_b.default,fI=wb.default,hI=xb.default,pI=Dy,dI=gf.default,mI=df.default,gI=mf.default;0&&(module.exports={HttpsProxyAgent,PNG,SocksProxyAgent,colors,debug,diff,dotenv,getProxyForUrl,jpegjs,lockfile,mime,minimatch,open,program,progress,ws,wsReceiver,wsSender,wsServer,yaml});
/*! Bundled license information:

progress/lib/node-progress.js:
  (*!
   * node-progress
   * Copyright(c) 2011 TJ Holowaychuk <<EMAIL>>
   * MIT Licensed
   *)
*/
