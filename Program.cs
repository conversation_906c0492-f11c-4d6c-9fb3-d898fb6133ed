using Microsoft.Extensions.Configuration;
using GoogleListCreate;
using GoogleListCreate.Models;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Google Maps 景點新增到清單自動化工具啟動中...");

        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();

        var googleMapsSettings = configuration.GetSection("GoogleMaps").Get<GoogleMapsSettings>();
        if (googleMapsSettings == null)
        {
            Console.WriteLine("無法讀取 Google Maps 設定");
            return;
        }

        var places = configuration.GetSection("Places").Get<List<PlaceInfo>>();
        if (places == null || !places.Any())
        {
            Console.WriteLine("無法讀取景點設定");
            return;
        }

        var listsToCreate = configuration.GetSection("ListsToCreate").Get<List<ListInfo>>();
        if (listsToCreate == null)
        {
            listsToCreate = new List<ListInfo>();
        }

        using var automation = new GoogleMapsAutomation(googleMapsSettings);

        try
        {
            Console.WriteLine("正在初始化瀏覽器...");
            await automation.InitializeAsync();

            Console.WriteLine("正在登入 Google 帳號...");
            var loginSuccess = await automation.LoginToGoogleAsync();
            if (!loginSuccess)
            {
                Console.WriteLine("Google 帳號登入失敗，請檢查帳號密碼設定");
                return;
            }

            Console.WriteLine("登入成功！");

            // 處理清單建立（如果有設定且功能啟用）
            if (googleMapsSettings.ListCreation.Enabled && listsToCreate.Any())
            {
                Console.WriteLine($"開始建立 {listsToCreate.Count} 個新清單...");

                int listSuccessCount = 0;
                foreach (var listInfo in listsToCreate)
                {
                    Console.WriteLine($"正在建立清單: {listInfo.Name}");

                    var listSuccess = await automation.CreateNewSavedListAsync(listInfo.Name);
                    if (listSuccess)
                    {
                        listSuccessCount++;
                        Console.WriteLine($"✓ 成功建立清單: {listInfo.Name}");
                    }
                    else
                    {
                        Console.WriteLine($"✗ 建立清單失敗: {listInfo.Name}");
                    }

                    // 在清單建立之間稍作等待
                    await Task.Delay(2000);
                }

                Console.WriteLine($"清單建立完成！成功: {listSuccessCount}/{listsToCreate.Count}");
                Console.WriteLine();
            }

            // 處理景點新增
            Console.WriteLine("開始處理景點...");

            int successCount = 0;
            int totalCount = places.Count;

            foreach (var place in places)
            {
                Console.WriteLine($"正在處理景點: {place.Name}");

                var success = await automation.AddPlaceToListAsync(place);
                if (success)
                {
                    successCount++;
                    Console.WriteLine($"✓ 成功新增 {place.Name} 到 {place.ListName}");
                }
                else
                {
                    Console.WriteLine($"✗ 新增 {place.Name} 失敗");
                }

                await Task.Delay(2000);
            }

            Console.WriteLine($"\n處理完成！成功: {successCount}/{totalCount}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"執行過程中發生錯誤: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }

        if (Environment.UserInteractive)
        {
            Console.WriteLine("按任意鍵結束程式...");
            Console.ReadKey();
        }
    }
}