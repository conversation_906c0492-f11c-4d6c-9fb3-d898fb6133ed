namespace GoogleListCreate.Models;

public class PlaceInfo
{
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Description { get; set; }
    public string ListName { get; set; } = string.Empty;
}

/// <summary>
/// 表示要建立的清單資訊
/// </summary>
public class ListInfo
{
    /// <summary>
    /// 清單名稱
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 清單描述（選填）
    /// </summary>
    public string? Description { get; set; }
}

public enum BrowserChoice
{
    SystemChrome,
    PlaywrightChromium,
    PlaywrightFirefox,
    PlaywrightWebkit
}

public class GoogleMapsSettings
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public bool Headless { get; set; } = false;
    public BrowserChoice Browser { get; set; } = BrowserChoice.SystemChrome;
    public string? CustomChromePath { get; set; }

    /// <summary>
    /// 新清單建立功能的相關設定
    /// </summary>
    public ListCreationSettings ListCreation { get; set; } = new();
}

/// <summary>
/// 清單建立功能的設定選項
/// </summary>
public class ListCreationSettings
{
    /// <summary>
    /// 是否啟用新清單建立功能
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 點擊「已儲存」按鈕後的等待時間（毫秒）
    /// </summary>
    public int SavedButtonClickDelayMs { get; set; } = 3000;

    /// <summary>
    /// 點擊「新增清單」按鈕後的等待時間（毫秒）
    /// </summary>
    public int NewListButtonClickDelayMs { get; set; } = 2000;

    /// <summary>
    /// 輸入清單名稱時每個字符的間隔時間（毫秒）
    /// </summary>
    public int TypingDelayMs { get; set; } = 50;

    /// <summary>
    /// 是否在每個步驟後自動截圖（用於除錯）
    /// </summary>
    public bool EnableDebugScreenshots { get; set; } = true;

    /// <summary>
    /// 元素定位的最大重試次數
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// 重試間隔時間（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;
}