namespace GoogleListCreate.Models;

public class PlaceInfo
{
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Description { get; set; }
    public string ListName { get; set; } = string.Empty;
}

public enum BrowserChoice
{
    SystemChrome,
    PlaywrightChromium,
    PlaywrightFirefox,
    PlaywrightWebkit
}

public class GoogleMapsSettings
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public bool Headless { get; set; } = false;
    public BrowserChoice Browser { get; set; } = BrowserChoice.SystemChrome;
    public string? CustomChromePath { get; set; }
}