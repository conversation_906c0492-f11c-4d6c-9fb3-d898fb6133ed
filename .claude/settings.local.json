{"permissions": {"allow": ["Bash(dotnet add package:*)", "Bash(pwsh:*)", "Bash(dotnet build)", "Bash(dotnet run:*)", "Bash(dotnet exec:*)", "Bash(dotnet tool install:*)", "<PERSON><PERSON>(playwright install:*)", "<PERSON><PERSON>(dotnet playwright install:*)", "Bash(brew install:*)", "Bash(dotnet build:*)", "Read(//Applications/**)", "Bash(brew list:*)", "<PERSON><PERSON>(timeout:*)"], "deny": [], "ask": [], "additionalDirectories": ["/Applications/Chromium.app/Contents/MacOS"]}}