# Google Maps 景點自動新增到清單工具

使用 Playwright 和 .NET 9 C# 自動將景點新增到 Google Maps 的已儲存清單中。

## 功能特色

- 自動登入 Google 帳號
- **🆕 自動建立 Google Maps 已儲存清單**
- 搜尋指定景點
- 將景點新增到指定的清單中
- 支援建立新清單或新增到現有清單
- 批次處理多個景點和清單
- 完整的錯誤處理和截圖記錄
- 可配置的等待時間和重試機制
- 支援除錯模式和測試環境

## 系統需求

- .NET 9.0 或以上版本
- macOS, Windows, 或 Linux
- Chrome/Chromium 瀏覽器

## 瀏覽器選擇與安裝

### 推薦方案：使用系統瀏覽器（無需額外安裝）

**最簡單的方式**：直接使用系統已安裝的 Google Chrome 或 Chromium
- ✅ 無需額外安裝任何瀏覽器
- ✅ 使用您熟悉的瀏覽器環境
- ✅ 設定檔中預設已配置為 `"Browser": "SystemChrome"`

### 其他瀏覽器選項

您可以在 `appsettings.json` 中修改 `Browser` 設定：

1. **SystemChrome** (預設推薦)
   ```json
   "Browser": "SystemChrome"
   ```
   - 使用系統已安裝的 Chrome
   - 無需額外安裝

2. **PlaywrightChromium** (需安裝)
   ```json
   "Browser": "PlaywrightChromium"  
   ```
   - 需要安裝：`pwsh bin/Debug/net9.0/playwright.ps1 install chromium`

3. **PlaywrightFirefox** (需安裝)
   ```json
   "Browser": "PlaywrightFirefox"
   ```
   - 需要安裝：`pwsh bin/Debug/net9.0/playwright.ps1 install firefox`

4. **PlaywrightWebkit** (需安裝)
   ```json
   "Browser": "PlaywrightWebkit"
   ```
   - 需要安裝：`pwsh bin/Debug/net9.0/playwright.ps1 install webkit`

### 安裝 Playwright 瀏覽器的替代方案

**如果您需要使用 Playwright 瀏覽器，但沒有 PowerShell：**

1. **安裝 PowerShell** (推薦)
   ```bash
   # macOS
   brew install powershell
   
   # 然後安裝瀏覽器
   pwsh bin/Debug/net9.0/playwright.ps1 install chromium
   ```

2. **使用 Node.js 版本的 Playwright**
   ```bash
   npm install -g playwright
   playwright install chromium
   ```

3. **手動下載瀏覽器**
   - 程式會顯示所需的瀏覽器路徑
   - 您可以手動下載並放置到指定位置

4. **指定自訂 Chrome 路徑**
   ```json
   "CustomChromePath": "/path/to/your/chrome"
   ```

### 2. 設定 Google 帳號資訊

編輯 `appsettings.json` 檔案：

```json
{
  "GoogleMaps": {
    "Email": "<EMAIL>",
    "Password": "your-password",
    "TimeoutSeconds": 30,
    "Headless": false,
    "ListCreation": {
      "Enabled": true,
      "SavedButtonClickDelayMs": 3000,
      "NewListButtonClickDelayMs": 2000,
      "TypingDelayMs": 50,
      "EnableDebugScreenshots": true,
      "MaxRetryAttempts": 3,
      "RetryDelayMs": 1000
    }
  },
  "Places": [
    {
      "Name": "台北101",
      "Address": "台北市信義區信義路五段7號",
      "Description": "台北著名地標",
      "ListName": "我的最愛景點"
    }
  ],
  "ListsToCreate": [
    {
      "Name": "我的旅遊清單",
      "Description": "自動建立的旅遊景點清單"
    },
    {
      "Name": "美食推薦",
      "Description": "收集各地美食的清單"
    }
  ]
}
```

**重要提醒：**
- 請將您的實際 Google 帳號和密碼填入設定檔
- 建議使用應用程式專用密碼而非主要密碼
- 如果啟用了兩步驟驗證，請設定應用程式密碼

### 3. 新增要處理的景點

在 `appsettings.json` 的 `Places` 陣列中新增景點：

```json
"Places": [
  {
    "Name": "景點名稱",
    "Address": "可選：景點地址",
    "Description": "可選：景點描述",
    "ListName": "要新增到的清單名稱"
  }
]
```

## 使用方式

### 建置專案
```bash
dotnet build
```

### 執行程式
```bash
dotnet run
```

## 程式執行流程

1. **初始化瀏覽器** - 啟動 Chromium 瀏覽器
2. **登入 Google 帳號** - 自動填寫帳號密碼並登入
3. **🆕 建立新清單**（如果有設定）- 自動建立 Google Maps 已儲存清單：
   - 導航到 Google Maps 頁面
   - 點擊左側「已儲存」按鈕
   - 在右側區塊中點擊「新增清單」按鈕
   - 輸入清單名稱並確認建立
4. **處理景點清單** - 逐一處理設定檔中的每個景點：
   - 在 Google Maps 中搜尋景點
   - 點擊「儲存」按鈕
   - 選擇或建立指定的清單
   - 將景點新增到清單中
5. **完成報告** - 顯示成功和失敗的統計

## 設定選項

### GoogleMaps 設定

- `Email`: Google 帳號電子郵件
- `Password`: Google 帳號密碼（建議使用應用程式密碼）
- `TimeoutSeconds`: 頁面載入等待時間（預設 30 秒）
- `Headless`: 是否在背景執行（false=顯示瀏覽器視窗，true=背景執行）
- `Browser`: 瀏覽器類型選擇
  - `SystemChrome`: 使用系統 Chrome（預設，推薦）
  - `PlaywrightChromium`: 使用 Playwright Chromium
  - `PlaywrightFirefox`: 使用 Playwright Firefox  
  - `PlaywrightWebkit`: 使用 Playwright WebKit
- `CustomChromePath`: 自訂 Chrome 路徑（選填）

### Places 設定

- `Name`: 景點名稱（必填）
- `Address`: 景點地址（選填，有助於精確搜尋）
- `Description`: 景點描述（選填）
- `ListName`: 目標清單名稱（必填）

### 🆕 ListsToCreate 設定

- `Name`: 要建立的清單名稱（必填）
- `Description`: 清單描述（選填）

### 🆕 ListCreation 設定

- `Enabled`: 是否啟用新清單建立功能（預設：true）
- `SavedButtonClickDelayMs`: 點擊「已儲存」按鈕後的等待時間（預設：3000ms）
- `NewListButtonClickDelayMs`: 點擊「新增清單」按鈕後的等待時間（預設：2000ms）
- `TypingDelayMs`: 輸入清單名稱時每個字符的間隔時間（預設：50ms）
- `EnableDebugScreenshots`: 是否在每個步驟後自動截圖（預設：true）
- `MaxRetryAttempts`: 元素定位的最大重試次數（預設：3）
- `RetryDelayMs`: 重試間隔時間（預設：1000ms）

## 故障排除

### 瀏覽器相關問題

如果遇到「Executable doesn't exist」錯誤：
1. 確認已正確安裝 Playwright 瀏覽器
2. 嘗試重新執行安裝命令
3. 檢查瀏覽器檔案是否存在於指定路徑

### 登入問題

1. 確認 Google 帳號和密碼正確
2. 如果啟用兩步驟驗證，請使用應用程式專用密碼
3. 檢查帳號是否被 Google 暫時鎖定

### 景點搜尋問題

1. 確保景點名稱正確
2. 提供詳細的地址資訊有助於搜尋準確性
3. 檢查網路連線狀態

## 安全性注意事項

- 不要將包含真實密碼的 `appsettings.json` 提交到版本控制系統
- 建議使用環境變數或其他安全方式儲存敏感資訊
- 定期更新 Playwright 和相關套件以獲得安全性更新

## 開發資訊

- **語言**: C# (.NET 9)
- **自動化框架**: Microsoft Playwright
- **設定管理**: Microsoft.Extensions.Configuration

## 專案結構

```text
google-list-create/
├── Models/
│   └── PlaceInfo.cs                    # 資料模型（PlaceInfo, ListInfo, GoogleMapsSettings）
├── GoogleMapsAutomation.cs             # 主要自動化邏輯
├── Program.cs                          # 程式進入點
├── appsettings.json                    # 設定檔
├── GoogleListCreate.Tests/             # 🆕 測試專案
│   ├── Models/
│   │   └── PlaceInfoTests.cs           # 模型單元測試
│   ├── GoogleMapsAutomationTests.cs    # 自動化類別測試
│   ├── appsettings.test.json           # 測試設定檔
│   └── GoogleListCreate.Tests.csproj   # 測試專案檔
└── README.md                           # 專案說明
```

## 🆕 測試

### 執行測試

```bash
# 執行所有測試
dotnet test

# 執行特定測試類別
dotnet test --filter "ClassName=PlaceInfoTests"

# 執行測試並產生覆蓋率報告
dotnet test --collect:"XPlat Code Coverage"
```

### 測試類型

1. **單元測試**: 測試模型類別和基本功能
2. **整合測試**: 測試完整的自動化流程（需要實際的 Google 帳號）
3. **配置測試**: 驗證設定檔載入和解析

**注意**: 整合測試預設為跳過狀態，因為需要實際的 Google 帳號和網路連線。在本地開發時可以移除 `Skip` 屬性來執行。

## 授權

此專案僅供學習和個人使用。請遵守 Google Maps 的使用條款。