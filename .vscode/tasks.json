{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/google-list-create.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "run", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/google-list-create.csproj"], "problemMatcher": "$msCompile", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "dependsOn": "build"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/google-list-create.csproj"], "problemMatcher": "$msCompile", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "rebuild", "dependsOrder": "sequence", "dependsOn": ["clean", "build"], "group": {"kind": "build", "isDefault": false}}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/google-list-create.csproj"], "problemMatcher": "$msCompile", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}