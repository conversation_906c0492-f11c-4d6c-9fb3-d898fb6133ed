{"dotnet.defaultSolution": "google-list-create.csproj", "files.exclude": {"**/bin": true, "**/obj": true}, "omnisharp.enableRoslynAnalyzers": true, "omnisharp.enableEditorConfigSupport": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.codeLens.enableReferencesCodeLens": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.json": "jsonc"}}