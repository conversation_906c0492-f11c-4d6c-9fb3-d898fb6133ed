{"version": "0.2.0", "configurations": [{"name": "Launch Google List Create", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net9.0/google-list-create.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false, "preLaunchTask": "build", "env": {"DOTNET_ENVIRONMENT": "Development"}}, {"name": "Launch Google List Create (External Console)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net9.0/google-list-create.dll", "args": [], "cwd": "${workspaceFolder}", "console": "externalTerminal", "stopAtEntry": false, "requireExactSource": false, "preLaunchTask": "build", "env": {"DOTNET_ENVIRONMENT": "Development"}}, {"name": "Launch Google List Create (Headless Mode)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net9.0/google-list-create.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false, "preLaunchTask": "build", "env": {"DOTNET_ENVIRONMENT": "Development", "HEADLESS_MODE": "true"}}, {"name": "Attach to Process", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}]}